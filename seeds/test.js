/* eslint-disable max-len */
/* eslint-disable @hapi/for-loop */
'use strict';

const { ManagementClient } = require('auth0');
const Shoot = require('../lib/models/Shoot');
const { shuffle } = require('../lib/helpers/utils');
const { packages, services, tableWithoutIdNames } = require('./_1base');
const { v4: uuidv4 } = require('uuid');
const Path = require('path');
const Fs = require('fs');

const numUsers = {
    admins: 1,
    photographers: 125,
    clients: 100,
    editors: 1
};

const shootStatusQuantities = {
    scheduled: 50,
    assigned: 50,
    confirmed: 50,
    uploaded: 50,
    ready: 100,
    completed: 3000,
    canceled: 300,
    redeemable: 250
};

const cities = ['Cairo', 'Riyadh', 'Dubai', 'Beirut', 'Amman', 'Doha', 'Marrakesh', 'Muscat', 'Kuwait City', 'Istanbul'];
const countries = ['EG', 'SA', 'AE', 'LB', 'JO', 'QA', 'MA', 'OM', 'KW', 'TR'];
const industries = ['Technology', 'Healthcare', 'Finance', 'Education', 'Retail', 'Hospitality', 'Manufacturing', 'Construction', 'Transportation', 'Energy'];
const companyNames = ['TechCorp', 'HealthPlus', 'FinanceInc', 'EduWorld', 'RetailHub', 'HospitalityNetwork', 'ManufactureIt', 'BuildRight', 'TransportEase', 'EnergySolutions'];

const firstNamesPath = Path.join(__dirname, 'first-names.json');
const firstNames = JSON.parse(Fs.readFileSync(firstNamesPath, 'utf8'));

const lastNamesPath = Path.join(__dirname, 'last-names.json');
const lastNames = JSON.parse(Fs.readFileSync(lastNamesPath, 'utf8'));

const getRandomItem = (array) => array[Math.floor(Math.random() * array.length)];
const getRandomInt = (min, max) => Math.floor(Math.random() * (max - min + 1)) + min;

const data = {
    payment_details: [],
    photographers: [],
    editors: [],
    clients: [],
    users: [],
    orders: [],
    shoots: [],
    shoots_services: [],
    photographers_services: []
};

for (let i = 0; i < numUsers.admins; ++i) {
    data.users.push({
        id: 1 + data.users.length,
        name: `Test admin ${i + 1}`,
        email: `test.admin${i + 1}@test.dev`,
        phone: '**********',
        photographer_id: null,
        client_id: null,
        editor_id: null,
        status: 'active'
    });
}

for (let i = 0; i < numUsers.editors; ++i) {
    data.editors.push({ id: i + 1 });
    data.users.push({
        id: 1 + data.users.length,
        name: `Test editor ${i + 1}`,
        email: `test.editor${i + 1}@test.dev`,
        phone: '**********',
        photographer_id: null,
        client_id: null,
        editor_id: i + 1,
        status: 'active'
    });
}

// Modify the photographer creation loop
for (let i = 0; i < numUsers.photographers; ++i) {
    const paymentDetailId = 1 + data.payment_details.length;
    const photographerId = 1 + data.photographers.length;

    const randomCity = getRandomItem(cities);
    const randomFirstName = getRandomItem(firstNames);
    const randomLastName = getRandomItem(lastNames);
    const photographerName = `${randomFirstName} ${randomLastName}`;

    data.payment_details.push({
        id: paymentDetailId,
        type: 'test',
        details: {},
        status: 'none'
    });

    data.photographers.push({
        id: photographerId,
        birthdate: new Date(),
        english_level: 'B2',
        arabic_level: 'A1',
        main_location: randomCity,
        is_pro: Math.random() < 0.5,
        details: {},
        payment_details_id: paymentDetailId,
        status: 'none'
    });

    const numServices = Math.floor(2 + Math.random() * 2);
    for (const { id: service_id } of shuffle(services).slice(0, numServices)) {
        data.photographers_services.push({
            photographer_id: photographerId,
            service_id,
            rate: Math.floor(Math.random() * 10) + 1
        });
    }

    data.users.push({
        id: 1 + data.users.length,
        name: photographerName,
        email: `${randomFirstName.toLowerCase()}.${randomLastName.toLowerCase()}@test.dev`,
        phone: '**********',
        photographer_id: photographerId,
        client_id: null,
        editor_id: null,
        status: 'active'
    });
}

for (let i = 0; i < numUsers.clients; ++i) {
    const paymentDetailId = 1 + data.payment_details.length;
    const clientId = 1 + data.clients.length;

    const randomCity = getRandomItem(cities);
    const randomCountry = getRandomItem(countries);
    const randomIndustry = getRandomItem(industries);
    const randomCompanyName = getRandomItem(companyNames);
    const randomFirstName = getRandomItem(firstNames);
    const randomLastName = getRandomItem(lastNames);
    const clientName = `${randomFirstName} ${randomLastName}`;

    const client = {
        id: clientId,
        company_name: randomCompanyName,
        industry: randomIndustry,
        country: randomCountry,
        contact_name: clientName,
        contact_email: `${getRandomItem(firstNames).toLowerCase()}.${getRandomItem(lastNames).toLowerCase()}@test.dev`,
        contact_phone: '**********',
        main_location: randomCity,
        size: 10,
        payment_details_id: paymentDetailId,
        status: 'none',
        registration_guid: uuidv4()
    };

    data.payment_details.push({
        id: paymentDetailId,
        type: 'test',
        details: {},
        status: 'none'
    });

    data.clients.push(client);

    data.users.push({
        id: 1 + data.users.length,
        name: clientName,
        email: `${randomFirstName.toLowerCase()}.${randomLastName.toLowerCase()}@test.dev`,
        phone: '**********',
        photographer_id: null,
        client_id: clientId,
        editor_id: null,
        status: 'active'
    });

    // Create subclients for 30% of the main clients
    if (Math.random() < 0.3) {
        const numSubClients = getRandomInt(1, 5); // Random number of subclients between 1 and 5
        for (let j = 0; j < numSubClients; ++j) {
            const firstName = getRandomItem(firstNames);
            const lastName = getRandomItem(lastNames);
            const subClientId = 1 + data.clients.length;
            const subClient = {
                id: subClientId,
                company_name: client.company_name,
                industry: client.industry,
                country: client.country,
                contact_name: client.contact_name,
                contact_email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@test.dev`,
                contact_phone: '**********',
                main_location: client.main_location,
                size: client.size,
                payment_details_id: paymentDetailId,
                status: 'none',
                parent_client_id: client.id
            };

            data.clients.push(subClient);

            data.users.push({
                id: 1 + data.users.length,
                name: `Test subclient ${i + 1}.${j + 1}`,
                email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@test.dev`,
                phone: '**********',
                photographer_id: null,
                client_id: subClientId,
                editor_id: null,
                status: 'active'
            });
        }
    }
}

const clientIds = data.clients.filter((({ parent_client_id }) => !parent_client_id)).map(({ id }) => id);
const subClients = data.clients.filter((({ id }) => !clientIds.includes(id))).map(({ id, parent_client_id }) => ({ id, parent_client_id }));
const photographerIds = data.photographers.map(({ id }) => id);

const createRandomShoot = ({ client_id, photographer_id = null, status, numServices = null, subClient = null }) => {

    const randomCity = cities[Math.floor(Math.random() * cities.length)];
    const randomCountry = countries[Math.floor(Math.random() * countries.length)];

    numServices = numServices || 1; // Ensure numServices is set to 1 if null or undefined

    // Randomly pick numServices services and check for available packages
    const chosenServices = shuffle(services).slice(0, numServices);
    const availableServices = chosenServices.filter((service) => {

        const packagesForService = packages.filter((pack) => pack.service_id === service.id);
        return packagesForService.length > 0;
    });

    // Only proceed if there are available services with packages
    if (availableServices.length > 0) {
        const order = {
            id: data.orders.length + 1,
            client_id: (status === 'redeemable') ? subClient.parent_client_id : client_id,
            purchase_type: 'single',
            payment_details: {},
            price: 123,
            billing_address: {
                city: randomCity,
                country: randomCountry,
                line1: 'line1',
                line2: 'line2',
                postal_code: 'postal_code',
                state: 'state'
            },
            payment_confirmed: true,
            payment_date: new Date(),
            status: 'none'
        };

        const shoot = {
            id: data.shoots.length + 1,
            photographer_id,
            order_id: order.id,
            datetime: (status === 'redeemable') ? null : new Date(),
            address: {
                city: randomCity,
                country: randomCountry,
                line1: 'line1',
                postal_code: '12345',
                state: 'state',
                formatted_address: 'formatted_address'
            },
            latitude: 25.276987,
            longitude: 55.296249,
            picture_number: 12,
            price: 123,
            photographer_revenue: 0,
            redeemable_total: (status === 'redeemable') ? '-1' : null,
            redeemable_counter: (status === 'redeemable') ? 1 : null,
            duration: 1,
            scheduled: !(status === Shoot.statuses.toSchedule || status === Shoot.statuses.redeemable),
            status,
            consumer_client_id: (status === 'redeemable') ? subClient.parent_client_id : client_id
        };

        // Create shoot services only if packages are available
        for (const service of availableServices) {
            const packagesForService = packages.filter((pack) => pack.service_id === service.id);
            const pack = packagesForService[Math.floor(Math.random() * packagesForService.length)];
            const shootService = {
                shoot_id: shoot.id,
                service_id: service.id,
                picture_number: 12,
                package: pack
            };
            data.shoots_services.push(shootService);
        }

        data.orders.push(order);
        data.shoots.push(shoot);
    }

};

const createRandomShootsForStatus = (status, count) => {

    for (let i = 0; i < count; ++i) {
        const client_id = clientIds[Math.floor(Math.random() * clientIds.length)];
        const photographer_id = status === 'scheduled' ? null : photographerIds[Math.floor(Math.random() * photographerIds.length)];
        const subClient = subClients[Math.floor(Math.random() * subClients.length)];
        createRandomShoot({ client_id, photographer_id, status, subClient });
    }
};

// Generate shoots based on specified status quantities
for (const status in shootStatusQuantities) {
    createRandomShootsForStatus(status, shootStatusQuantities[status]);
}

const BATCH_SIZE = 5;
const RATE_LIMIT_DELAY = 1000; // 1 second delay between batches

const processInBatches = async (items, processFn) => {

    for (let i = 0; i < items.length; i += BATCH_SIZE) {
        const batch = items.slice(i, i + BATCH_SIZE);
        await Promise.all(batch.map(processFn));
        if (i + BATCH_SIZE < items.length) {
            await new Promise((resolve) => setTimeout(resolve, RATE_LIMIT_DELAY));
        }
    }
};

const fetchAllAuth0Users = async (auth0Client) => {

    console.log('Fetching all Auth0 users...');
    let allUsers = [];
    let page = 0;
    let hasNextPage = true;

    while (hasNextPage) {
        const users = await auth0Client.getUsers({ page, per_page: 100, include_totals: true });
        allUsers = allUsers.concat(users.users);
        hasNextPage = users.start + users.length < users.total;
        page++;
    }

    return allUsers;
};

const deleteAuth0Users = async (auth0Client, users) => {

    console.log(`Deleting ${users.length} Auth0 users...`);
    await processInBatches(users, async (user) => {

        try {
            await auth0Client.deleteUser({ id: user.user_id });
            console.log(`Deleted Auth0 user: ${user.email}`);
        }
        catch (error) {
            console.error(`Failed to delete Auth0 user ${user.email}:`, error);
        }
    });
};

const deleteLocalData = async (knex) => {

    console.log('Deleting all local users, clients, photographers, and related data...');

    // Delete in reverse order of dependencies
    await knex('photographers_services').del();
    await knex('shoots_services').del();
    await knex('shoots').del();
    await knex('orders').del();
    await knex('photographers').del();
    await knex('clients').del();
    await knex('editors').del();
    await knex('payment_details').del();
    await knex('users').del();

    console.log('All related local data deleted.');
};

const createAuth0Users = async (auth0Client, users) => {

    console.log(`Creating ${users.length} Auth0 users...`);
    let createdCount = 0;

    for (const user of users) {

        let retries = 3;
        while (retries > 0) {
            try {
                await auth0Client.createUser({
                    email: user.email,
                    password: 'S33der_pw!',
                    name: user.name,
                    connection: 'Username-Password-Authentication',
                    email_verified: true
                });
                console.log(`Created Auth0 user: ${user.email}`);
                createdCount++;
                break; // Success, exit retry loop
            }
            catch (error) {
                if (error.statusCode === 429) { // Rate limit error
                    console.log(`Rate limit hit. Waiting before retry...`);
                    await new Promise((resolve) => setTimeout(resolve, 60000)); // Wait for 1 minute
                    retries--;
                }
                else {
                    console.error(`Failed to create Auth0 user ${user.email}:`, error);
                    break; // Non-rate limit error, exit retry loop
                }
            }
        }

        // Save progress every 10 users
        if (createdCount % 10 === 0) {
            console.log(`Progress: Created ${createdCount}/${users.length} Auth0 users`);
            // You could save lastCreatedEmail to a file or database here
        }
    }

    console.log(`Finished creating Auth0 users. Total created: ${createdCount}`);
};

exports.seed = async (knex) => {

    console.info('\n\n[INFO]: running test seeder');

    const seedTable = async ({ tableName, elements }) => {

        console.info(`\n[INFO]: inserting ${elements.length} elements in table ${tableName}`);
        await knex.batchInsert(tableName, elements, 100); // 100 is the batch size
    };

    let auth0Client;

    try {
        if (process.env.AUTH_PROVIDER_NAME === 'auth0') {
            auth0Client = new ManagementClient({
                domain: process.env.AUTH_PROVIDER_CONFIG_DOMAIN,
                clientId: process.env.AUTH_PROVIDER_CLIENT_ID,
                clientSecret: process.env.AUTH_PROVIDER_CLIENT_SECRET,
                scope: 'read:users update:users create:users delete:users'
            });
            // Step 1: Fetch all Auth0 users
            const allAuth0Users = await fetchAllAuth0Users(auth0Client);

            // Step 2: Delete all Auth0 users
            await deleteAuth0Users(auth0Client, allAuth0Users);
        }

        // Step 3: Delete local users, clients, photographers, and all related data
        await deleteLocalData(knex);

        // Step 4: Recreate the local data (including users)
        for (const [tableName, elements] of Object.entries(data)) {
            await seedTable({ tableName, elements });

            // Update serial id counters
            if (!tableWithoutIdNames.includes(tableName)) {
                const rawQuery = `SELECT setval('${tableName}_id_seq', (SELECT MAX(id) from "${tableName}"))`;
                console.info(`[INFO]: updating serial id counter for table ${tableName} (using raw query ${rawQuery})`);
                const result = await knex.raw(rawQuery);
                console.info(`[INFO]: serial id counter updated to ${result.rows[0].setval}`);
            }
        }

        // Step 5: Create new Auth0 users
        if (process.env.AUTH_PROVIDER_NAME === 'auth0') {
            auth0Client = new ManagementClient({
                domain: process.env.AUTH_PROVIDER_CONFIG_DOMAIN,
                clientId: process.env.AUTH_PROVIDER_CLIENT_ID,
                clientSecret: process.env.AUTH_PROVIDER_CLIENT_SECRET,
                scope: 'read:users update:users create:users delete:users'
            });

            await createAuth0Users(auth0Client, data.users);
        }

        console.log('Data synchronization completed successfully.');
    }
    catch (error) {
        console.error('Error during data synchronization:', error);
    }
};
