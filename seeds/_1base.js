/* eslint-disable max-len */
'use strict';

const Fs = require('fs');
const Path = require('path');

const data = {};

// Read and parse the packages.json file

const packagesPath = Path.join(__dirname, 'packages.json');
data.packages = JSON.parse(Fs.readFileSync(packagesPath, 'utf8'));

const servicesPath = Path.join(__dirname, 'services.json');
data.services = JSON.parse(Fs.readFileSync(servicesPath, 'utf8'));

data.purposes = [
    {
        id: 1,
        name: 'Business'
    }
];

data.notification_settings = [
    //parent client
    {
        id: 2,
        parent_id: null,
        key: 'client.PendingShoot',
        description: 'Pending shoot',
        value: true,
        user_type: 'client'
    },
    {
        id: 3,
        parent_id: null,
        key: 'client.ConfirmedShoot',
        description: 'Confirmed shoot',
        value: true,
        user_type: 'client'
    },
    {
        id: 4,
        parent_id: null,
        key: 'client.ShootReminder',
        description: 'Shoot reminder',
        value: true,
        user_type: 'client'
    },
    {
        id: 5,
        parent_id: null,
        key: 'client.PostShootFeedbacks',
        description: 'Post-shoot feedbacks',
        value: true,
        user_type: 'client'
    },
    {
        id: 6,
        parent_id: null,
        key: 'client.ShootReadyForUpload',
        description: 'Shoot ready for download',
        value: true,
        user_type: 'client'
    },

    {
        id: 8,
        parent_id: null,
        key: 'client.CancelledShoot',
        description: 'Cancelled shoot',
        value: true,
        user_type: 'client'
    },
    //parent ph
    {
        id: 10,
        parent_id: null,
        key: 'photographer.PendingShoot',
        description: 'Pending shoot',
        value: true,
        user_type: 'photographer'
    },
    {
        id: 11,
        parent_id: null,
        key: 'photographer.ConfirmedShoot',
        description: 'Confirmed shoot',
        value: true,
        user_type: 'photographer'
    },
    {
        id: 12,
        parent_id: null,
        key: 'photographer.ShootReminder',
        description: 'Shoot reminder',
        value: true,
        user_type: 'photographer'
    },
    {
        id: 13,
        parent_id: null,
        key: 'photographer.PostShootFeedbacks',
        description: 'Post-shoot feedbacks',
        value: true,
        user_type: 'photographer'
    },
    {
        id: 14,
        parent_id: null,
        key: 'photographer.ShootReadyForUpload',
        description: 'Shoot ready for upload',
        value: true,
        user_type: 'photographer'
    },
    {
        id: 16,
        parent_id: null,
        key: 'photographer.CancelledShoot',
        description: 'Cancelled shoot',
        value: true,
        user_type: 'photographer'
    },
    //parent admin
    {
        id: 17,
        parent_id: null,
        key: 'admin.AccountRegistration',
        description: 'Account registration',
        value: true,
        user_type: 'admin'
    },
    {
        id: 18,
        parent_id: null,
        key: 'admin.PendingShoot',
        description: 'Pending shoot',
        value: true,
        user_type: 'admin'
    },
    {
        id: 19,
        parent_id: null,
        key: 'admin.ConfirmedShoot',
        description: 'Confirmed shoot',
        value: true,
        user_type: 'admin'
    },
    {
        id: 20,
        parent_id: null,
        key: 'admin.ShootReminder',
        description: 'Shoot reminder',
        value: true,
        user_type: 'admin'
    },
    {
        id: 24,
        parent_id: null,
        key: 'admin.CancelledShoot',
        description: 'Cancelled shoot',
        value: true,
        user_type: 'admin'
    },
    //children client
    {
        id: 25,
        parent_id: 2,
        key: 'shootCreated',
        description: 'New shoot created',
        value: true,
        user_type: 'client'
    },
    {
        id: 26,
        parent_id: 3,
        key: 'clientShootConfirmed',
        description: 'Shoot is confirmed',
        value: true,
        user_type: 'client'
    },
    {
        id: 27,
        parent_id: 4,
        key: 'clientShootReminder',
        description: 'Shoot reminder',
        value: true,
        user_type: 'client'
    },
    {
        id: 28,
        parent_id: 5,
        key: 'reviewShoot',
        description: 'Rate experience reminder',
        value: true,
        user_type: 'client'
    },
    {
        id: 29,
        parent_id: 6,
        key: 'clientContentReady',
        description: 'Photo ready',
        value: true,
        user_type: 'client'
    },
    {
        id: 30,
        parent_id: 8,
        key: 'informClientCanceledByFlashy',
        description: 'Shoot cancelled by flashy',
        value: true,
        user_type: 'client'
    },
    //children photographer
    {
        id: 31,
        parent_id: 10,
        key: 'phShootAssigned',
        description: 'New shoot assigned to photographer',
        value: true,
        user_type: 'photographer'
    },
    {
        id: 32,
        parent_id: 11,
        key: 'phShootConfirmed',
        description: 'Shoot is confirmed',
        value: true,
        user_type: 'photographer'
    },
    {
        id: 33,
        parent_id: 12,
        key: 'phShootReminder',
        description: 'Shoot reminder',
        value: true,
        user_type: 'photographer'
    },
    {
        id: 34,
        parent_id: 14,
        key: 'phShootUploadPhotoReminder',
        description: 'Upload photo shoot reminder',
        value: true,
        user_type: 'photographer'
    },
    {
        id: 35,
        parent_id: 13,
        key: 'rateUpPh',
        description: 'Rate experience reminder',
        value: true,
        user_type: 'photographer'
    },
    {
        id: 36,
        parent_id: 16,
        key: 'informPhOfShootCanceled',
        description: 'Shoot cancelled by flashy',
        value: true,
        user_type: 'photographer'
    },
    {
        id: 37,
        parent_id: 16,
        key: 'informPhOfSuccessfullShootCancellationByHimself',
        description: 'Shoot cancelled by photographer',
        value: true,
        user_type: 'photographer'
    },
    //children admin
    {
        id: 38,
        parent_id: 18,
        key: 'newShootRequest',
        description: 'New shoot request',
        value: true,
        user_type: 'admin'
    },
    {
        id: 39,
        parent_id: 19,
        key: 'adminShootConfirmed',
        description: 'Shoot is confirmed',
        value: true,
        user_type: 'admin'
    },
    {
        id: 40,
        parent_id: 20,
        key: 'rawPhotosUploaded',
        description: 'Raw photos uploaded',
        value: true,
        user_type: 'admin'
    },
    {
        id: 41,
        parent_id: 24,
        key: 'informAdminOfPhShootCancellation',
        description: 'Shoot cancelled by photographer',
        value: true,
        user_type: 'admin'
    },
    {
        id: 42,
        parent_id: 24,
        key: 'informFlashyShootCanceldByAdmin',
        description: 'Shoot cancelled by flashy',
        value: true,
        user_type: 'admin'
    },
    {
        id: 43,
        parent_id: 17,
        key: 'newCustomerRegistered',
        description: 'New customer registered',
        value: true,
        user_type: 'admin'
    },
    {
        id: 44,
        parent_id: 17,
        key: 'newPhotographerRegistered',
        description: 'New photographer registered',
        value: true,
        user_type: 'admin'
    }
];
//This array contains all the table names without autoincrement id
const tableWithoutIdNames = ['photographers_services'];

exports.seed = async (knex) => {

    console.info('\n\n[INFO]: running base seeder');

    const insertIfNotPresent = async ({ tableName, elements }) => {

        const isSameElement = (dbObject, newObject) => {

            return Object.entries(newObject).every(([key, value]) =>
                ((typeof value === 'object') ? isSameElement(dbObject[key], value) : dbObject[key] === value));
        };

        const existingElements = await knex(tableName);
        const existingElementsIdSet = new Set(existingElements.map(({ id }) => id));

        let insertedCount = 0;
        let updatedCount = 0;

        for (const newElement of elements) {
            if (newElement.id && existingElementsIdSet.has(newElement.id)) {
                await knex(tableName).where('id', newElement.id).update(newElement);
                ++updatedCount;
            }
            else if (existingElements.some((oldElement) => isSameElement(oldElement, newElement))) {
                continue;
            }
            else {
                await knex(tableName).insert(newElement);
                ++insertedCount;
            }
        }

        console.info(`\n[INFO]: inserting ${elements.length} elements in table ${tableName} (${insertedCount} inserted, ${updatedCount} updated)`);
    };

    // Insert purposes first
    if (data.purposes && data.purposes.length > 0) {
        console.info('\n[INFO]: Inserting purposes data...');
        await insertIfNotPresent({ tableName: 'purposes', elements: data.purposes });

        const rawQueryPurposes = `SELECT setval('purposes_id_seq', (SELECT MAX(id) from "purposes"))`;
        console.info(`[INFO]: updating serial id counter for table purposes (using raw query ${rawQueryPurposes})`);
        const resultPurposes = await knex.raw(rawQueryPurposes);
        console.info(`[INFO]: serial id counter updated to ${resultPurposes.rows[0].setval}`);
    }
    else {
        console.warn('[WARN]: No purposes data to insert');
    }

    // Insert services next
    if (data.services && data.services.length > 0) {
        console.info('\n[INFO]: Inserting services data...');
        await insertIfNotPresent({ tableName: 'services', elements: data.services });

        const rawQueryServices = `SELECT setval('services_id_seq', (SELECT MAX(id) from "services"))`;
        console.info(`[INFO]: updating serial id counter for table services (using raw query ${rawQueryServices})`);
        const resultServices = await knex.raw(rawQueryServices);
        console.info(`[INFO]: serial id counter updated to ${resultServices.rows[0].setval}`);
    }
    else {
        console.warn('[WARN]: No services data to insert');
    }

    // Insert packages next
    if (data.packages && data.packages.length > 0) {
        console.info('\n[INFO]: Inserting packages data...');
        await insertIfNotPresent({ tableName: 'packages', elements: data.packages });

        const rawQueryPackages = `SELECT setval('packages_id_seq', (SELECT MAX(id) from "packages"))`;
        console.info(`[INFO]: updating serial id counter for table packages (using raw query ${rawQueryPackages})`);
        const resultPackages = await knex.raw(rawQueryPackages);
        console.info(`[INFO]: serial id counter updated to ${resultPackages.rows[0].setval}`);
    }
    else {
        console.warn('[WARN]: No packages data to insert');
    }

    // Insert remaining data
    for (const [tableName, elements] of Object.entries(data)) {
        if (['purposes', 'services', 'packages'].includes(tableName)) {
            continue;
        }

        await insertIfNotPresent({ tableName, elements });

        if (!tableWithoutIdNames.includes(tableName)) {
            const rawQuery = `SELECT setval('${tableName}_id_seq', (SELECT MAX(id) from "${tableName}"));`;
            console.info(`[INFO]: updating serial id counter for table ${tableName} (using raw query ${rawQuery})`);
            const result = await knex.raw(rawQuery);
            console.info(`[INFO]: serial id counter updated to ${result.rows[0].setval}`);
        }
    }
};

module.exports.packages = data.packages;

module.exports.services = data.services;

module.exports.tableWithoutIdNames = tableWithoutIdNames;
