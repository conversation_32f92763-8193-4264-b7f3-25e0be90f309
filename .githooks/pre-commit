#!/bin/bash

error_count=0

RED='\033[0;31m'
CYAN='\033[0;36m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color

echo "
Running pre-commit hook...
"

files=$(git diff --cached --name-only | grep -E '\.(js|jsx)$')
files_count=$(echo $files | wc -w)

if [ $files_count -ge 5 ]; then
  echo -e "${RED}TOO MANY FILES${NC}: You are including $files_count different files in a single commit. Please split them into multiple commits.
"
  error_count=$((error_count+1))
fi

for file in $files
do

  ### Run ESLint on all the staged changes
  eslint_res=$(git show ":$file" | npx eslint --color --stdin --stdin-filename "$file") # We use git show to only lint the staged changes, not any un-staged changes
  if [ $? -ne 0 ]; then
    echo -e "${RED}ESLINT ERROR${NC}: ESLint failed on staged file '$file'. Please check your code and try again. \
To run ESLint manually, you can use the command $CYAN'npx eslint $file'$NC
$eslint_res"
    error_count=$((error_count+1))
  fi

  ### Check for staged `console.` calls (they should be removed before committing)
  debug_commands=$(git diff --cached --color=never $file | grep -n -E '^\+' -A 1 -B 1 | grep 'console.' -A 1 -B 1)
  if [[ -n $debug_commands ]]; then
    echo -e "${RED}DEBUG CODE${NC}: File $file contains committed debug commands, please remove them from the commit.

$debug_commands
"
    error_count=$((error_count+1))
  fi
done

if [ $error_count -gt 0 ]; then
  echo -e "

$RED$error_count errors$NC have been found while scanning the files staged for the commit. Please review and fix them (to do it, you \
can modify the corresponding files, use $CYAN'git add'$NC to stage them and $CYAN'git commit --amend'$NC to include them in the commit).

In case the commit absolutely needs to be done even if it breaks this check (please, do this only if you're really sure about it), \
you can skip this validation by running $CYAN'git commit --no-verify'$NC (NOTE: this will skip ALL pre-commit validation).
  "
  exit 1
else
  echo -e "${GREEN}Everything looks good!$NC
"
fi
