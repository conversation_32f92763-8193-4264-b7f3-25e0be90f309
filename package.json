{"name": "flashy-backend", "version": "0.1.0", "description": "", "author": "", "license": "ISC", "main": "server/index.js", "directories": {"lib": "lib", "test": "test"}, "scripts": {"start-nodemon": "nodemon --ignore logs/ --ignore test/ server/index.js", "start": "node server/index.js", "test": "./test.sh", "lint": "eslint .", "test-coverage": "./test.sh -r html -o test_results/coverage.html -v", "test-coverage-routes": "./test.sh -r html -o test_results/coverage_routes.html -v --coverage-path /lib/routes"}, "dependencies": {"@hapi/bell": "^12.2.0", "@hapi/boom": "^9.1.2", "@hapi/bourne": "^2.0.0", "@hapi/glue": "^8.0.0", "@hapi/hapi": "^20.1.4", "@hapi/hoek": "^9.1.1", "@hapi/inert": "^6.0.3", "@hapi/jwt": "^2.0.1", "@hapi/vision": "^6.0.1", "@langchain/pinecone": "^0.0.8", "@pinecone-database/pinecone": "^2.2.2", "archiver": "^6.0.1", "auth0": "^2.35.0", "aws-sdk": "^2.929.0", "axios": "^0.27.2", "confidence": "^5.0.1", "hapi-auth-jwt2": "^10.2.0", "hapi-pino": "^8.3.0", "hapi-swagger": "^14.1.0", "haute-couture": "^3.6.1", "image-size": "^1.1.1", "joi": "^17.4.0", "json2csv": "^5.0.6", "jsonwebtoken": "^8.5.1", "knex": "^0.95.3", "langchain": "^0.2.12", "moment": "^2.29.3", "multer": "^1.4.5-lts.1", "node-stream-zip": "^1.15.0", "objection": "^2.2.15", "openai": "^4.53.2", "pg": "^8.5.1", "pgvector": "^0.2.0", "replicate": "^1.0.1", "schmervice": "^1.6.0", "schwifty": "^5.4.1", "sharp": "^0.33.5", "stripe": "^8.184.0", "unzipper": "^0.11.6", "uuid": "^9.0.0"}, "devDependencies": {"@hapi/code": "^8.0.3", "@hapi/eslint-plugin": "^5.0.0", "@hapi/lab": "^24.1.1", "eslint": "^7.22.0", "hpal": "^2.6.0", "hpal-debug": "^1.5.0", "nodemon": "^2.0.7", "toys": "^2.3.1"}}