'use strict';

module.exports = (server, options) => ({
    plugins: {
        options: {
            prettyPrint: server.settings.app.log.prettyPrint && { colorize: true },
            logRequestStart: true,
            logRequestComplete: true,
            logPayload: false,
            logQueryParams: true,
            level: server.settings.app.log.level,
            stream: server.settings.app.log.stream,
            redact: { paths: ['req.headers.authorization', 'req.headers.referer', 'tags'], remove: true },
            formatters: {
                level(label) {

                    return { level: label.toUpperCase() };
                }
            }
        }
    }
});
