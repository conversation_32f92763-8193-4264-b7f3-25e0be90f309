'use strict';

const Package = require('../../package.json');
const { resolve } = require('path');

module.exports = (server, options) => {

    return {
        plugins: {
            options: {
                info: {
                    title: 'API Documentation',
                    version: Package.version,
                    description: 'This is documentation of the backend API for the service',
                    contact: {
                        name: '<PERSON><PERSON><PERSON>',
                        email: '<EMAIL>'
                    }
                },
                routeTag: 'swag-include',
                schemes: ['https', 'http'],
                securityDefinitions: {
                    jwt: {
                        type: 'api<PERSON>ey',
                        name: 'Authorization',
                        in: 'header'
                    }
                },
                security: [{ jwt: [] }],
                auth: false, // TODO: jwt (?)
                host: server.settings.app.publicHostname,
                cors: true,
                documentationPath: '/documentation/index',
                jsonPath: '/documentation/swagger.json',
                jsonRoutePath: '/documentation/swagger.json',
                swaggerUIPath: '/documentation/swaggerui',
                routesBasePath: '/documentation/swaggerui',
                templates: resolve(__dirname, '..', 'documentation-templates'),
                grouping: 'tags',
                tagsGroupingFilter: (tag) => tag.startsWith('resource-')
            }
        }
    };
};
