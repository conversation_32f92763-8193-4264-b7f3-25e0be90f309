<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>{{ hapiSwagger.info.title }}</title>
    <link rel="stylesheet" type="text/css" href="{{ hapiSwagger.swaggerUIPath }}swagger-ui.css" />
    <link rel="icon" type="image/png" href="{{ hapiSwagger.swaggerUIPath }}favicon-32x32.png" sizes="32x32" />
    <link rel="icon" type="image/png" href="{{ hapiSwagger.swaggerUIPath }}favicon-16x16.png" sizes="16x16" />
    <link rel="stylesheet" type="text/css" href="/documentation/custom.css" />
    <style>
      html {
        box-sizing: border-box;
        overflow: -moz-scrollbars-vertical;
        overflow-y: scroll;
      }

      *,
      *:before,
      *:after {
        box-sizing: inherit;
      }

      body {
        margin: 0;
        background: #fafafa;
        color: blue;
      }
    </style>
  </head>

  <body>
    <div id="swagger-ui"></div>
    <script src="{{ hapiSwagger.swaggerUIPath }}swagger-ui-bundle.js"></script>
    <script src="{{ hapiSwagger.swaggerUIPath }}swagger-ui-standalone-preset.js"></script>
    <script src="{{ hapiSwagger.swaggerUIPath }}extend.js" type="text/javascript"></script>
    <script>
      window.onload = function() {

        var url = window.location.search.match(/url=([^&]+)/);
        if (url && url.length > 1) {
            url = decodeURIComponent(url[1]);
        } else {
            url = "{{{hapiSwagger.jsonPath}}}";
        }

        // pull validatorUrl string or null form server
        var validatorUrl = null;
        {{#if hapiSwagger.validatorUrl}}
        validatorUrl: '{{hapiSwagger.validatorUrl}}';
        {{/if}}

        var swaggerOptions = {
          url: url,
          validatorUrl: validatorUrl,
          dom_id: '#swagger-ui',
          deepLinking: true,
          presets: [SwaggerUIBundle.presets.apis, SwaggerUIStandalonePreset],
          plugins: [SwaggerUIBundle.plugins.DownloadUrl],
          layout: 'StandaloneLayout',
          docExpansion: "{{hapiSwagger.expanded}}",
          tagsSorter: apisSorter.{{hapiSwagger.sortTags}},
          operationsSorter: operationsSorter.{{hapiSwagger.sortEndpoints}},
          displayRequestDuration: true,
          filter: true,
          requestInterceptor: (request) => {

            if (request.headers.Authorization && !request.headers.Authorization.startsWith('Bearer')) {
              request.headers.Authorization = `Bearer ${request.headers.Authorization}`
            }

            return request;
          }
        }

        var ui = SwaggerUIBundle(swaggerOptions);
        window.ui = ui;
        
        const urlParams = new URLSearchParams(window.location.search);
        const accessToken = urlParams.get('__bearer_access_token');
        
        // TODO: remove set timeout
        setTimeout(() => {
          document.querySelector('button.authorize').onclick = (event) => {

            event = event || window.event;
            if (typeof event.stopPropagation != "undefined") {
                event.stopPropagation();
            } else {
                event.cancelBubble = true;
            }

            window.location = '/login?__redirect_uri=documentation/index';
          };

          if (accessToken) {
            console.log('pre')
            ui.preauthorizeApiKey('jwt', accessToken);
          }
        }, 1000);
      };
    </script>
  </body>
</html>
