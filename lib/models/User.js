'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class User extends (Schwifty.Model) {

    static get tableName() {

        return 'users';
    }

    static get joiSchema() {

        return Joi.object({
            id: Joi.number().integer(),
            name: Joi.string().required(),
            email: Joi.string().max(320).required(),
            phone: Joi.string().max(20).optional().allow(''),
            photographer_id: Joi.number().integer().required().allow(null),
            editor_id: Joi.number().integer().required().allow(null),
            client_id: Joi.number().integer().required().allow(null),
            account_verified: Joi.boolean().default(false),
            // Disable user approval (users are approved by default)
            status: Joi.string().max(10).default('active'),
            last_update: Joi.date(),
            auth_provider_reference: Joi.string().max(200).optional().allow(null)
        });
    }

    get role() {

        if (this.client_id !== null) {
            return 'client';
        }

        if (this.photographer_id !== null) {
            return 'photographer';
        }

        if (this.editor_id !== null) {
            return 'editor';
        }

        return 'admin';
    }

    get photographer() {

        return this.$relatedQuery('photographer');
    }

    get client() {

        return this.$relatedQuery('client');
    }

    get editor() {

        return this.$relatedQuery('editor');
    }

    get userPaymentGateways() {

        return this.$relatedQuery('userPaymentGateways');
    }

    static get relationMappings() {

        const Photographer = require('./Photographer');
        const Client = require('./Client');
        const Editor = require('./Editor');

        return {
            photographer: {
                relation: Schwifty.Model.HasOneRelation,
                modelClass: Photographer,
                join: {
                    from: `${User.tableName}.photographer_id`,
                    to: `${Photographer.tableName}.id`
                }
            },
            client: {
                relation: Schwifty.Model.HasOneRelation,
                modelClass: Client,
                join: {
                    from: `${User.tableName}.client_id`,
                    to: `${Client.tableName}.id`
                }
            },
            editor: {
                relation: Schwifty.Model.HasOneRelation,
                modelClass: Editor,
                join: {
                    from: `${User.tableName}.editor_id`,
                    to: `${Editor.tableName}.id`
                }
            },
            userPaymentGateways: {
                relation: Schwifty.Model.HasManyRelation,
                modelClass: require('./UserPaymentGateway'),
                join: {
                    from: `${User.tableName}.id`,
                    to: `${require('./UserPaymentGateway').tableName}.user_id`
                }
            }
        };
    }
};
