'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

const statuses = {
    toBePayed: 'toBePayed',
    toSchedule: 'toSchedule',
    scheduled: 'scheduled',
    photographerAssigned: 'assigned',
    confirmed: 'confirmed',
    photosUploaded: 'uploaded',
    photosReady: 'ready',
    completed: 'completed',
    canceled: 'canceled',
    redeemable: 'redeemable',
    notCreated: 'notCreated'
};

module.exports = class Shoot extends (Schwifty.Model) {

    static get tableName() {

        return 'shoots';
    }

    static get statuses() {

        // NOTE: destructuring and rebuilding creates a shallow copy of the object
        return { ...statuses };
    }

    static get joiSchema() {

        return Joi.object({
            id: Joi.number().integer(),
            name: Joi.string().max(255).allow(null),
            photographer_id: Joi.number().integer().required().allow(null),
            order_id: Joi.number().integer().required(),
            datetime: Joi.date().timestamp().allow(null),
            address: Joi.object(),
            latitude: Joi.number().precision(6),
            longitude: Joi.number().precision(6),
            notes: Joi.string().allow(null),
            contact_phone: Joi.string().max(255).allow(null),
            contact_name: Joi.string().max(255).allow(null),
            picture_number: Joi.number().integer().min(0).required(),
            video_number: Joi.number().integer().min(0),
            video_duration: Joi.number().integer().min(0),
            price: Joi.number().precision(2).min(0).required(),
            photographer_revenue: Joi.number().precision(2).min(0).required(),
            duration: Joi.number().integer().required(),
            last_edit_by: Joi.number().integer().required().allow(null),
            last_update: Joi.date(),
            scheduled: Joi.boolean(),
            images_preview: Joi.boolean().default(false),
            raws_preview: Joi.boolean().default(false),
            old: Joi.boolean().default(false),
            redeemable_counter: Joi.number().integer().allow(null),
            redeemable_total: Joi.number().integer().allow(null),
            redeemable_shoot_id: Joi.number().integer().allow(null),
            outlet_code: Joi.string().allow(null),
            outlet_name: Joi.string().allow(null),
            poc_email: Joi.string().allow(null),
            poc_phone: Joi.string().allow(null),
            poc_name: Joi.string().allow(null),
            consumer_client_id: Joi.number().integer().required().allow(null),
            is_brief_uploaded: Joi.boolean(),
            type: Joi.string().allow(null),
            content: Joi.string().allow('photography', 'videography'),
            status: Joi.string().max(10).required()
        });
    }

    static get relationMappings() {

        const Service = require('./Service');
        const ShootService = require('./ShootService');
        const Order = require('./Order');
        const Photographer = require('./Photographer');
        const Client = require('./Client');
        const Image = require('./Image');
        const RawImage = require('./RawImage');

        return {
            services: {
                relation: Schwifty.Model.ManyToManyRelation,
                modelClass: Service,
                join: {
                    from: `${Shoot.tableName}.id`,
                    through: {
                        from: `${ShootService.tableName}.shoot_id`,
                        to: `${ShootService.tableName}.service_id`
                    },
                    to: `${Service.tableName}.id`
                }
            },
            order: {
                relation: Schwifty.Model.HasOneRelation,
                modelClass: Order,
                join: {
                    from: `${Shoot.tableName}.order_id`,
                    to: `${Order.tableName}.id`
                }
            },
            photographer: {
                relation: Schwifty.Model.HasOneRelation,
                modelClass: Photographer,
                join: {
                    from: `${Shoot.tableName}.photographer_id`,
                    to: `${Photographer.tableName}.id`
                }
            },
            consumer: {
                relation: Schwifty.Model.HasOneRelation,
                modelClass: Client,
                join: {
                    from: `${Shoot.tableName}.consumer_client_id`,
                    to: `${Client.tableName}.id`
                }
            },
            images: {
                relation: Schwifty.Model.HasManyRelation, // Use HasManyRelation for a one-to-many relationship
                modelClass: Image, // Use the Image model class
                join: {
                    from: `${Shoot.tableName}.id`,
                    to: `${Image.tableName}.shoot_id`
                }
            },
            raws: {
                relation: Schwifty.Model.HasManyRelation, // Use HasManyRelation for a one-to-many relationship
                modelClass: RawImage, // Use the Image model class
                join: {
                    from: `${Shoot.tableName}.id`,
                    to: `${RawImage.tableName}.shoot_id`
                }
            }
        };
    }
};
