'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class PackagePlan extends Schwifty.Model {
    static get tableName() {

        return 'package_plans';
    }

    static get idColumn() {

        return 'id';
    }

    static get jsonSchema() {

        return {
            type: 'object',
            required: ['name', 'stripe_reference', 'status', 'price', 'provider', 'duration', 'description', 'plans'],
            properties: {
                id: { type: 'integer' },
                name: { type: 'string', minLength: 1, maxLength: 255 },
                label: { type: 'string', minLength: 1, maxLength: 255 },
                stripe_reference: { type: 'string', minLength: 1, maxLength: 255 },
                status: { type: 'string', enum: ['active', 'inactive'] },
                price: { type: 'number', minimum: 0 },
                discount: { type: 'number', minimum: 0 },
                plan: { type: 'object' },
                original_price: { type: 'number', minimum: 0 },
                currency: { type: 'string', enum: ['aed', 'usd'] },
                interval: { type: 'string', enum: ['day', 'week', 'month', 'year'] },
                interval_count: { type: 'integer', minimum: 1 },
                provider: { type: 'string', default: 'stripe' },
                description: { type: 'string', minLength: 0 },
                plans: { type: 'object' },
                is_popular: { type: 'boolean' },
                created_at: { type: 'string', format: 'date-time' },
                updated_at: { type: 'string', format: 'date-time' }
            }
        };
    }

    static get joiSchema() {

        return Joi.object({
            id: Joi.number().integer().positive().optional(),
            name: Joi.string().min(1).max(255).required(),
            label: Joi.string().min(1).max(255).required(),
            stripe_reference: Joi.string().min(1).max(255).optional().allow(null),
            status: Joi.string().valid('active', 'inactive').default('active'),
            price: Joi.number().min(0).optional().allow(null),
            discount: Joi.number().min(0).optional(),
            origin_price: Joi.number().min(0).optional(),
            currency: Joi.string().default('aed'),
            interval: Joi.string().valid('day', 'week', 'month', 'year').required(),
            interval_count: Joi.number().integer().min(1).required(),
            provider: Joi.string().default('stripe').required(),
            description: Joi.string().min(1).required(),
            plans: Joi.object().required(),
            is_popular: Joi.boolean().optional(),
            weight: Joi.number().integer().optional(),
            created_at: Joi.date().iso().optional(),
            updated_at: Joi.date().iso().optional()
        });
    }

    $beforeUpdate() {

        this.updated_at = new Date().toISOString();
    }
};
