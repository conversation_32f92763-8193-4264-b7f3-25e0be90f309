'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class Service extends (Schwifty.Model) {

    static get tableName() {

        return 'services';
    }

    static get joiSchema() {

        return Joi.object({

            id: Joi.number().integer(),
            purpose_id: Joi.number().integer().required(),
            name: Joi.string().max(100).required(),
            status: Joi.string().max(10),
            last_update: Joi.date(),
            num_pictures: Joi.number().integer().min(0).default(0)
        });
    }

    static get relationMappings() {

        const Purpose = require('./Purpose');

        return {
            purpose: {
                relation: Schwifty.Model.HasOneRelation,
                modelClass: Purpose,
                join: {
                    from: `${Service.tableName}.purpose_id`,
                    to: `${Purpose.tableName}.id`
                }
            }
        };
    }
};
