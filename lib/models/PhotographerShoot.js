'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class PhotographerShoot extends (Schwifty.Model) {

    static get tableName() {

        return 'photographers_shoots';
    }

    static get joiSchema() {

        return Joi.object({

            photographer_id: Joi.number().integer(),
            shoot_id: Joi.number().integer(),
            creation_date: Joi.date()
        });
    }

    static get idColumn() {

        return ['photographer_id', 'shoot_id'];
    }
};
