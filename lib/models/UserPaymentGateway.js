'use strict';

const { Model } = require('objection');
const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class UserPaymentGateway extends Schwifty.Model {
    static get tableName() {

        return 'user_payment_gateways';
    }

    static get idColumn() {

        return 'id';
    }

    static get jsonSchema() {

        return {
            type: 'object',
            required: ['user_id', 'reference', 'status', 'provider'],
            properties: {
                id: { type: 'integer' },
                user_id: { type: 'integer' },
                status: { type: 'string', enum: ['deleted', 'active', 'inactive'] },
                reference: { type: 'string', minLength: 1, maxLength: 255 },
                provider: { type: 'string', default: 'stripe' },
                created_at: { type: 'string', format: 'date-time' },
                updated_at: { type: 'string', format: 'date-time' }
            }

        };
    }

    static get relationMappings() {

        const User = require('./User'); // Adjust path to your User model

        return {
            user: {
                relation: Model.BelongsToOneRelation,
                modelClass: User,
                join: {
                    from: 'user_payment_gateways.user_id',
                    to: 'users.id'
                }
            }
        };

    }

    static get joiSchema() {

        return Joi.object({
            id: Joi.number().integer().positive().optional(),
            user_id: Joi.number().integer().positive().required(),
            reference: Joi.string().min(1).max(255).required(),
            status: Joi.string().valid('deleted', 'active', 'inactive').default('active'),
            provider: Joi.string().default('stripe').required(),
            created_at: Joi.date().iso().optional(),
            updated_at: Joi.date().iso().optional()
        });
    }

    $beforeUpdate() {

        this.updated_at = new Date().toISOString();
    }
};
