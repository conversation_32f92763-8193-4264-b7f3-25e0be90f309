'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class ImageVersion extends Schwifty.Model {
    static get tableName() {

        return 'image_versions';
    }

    static get relationMappings() {

        const Image = require('./Image');
        const Stack = require('./Stack');

        return {
            image_version: {
                relation: Schwifty.Model.BelongsToOneRelation,
                modelClass: Image,
                join: {
                    from: `${ImageVersion.tableName}.version_image_id`,
                    to: `${Image.tableName}.id`
                }
            },
            origin_image: {
                relation: Schwifty.Model.BelongsToOneRelation,
                modelClass: Image,
                join: {
                    from: `${ImageVersion.tableName}.origin_image_id`,
                    to: `${Image.tableName}.id`
                }
            },
            stacks: {
                relation: Schwifty.Model.BelongsToOneRelation,
                modelClass: Stack,
                join: {
                    from: `${ImageVersion.tableName}.stack_id`,
                    to: `${Stack.tableName}.id`
                }
            }
        };
    }

    static get joiSchema() {

        return Joi.object({
            id: Joi.number().integer(),
            version_name: Joi.string().allow(null).optional(),
            stack_id: Joi.number().allow(null).optional(),
            origin_image_id: Joi.number().allow(null).optional(),
            version_image_id: Joi.number().allow(null).optional(),
            is_default: Joi.bool().default(true),
            created_at: Joi.date().iso().allow(null).optional()
        });
    }
};
