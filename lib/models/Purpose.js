'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class Purpose extends (Schwifty.Model) {

    static get tableName() {

        return 'purposes';
    }

    static get joiSchema() {

        return Joi.object({

            id: Joi.number().integer(),
            name: Joi.string().max(100).required(),
            status: Joi.string().max(10)

        });
    }
};
