'use strict';

const { Model } = require('objection');
const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class PaymentMethod extends Schwifty.Model {
    static get tableName() {

        return 'payment_methods';
    }

    static get idColumn() {

        return 'id';
    }

    static get jsonSchema() {

        return {
            type: 'object',
            required: ['user_id', 'last4_digit', 'status', 'type', 'provider', 'full_name'],
            properties: {
                id: { type: 'integer' },
                user_id: { type: 'integer' },
                reference: { type: 'string', minLength: 1, maxLength: 255 },
                last4_digit: { type: 'string', minLength: 4, maxLength: 4 },
                status: { type: 'string', enum: ['deleted', 'active', 'inactive'] },
                type: { type: 'string', enum: ['card', 'bank', 'paypal'] },
                is_default: { type: 'boolean', default: true },
                provider: { type: 'string', default: 'stripe' },
                full_name: { type: 'string', minLength: 1, maxLength: 255 },
                created_at: { type: 'string', format: 'date-time' },
                updated_at: { type: 'string', format: 'date-time' }
            }
        };
    }

    static get relationMappings() {

        const User = require('./User'); // Adjust path to your User model

        return {
            user: {
                relation: Model.BelongsToOneRelation,
                modelClass: User,
                join: {
                    from: 'payment_methods.user_id',
                    to: 'users.id'
                }
            }
        };
    }

    static get joiSchema() {

        return Joi.object({
            id: Joi.number().integer().positive().optional(),
            user_id: Joi.number().integer().positive().required(),
            last4_digit: Joi.string().length(4).required(),
            reference: Joi.string().max(255).required(),
            status: Joi.string().valid('deleted', 'active', 'inactive').default('active'),
            type: Joi.string().required(),
            provider: Joi.string().default('stripe').required(),
            full_name: Joi.string().min(1).max(255).required(),
            created_at: Joi.date().iso().optional(),
            updated_at: Joi.date().iso().optional(),
            is_default: Joi.boolean().default(true)
        });
    }

    // Optional: Update timestamps before updating the record
    $beforeUpdate() {

        this.updated_at = new Date().toISOString();
    }
};
