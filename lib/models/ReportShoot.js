'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class ReportShoot extends (Schwifty.Model) {

    static get tableName() {

        return 'reports_shoots';
    }

    static get joiSchema() {

        return Joi.object({
            id: Joi.number().integer(),
            report_id: Joi.number().integer().required(),
            key: Joi.string().max(100).required(),
            value: Joi.number().required()
        });
    }

    static get relationMappings() {

        const Report = require('./Report');

        return {
            report: {
                relation: Schwifty.Model.HasOneRelation,
                modelClass: Report,
                join: {
                    from: `${ReportShoot.tableName}.report_id`,
                    to: `${Report.tableName}.id`
                }
            }
        };
    }
};
