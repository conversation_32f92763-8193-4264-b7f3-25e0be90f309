'use strict';

const { Model } = require('objection');
const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class Billing extends Schwifty.Model {
    static get tableName() {

        return 'billing';
    }

    static get idColumn() {

        return 'id';
    }

    static get relationMappings() {

        const User = require('./User'); // Adjust path
        const PackagePlan = require('./PackagePlan'); // Adjust path

        return {
            user: {
                relation: Model.BelongsToOneRelation,
                modelClass: User,
                join: {
                    from: 'billing.user_id',
                    to: 'users.id'
                }
            },
            package: {
                relation: Model.BelongsToOneRelation,
                modelClass: PackagePlan,
                join: {
                    from: 'billing.package_id',
                    to: 'package_plans.id'
                }
            }
        };
    }

    static get joiSchema() {

        return Joi.object({
            id: Joi.number().integer().positive().optional(),
            user_id: Joi.number().integer().positive().required(),
            package_id: Joi.number().integer().positive().required(),
            name: Joi.string().min(1).max(255).required(),
            address: Joi.string().min(1).max(500).required(),
            status: Joi.string().valid('active', 'inactive', 'canceled', 'pending_payment').default('active'),
            phone: Joi.string().allow(null).optional(),
            email: Joi.string().email().max(255).required(),
            reference: Joi.string().min(1).max(255).required(),
            description: Joi.string().max(1000).allow(null).optional(),
            amount: Joi.number().positive().required(),
            currency: Joi.string().default('aed'),
            interval: Joi.string().optional().allow(null),
            interval_count: Joi.number().integer().positive().optional().allow(null),
            period_start: Joi.date().iso().optional().allow(null),
            period_end: Joi.date().iso().optional().allow(null),
            canceled_at: Joi.date().iso().optional().allow(null),
            created_at: Joi.date().iso().optional(),
            updated_at: Joi.date().iso().optional()
        });
    }

    $beforeUpdate() {

        this.updated_at = new Date().toISOString();
    }
};
