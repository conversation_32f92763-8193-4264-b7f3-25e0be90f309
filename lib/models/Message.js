/* eslint-disable @hapi/scope-start */
'use strict';

const { Model } = require('objection');

module.exports = class Message extends Model {
    static get tableName() {
        return 'messages';
    }

    static get jsonSchema() {
        return {
            type: 'object',
            required: ['conversation_id', 'content', 'sender'],
            properties: {
                id: { type: 'integer' },
                conversation_id: { type: 'integer' },
                content: { type: 'string' },
                sender: { type: 'string' },
                created_at: { type: 'string', format: 'date-time' }
            }
        };
    }

    static get relationMappings() {
        const Conversation = require('./Conversation');

        return {
            conversation: {
                relation: Model.BelongsToOneRelation,
                modelClass: Conversation,
                join: {
                    from: 'messages.conversation_id',
                    to: 'conversations.id'
                }
            }
        };
    }
};
