'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class ReportUser extends (Schwifty.Model) {

    static get tableName() {

        return 'reports_users';
    }

    static get joiSchema() {

        return Joi.object({
            id: Joi.number().integer(),
            report_id: Joi.number().integer().required(),
            shoot_id: Joi.number().integer().required(),
            performer_user_id: Joi.number().integer().required(),
            target_client_user_id: Joi.number().integer().allow(null),
            target_photographer_user_id: Joi.number().integer().allow(null),
            starting_status: Joi.string().required(),
            target_status: Joi.string().required()
        });
    }

    static get relationMappings() {

        const Shoot = require('./Shoot');
        const Report = require('./Report');
        const User = require('./User');

        return {
            report: {
                relation: Schwifty.Model.HasOneRelation,
                modelClass: Report,
                join: {
                    from: `${ReportUser.tableName}.report_id`,
                    to: `${Report.tableName}.id`
                }
            },
            shoot: {
                relation: Schwifty.Model.HasOneRelation,
                modelClass: Shoot,
                join: {
                    from: `${ReportUser.tableName}.shoot_id`,
                    to: `${Shoot.tableName}.id`
                }
            },
            performer_user: {
                relation: Schwifty.Model.HasOneRelation,
                modelClass: User,
                join: {
                    from: `${ReportUser.tableName}.performer_user_id`,
                    to: `${User.tableName}.id`
                }
            },
            target_client_user: {
                relation: Schwifty.Model.HasOneRelation,
                modelClass: User,
                join: {
                    from: `${ReportUser.tableName}.target_client_user_id`,
                    to: `${User.tableName}.id`
                }
            },
            target_photographer_user: {
                relation: Schwifty.Model.HasOneRelation,
                modelClass: User,
                join: {
                    from: `${ReportUser.tableName}.target_photographer_user_id`,
                    to: `${User.tableName}.id`
                }
            }
        };
    }
};
