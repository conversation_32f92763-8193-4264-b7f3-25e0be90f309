'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class Video extends Schwifty.Model {
    static get tableName() {

        return 'videos';
    }

    static get joiSchema() {

        return Joi.object({
            id: Joi.number().integer(),
            shoot_id: Joi.number().integer().optional().allow(null),
            user_id: Joi.number().integer().allow(null),
            url: Joi.string().max(255).required(),
            filename: Joi.string().max(255).required(),
            folder: Joi.string().max(255).required(),
            filepath: Joi.string().max(255).required(),
            status: Joi.string().max(20).required().default('pending'),
            feedback: Joi.string().allow(null),
            aspect_ratio: Joi.number().allow(null),
            duration: Joi.number().integer().required(),
            codec: Joi.string().max(255).allow(null),
            framerate: Joi.number().integer().allow(null),
            created_at: Joi.date().allow(null),
            bitrate: Joi.number().integer().allow(null),
            preview_url: Joi.string().max(512).allow(null),
            name: Joi.string().max(2048).allow(null),
            placeholder_url: Joi.string().max(512).allow(null),
            video_hls_url: Joi.string().max(512).allow(null),
            preview_video_url: Joi.string().max(512).allow(null),
            seek_video_url: Joi.string().max(512).allow(null),
            sprite_sheet_url: Joi.string().max(512).allow(null),
            transcript_srt_url: Joi.string().max(512).allow(null),
            transcript_vtt_url: Joi.string().max(512).allow(null),
            processing_status: Joi.string().max(20).allow(null),
            uploaded_by: Joi.string().max(255).allow(null)
        });
    }

    static get relationMappings() {

        const Shoot = require('./Shoot');
        const User = require('./User');

        return {
            shoot: {
                relation: Schwifty.Model.HasOneRelation,
                modelClass: Shoot,
                join: {
                    from: `${Video.tableName}.shoot_id`,
                    to: `${Shoot.tableName}.id`
                }
            },
            user: {
                relation: Schwifty.Model.BelongsToOneRelation,
                modelClass: User,
                join: {
                    from: `${Video.tableName}.user_id`,
                    to: `${User.tableName}.id`
                }
            }
        };
    }
};
