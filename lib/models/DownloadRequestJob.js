'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class DownloadRequestJob extends Schwifty.Model {

    static get tableName() {

        return 'download_request_jobs';
    }

    static get joiSchema() {

        return Joi.object({
            id: Joi.number().integer(),
            shoot_id: Joi.number().integer().required(),
            message: Joi.string().max(1024).required(),
            status: Joi.string().max(20).required().default('pending'),
            progress: Joi.number().integer().min(0).max(100).default(0),
            downloadUrl: Joi.string().max(512),
            created_at: Joi.date().iso(),
            updated_at: Joi.date().iso()
        });
    }

    $beforeInsert() {

        const now = new Date().toISOString();
        this.created_at = now;
        this.updated_at = now;
    }

    $beforeUpdate() {

        this.updated_at = new Date().toISOString();
    }

    static get relationMappings() {

        const Shoot = require('./Shoot');

        return {
            shoot: {
                relation: Schwifty.Model.HasOneRelation,
                modelClass: Shoot,
                join: {
                    from: `${DownloadRequestJob.tableName}.shoot_id`,
                    to: `${Shoot.tableName}.id`
                }
            }
        };
    }
};
