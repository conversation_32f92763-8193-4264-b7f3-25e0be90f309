'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class PaymentDetail extends (Schwifty.Model) {

    static get tableName() {

        return 'payment_details';
    }

    static get joiSchema() {

        return Joi.object({

            id: Joi.number().integer(),
            type: Joi.string().max(255).required(),
            details: Joi.object().required(),
            status: Joi.string().max(10)
        });
    }
};
