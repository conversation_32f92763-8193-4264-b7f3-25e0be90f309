'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class Package extends (Schwifty.Model) {

    static get tableName() {

        return 'packages';
    }

    static get joiSchema() {

        return Joi.object({

            id: Joi.number().integer(),
            service_id: Joi.number().integer().required(),
            name: Joi.string().required(),
            description: Joi.string().required().default(''),
            price: Joi.number().precision(2).min(0).required(),
            photographer_revenue: Joi.number().precision(2).min(0).required(),
            currency: Joi.string().max(3).default('AED'),
            duration: Joi.number().integer().min(0).required(),
            picture_number: Joi.number().integer().min(0).required(),
            is_plus: Joi.boolean().default(false),
            price_per_additional_photo: Joi.number().precision(2).min(0).default(0),
            status: Joi.string().max(10),
            last_update: Joi.date(),
            num_pictures: Joi.number().integer().min(0).default(0),
            price_tag: Joi.string().allow('').allow(null).default(''),
            stripe_product_id: Joi.string().allow('').allow(null).default(''),
            package_type: Joi.string().allow('').allow(null).default('')
        });
    }

    static get relationMappings() {

        const Service = require('./Service');

        return {
            service: {
                relation: Schwifty.Model.HasOneRelation,
                modelClass: Service,
                join: {
                    from: `${Package.tableName}.service_id`,
                    to: `${Service.tableName}.id`
                }
            }
        };
    }
};
