'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class NotificationSettings extends (Schwifty.Model) {

    static get tableName() {

        return 'notification_settings';
    }

    static get joiSchema() {

        return Joi.object({

            id: Joi.number().integer(),
            parent_id: Joi.number().integer().allow(null),
            key: Joi.string().max(255).required(),
            description: Joi.string().required(),
            value: Joi.boolean().required(),
            user_type: Joi.string().max(255).required()
        });
    }

    static get relationMappings() {

        return {
            client: {
                relation: Schwifty.Model.HasOneRelation,
                modelClass: NotificationSettings,
                join: {
                    from: `${NotificationSettings.tableName}.parent_id`,
                    to: `${NotificationSettings.tableName}.id`
                }
            }
        };
    }
};
