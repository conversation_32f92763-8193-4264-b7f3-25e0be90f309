/* eslint-disable @hapi/scope-start */
'use strict';

const { Model } = require('objection');

module.exports = class Conversation extends Model {
    static get tableName() {
        return 'conversations';
    }

    static get jsonSchema() {
        return {
            type: 'object',
            required: ['user_id'],
            properties: {
                id: { type: 'integer' },
                user_id: { type: 'integer' },
                metadata: { type: ['object', 'null'] },
                created_at: { type: 'string', format: 'date-time' },
                updated_at: { type: 'string', format: 'date-time' }
            }
        };
    }

    static get relationMappings() {
        const Message = require('./Message');
        const User = require('./User');

        return {
            messages: {
                relation: Model.HasManyRelation,
                modelClass: Message,
                join: {
                    from: 'conversations.id',
                    to: 'messages.conversation_id'
                }
            },
            user: {
                relation: Model.BelongsToOneRelation,
                modelClass: User,
                join: {
                    from: 'conversations.user_id',
                    to: 'users.id'
                }
            }
        };
    }
};
