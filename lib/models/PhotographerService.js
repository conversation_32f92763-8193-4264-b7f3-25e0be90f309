'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class PhotographerService extends (Schwifty.Model) {

    static get tableName() {

        return 'photographers_services';
    }

    static get joiSchema() {

        return Joi.object({

            photographer_id: Joi.number().integer(),
            service_id: Joi.number().integer(),
            rate: Joi.number().integer().min(1).max(10).default(1)
        });
    }

    static get idColumn() {

        return ['photographer_id', 'service_id'];
    }
};
