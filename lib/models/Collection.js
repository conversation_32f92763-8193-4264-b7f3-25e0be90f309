'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class Collection extends Schwifty.Model {
    static get tableName() {

        return 'collections';
    }

    static get joiSchema() {

        return Joi.object({
            id: Joi.number().integer(),
            name: Joi.string().max(255).required(),
            user_id: Joi.number().integer().required(),
            created_at: Joi.date(),
            updated_at: Joi.date()
        });
    }

    static get relationMappings() {

        const User = require('./User');
        const CollectionItem = require('./CollectionItem');
        const Image = require('./Image');
        const Video = require('./Video');

        return {
            user: {
                relation: Schwifty.Model.BelongsToOneRelation,
                modelClass: User,
                join: {
                    from: `${Collection.tableName}.user_id`,
                    to: `${User.tableName}.id`
                }
            },
            collectionItems: {
                relation: Schwifty.Model.HasManyRelation,
                modelClass: CollectionItem,
                join: {
                    from: `${Collection.tableName}.id`,
                    to: `${CollectionItem.tableName}.collection_id`
                }
            },
            images: {
                relation: Schwifty.Model.ManyToManyRelation,
                modelClass: Image,
                join: {
                    from: `${Collection.tableName}.id`,
                    through: {
                        from: `${CollectionItem.tableName}.collection_id`,
                        to: `${CollectionItem.tableName}.image_id`
                    },
                    to: `${Image.tableName}.id`
                }
            },
            videos: {
                relation: Schwifty.Model.ManyToManyRelation,
                modelClass: Video,
                join: {
                    from: `${Collection.tableName}.id`,
                    through: {
                        from: `${CollectionItem.tableName}.collection_id`,
                        to: `${CollectionItem.tableName}.video_id`
                    },
                    to: `${Video.tableName}.id`
                }
            }
        };
    }
};
