'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class Comment extends Schwifty.Model {
    static get tableName() {

        return 'comments';
    }

    static get relationMappings() {

        const User = require('./User');
        const Image = require('./Image');

        return {
            user: {
                relation: Schwifty.Model.BelongsToOneRelation,
                modelClass: User,
                join: {
                    from: `${Comment.tableName}.user_id`,
                    to: `${User.tableName}.id`
                }
            },
            image: {
                relation: Schwifty.Model.BelongsToOneRelation,
                modelClass: Image,
                join: {
                    from: `${Comment.tableName}.image_id`,
                    to: `${Image.tableName}.id`
                }
            }

        };
    }

    static get joiSchema() {

        return Joi.object({
            id: Joi.number().integer(),
            content: Joi.string().required(),
            user_name: Joi.string().required(),
            user_id: Joi.number().integer().required(),
            image_id: Joi.number().integer().allow(null),
            created_at: Joi.date(),
            updated_at: Joi.date()
        });
    }
};
