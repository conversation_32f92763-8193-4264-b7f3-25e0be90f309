'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class Editor extends (Schwifty.Model) {

    static get tableName() {

        return 'editors';
    }

    static get joiSchema() {

        return Joi.object({

            id: Joi.number().integer(),
            status: Joi.string().max(10)
        });
    }

    static get relationMappings() {

        const User = require('./User');

        return {
            user: {
                relation: Schwifty.Model.BelongsToOneRelation,
                modelClass: User,
                join: {
                    from: `${User.tableName}.editor_id`,
                    to: `${Editor.tableName}.id`
                }
            }
        };
    }
};
