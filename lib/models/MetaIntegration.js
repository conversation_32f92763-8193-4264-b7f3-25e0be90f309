'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class MetaIntegration extends Schwifty.Model {

    static get tableName() {

        return 'meta_integrations';
    }

    static get joiSchema() {

        return Joi.object({
            id: Joi.number().integer(),
            user_id: Joi.number().integer().required(),
            fb_business_id: Joi.string().max(50).optional(),
            business_name: Joi.string().max(1024).optional(),

            // Access token details
            access_token: Joi.string().optional(),
            token_expires_at: Joi.date().optional(),

            // Facebook pages and Instagram accounts as JSONB arrays
            facebook_pages: Joi.array().default([]),
            instagram_accounts: Joi.array().default([]),

            // Metadata
            created_at: Joi.date(),
            updated_at: Joi.date()
        });
    }

    static get relationMappings() {

        const User = require('./User');

        return {
            user: {
                relation: Schwifty.Model.HasOneRelation,
                modelClass: User,
                join: {
                    from: `${this.tableName}.user_id`,
                    to: `${User.tableName}.id`
                }
            }
        };
    }

    $beforeInsert() {

        const now = new Date();
        this.created_at = now;
        this.updated_at = now;
    }

    $beforeUpdate() {

        this.updated_at = new Date();
    }
};
