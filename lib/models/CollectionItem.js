'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class CollectionItem extends Schwifty.Model {
    static get tableName() {

        return 'collection_items';
    }

    static get joiSchema() {

        return Joi.object({
            id: Joi.number().integer(),
            collection_id: Joi.number().integer().required(),
            image_id: Joi.number().integer().optional().allow(null),
            video_id: Joi.number().integer().optional().allow(null),
            added_at: Joi.date()
        });
    }

    static get relationMappings() {

        const Collection = require('./Collection');
        const Image = require('./Image');
        const Video = require('./Video');

        return {
            collection: {
                relation: Schwifty.Model.BelongsToOneRelation,
                modelClass: Collection,
                join: {
                    from: `${CollectionItem.tableName}.collection_id`,
                    to: `${Collection.tableName}.id`
                }
            },
            image: {
                relation: Schwifty.Model.BelongsToOneRelation,
                modelClass: Image,
                join: {
                    from: `${CollectionItem.tableName}.image_id`,
                    to: `${Image.tableName}.id`
                }
            },
            video: {
                relation: Schwifty.Model.BelongsToOneRelation,
                modelClass: Video,
                join: {
                    from: `${CollectionItem.tableName}.video_id`,
                    to: `${Video.tableName}.id`
                }
            }
        };
    }
};
