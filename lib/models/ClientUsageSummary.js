'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class ClientUsageSummary extends (Schwifty.Model) {

    static get tableName() {

        return 'client_usage_summaries';
    }

    static get joiSchema() {

        return Joi.object({

            id: Joi.number().integer(),
            client_id: Joi.number().integer().required(),
            total_members: Joi.number().min(0).integer().required().default(0),
            total_storage_size: Joi.number().min(0).integer().required().default(0),
            created_at: Joi.date().iso().optional(),
            updated_at: Joi.date().iso().optional()
        });
    }
    static get relationMappings() {

        const Client = require('./Client');

        return {

            client: {
                relation: Schwifty.Model.HasOneRelation,
                modelClass: Client,
                join: {
                    from: `${ClientUsageSummary.tableName}.client_id`,
                    to: `${Client.tableName}.id`
                }
            }
        };
    }

    $beforeUpdate() {

        this.updated_at = new Date().toISOString();
    }
};
