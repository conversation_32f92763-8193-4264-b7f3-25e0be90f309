'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class Client extends (Schwifty.Model) {

    static get tableName() {

        return 'clients';
    }

    static get joiSchema() {

        return Joi.object({

            id: Joi.number().integer(),
            company_name: Joi.string().optional().allow( null),
            industry: Joi.string().optional().allow('', null),
            size: Joi.number().optional().allow( null),
            contact_name: Joi.string().optional().allow('', null),
            contact_email: Joi.string().max(320).optional().allow('', null),
            contact_phone: Joi.string().max(20).optional().allow('', null),
            main_location: Joi.string().optional().default('N/A'),
            country: Joi.string().max(2).optional().default('SA'),
            payment_details_id: Joi.number().integer().required(),
            status: Joi.string().max(10),
            last_update: Joi.date(),
            registration_guid: Joi.string(),
            parent_client_id: Joi.number().integer().allow(null)
        });
    }
    static get relationMappings() {

        const PaymentDetail = require('./PaymentDetail');

        return {
            paymentDetail: {
                relation: Schwifty.Model.HasOneRelation,
                modelClass: PaymentDetail,
                join: {
                    from: `${Client.tableName}.payment_details_id`,
                    to: `${PaymentDetail.tableName}.id`
                }
            },
            parentClient: {
                relation: Schwifty.Model.HasOneRelation,
                modelClass: Client,
                join: {
                    from: `${Client.tableName}.parent_client_id`,
                    to: `${Client.tableName}.id`
                }
            }
        };
    }
};
