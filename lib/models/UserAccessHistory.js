'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class UserAccessHistory extends (Schwifty.Model) {

    static get tableName() {

        return 'user_access_histories';
    }

    static get idColumn() {

        return 'id';
    }

    static get joiSchema() {

        return Joi.object({

            id: Joi.number().integer(),
            user_id: Joi.number().integer().required(),
            active_session_id: Joi.string().required(),
            created_at: Joi.date().iso().optional(),
            updated_at: Joi.date().iso().optional()
        });
    }

    static get relationMappings() {

        const User = require('./User');

        return {

            client: {
                relation: Schwifty.Model.HasOneRelation,
                modelClass: User,
                join: {
                    from: `${UserAccessHistory.tableName}.user_id`,
                    to: `${User.tableName}.id`
                }
            }
        };
    }

    $beforeUpdate() {

        this.updated_at = new Date().toISOString();
    }
};
