'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class NotificationQueue extends (Schwifty.Model) {

    static get tableName() {

        return 'notification_queue';
    }

    static get joiSchema() {

        return Joi.object({

            id: Joi.number().integer(),
            shoot_id: Joi.number().integer().allow(null),
            type: Joi.string().max(255).required(),
            metaData: Joi.object(),
            target: Joi.object().max(255).required(),
            dispatch_time: Joi.date().timestamp().required()
        });
    }

    static get relationMappings() {

        const Shoot = require('./Shoot');

        return {
            shoot: {
                relation: Schwifty.Model.HasOneRelation,
                modelClass: Shoot,
                join: {
                    from: `${NotificationQueue.tableName}.shoot_id`,
                    to: `${Shoot.tableName}.id`
                }
            }
        };
    }
};
