'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class Order extends (Schwifty.Model) {

    static get tableName() {

        return 'orders';
    }

    static get joiSchema() {

        return Joi.object({

            id: Joi.number().integer(),
            client_id: Joi.number().integer().required(),
            purchase_type: Joi.string().max(255).required(),
            payment_details: Joi.object().required(),
            price: Joi.number().precision(2).min(0).required(),
            billing_address: Joi.object().required(),
            payment_confirmed: Joi.boolean().required(),
            payment_date: Joi.date().timestamp(),
            status: Joi.string().max(10).required()
        });
    }

    static get relationMappings() {

        const Client = require('./Client');

        return {
            client: {
                relation: Schwifty.Model.HasOneRelation,
                modelClass: Client,
                join: {
                    from: `${Order.tableName}.client_id`,
                    to: `${Client.tableName}.id`
                }
            }
        };
    }
};
