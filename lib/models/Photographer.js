'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class Photographer extends (Schwifty.Model) {

    static get tableName() {

        return 'photographers';
    }

    static get joiSchema() {

        return Joi.object({

            id: Joi.number().integer(),
            birthdate: Joi.date().required(),
            english_level: Joi.string().max(100).required(),
            arabic_level: Joi.string().max(100).required(),
            main_location: Joi.string().required(),
            last_update: Joi.date(),
            is_pro: Joi.boolean().default(false),
            is_internal: Joi.boolean().default(false),
            details: Joi.object().required(),
            payment_details_id: Joi.number().integer().required(),
            status: Joi.string().max(10)
        });
    }

    static get relationMappings() {

        const PaymentDetail = require('./PaymentDetail');
        const Service = require('./Service');
        const PhotographerService = require('./PhotographerService');
        const PhotographerShoot = require('./PhotographerShoot');
        const User = require('./User');
        const Shoot = require('./Shoot');

        return {
            paymentDetail: {
                relation: Schwifty.Model.HasOneRelation,
                modelClass: PaymentDetail,
                join: {
                    from: `${Photographer.tableName}.payment_details_id`,
                    to: `${PaymentDetail.tableName}.id`
                }
            },
            services: {
                relation: Schwifty.Model.ManyToManyRelation,
                modelClass: Service,
                join: {
                    from: `${Photographer.tableName}.id`,
                    through: {
                        from: `${PhotographerService.tableName}.photographer_id`,
                        to: `${PhotographerService.tableName}.service_id`,
                        extra: ['rate']
                    },
                    to: `${Service.tableName}.id`
                }
            },
            user: {
                relation: Schwifty.Model.BelongsToOneRelation,
                modelClass: User,
                join: {
                    from: `${User.tableName}.photographer_id`,
                    to: `${Photographer.tableName}.id`
                }
            },
            shoot: {
                relation: Schwifty.Model.ManyToManyRelation,
                modelClass: Shoot,
                join: {
                    from: `${Photographer.tableName}.id`,
                    through: {
                        from: `${PhotographerShoot.tableName}.photographer_id`,
                        to: `${PhotographerShoot.tableName}.shoot_id`
                    },
                    to: `${Shoot.tableName}.id`
                }
            }
        };
    }
};
