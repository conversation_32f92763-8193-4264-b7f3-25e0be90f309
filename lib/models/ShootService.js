'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class ShootService extends (Schwifty.Model) {

    static get tableName() {

        return 'shoots_services';
    }

    static get joiSchema() {

        return Joi.object({
            id: Joi.number().integer(),
            shoot_id: Joi.number().integer(),
            service_id: Joi.number().integer().allow(null),
            picture_number: Joi.number().integer().min(0).required(),
            package: Joi.object().required()
        });
    }

    static get idColumn() {

        return ['shoot_id', 'service_id'];
    }
};
