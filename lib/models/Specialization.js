'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class Specialization extends (Schwifty.Model) {

    static get tableName() {

        return 'specializations';
    }

    static get joiSchema() {

        return Joi.object({

            id: Joi.number().integer(),
            name: Joi.string().required(),
            status: Joi.string().max(10)
        });
    }
};
