'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class Stack extends Schwifty.Model {

    static get tableName() {

        return 'stacks';
    }

    static get relationMappings() {

        const Image = require('./Image');

        return {
            images: {
                relation: Schwifty.Model.HasManyRelation,
                modelClass: Image,
                join: {
                    from: 'stacks.id',
                    to: 'images.stack_id'
                }
            }
        };
    }

    static get joiSchema() {

        return Joi.object({
            id: Joi.number().integer(),
            description: Joi.string().max(1024).optional(), // Assuming description can be longer, adjust if needed
            user_id: Joi.number().integer().required(),
            created_at: Joi.date(),
            updated_at: Joi.date()
        });
    }
};
