'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class Image extends Schwifty.Model {
    static get tableName() {

        return 'images';
    }

    static get relationMappings() {

        const User = require('./User');
        const Stack = require('./Stack');
        const Comment = require('./Comment');
        const ImageVersion = require('./ImageVersion');

        return {
            user: {
                relation: Schwifty.Model.BelongsToOneRelation,
                modelClass: User,
                join: {
                    from: `${Image.tableName}.user_id`,
                    to: `${User.tableName}.id`
                }
            },
            stack: {
                relation: Schwifty.Model.BelongsToOneRelation,
                modelClass: Stack,
                join: {
                    from: `${Image.tableName}.stack_id`,
                    to: `${Stack.tableName}.id`
                }
            },
            image_versions: {
                relation: Schwifty.Model.HasManyRelation,
                modelClass: require('./ImageVersion'),
                join: {
                    from: `${Image.tableName}.id`,
                    to: `${ImageVersion.tableName}.origin_image_id`
                }
            },
            comments: {
                relation: Schwifty.Model.HasManyRelation,
                modelClass: Comment,
                join: {
                    from: `${Image.tableName}.id`,
                    to: `${Comment.tableName}.image_id`
                }
            }
        };
    }

    static get joiSchema() {

        return Joi.object({
            id: Joi.number().integer(),
            shoot_id: Joi.number().integer().allow(null),
            stack_id: Joi.number().integer().allow(null),
            user_id: Joi.number().integer().allow(null),
            url: Joi.string().max(255).required(),
            preview_url: Joi.string().max(255).required(),
            placeholder_url: Joi.string().max(255).required(),
            filename: Joi.string().max(255).required(),
            folder: Joi.string().max(255).required(),
            filepath: Joi.string().max(255).required(),
            status: Joi.string().max(20).required().default('pending'),
            feedback: Joi.string().allow(null),
            uploaded_by: Joi.string().max(255).allow(null),
            aspect_ratio: Joi.number().allow(null),
            height: Joi.number().integer().allow(null),
            width: Joi.number().integer().allow(null),
            embedding: Joi.array().items(Joi.number()).allow(null),
            embedding_context: Joi.string().allow(null),
            created_at: Joi.date().allow(null),
            embedded_at: Joi.date().allow(null),
            name: Joi.string().max(1024).allow(null),
            labels: Joi.array().items(Joi.string()).optional(),
            properties: Joi.object().optional()
        });
    }
};
