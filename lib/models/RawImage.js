'use strict';

const Schwifty = require('schwifty');
const Joi = require('joi');

module.exports = class RawImage extends Schwifty.Model {

    static get tableName() {

        return 'raws';
    }

    static get joiSchema() {

        return Joi.object({
            id: Joi.number().integer(),
            shoot_id: Joi.number().integer().required(),
            url: Joi.string().max(255).required(),
            preview_url: Joi.string().max(255).required(),
            placeholder_url: Joi.string().max(255).required(),
            filename: Joi.string().max(255).required(),
            filepath: Joi.string().max(255).required(),
            folder: Joi.string().max(255).required(),
            status: Joi.string().max(20).required().default('pending'),
            notes: Joi.string().allow(null).default(''),
            aspect_ratio: Joi.number().allow(null)
        });
    }

};
