'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { ValidationError } = require('../../helpers/errors');

module.exports = createRoute({
    documentation: {
        resourceTag: 'instagram',
        description: 'Post image to Instagram',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/instagram/business/{businessId}/post',
    auth: ['jwt'],
    validate: {
        params: Joi.object({
            businessId: Joi.string().required().description('Instagram business ID')
        }),
        payload: Joi.object({
            imageUrl: Joi.string().uri().required()
                .description('URL of the image to post'),
            message: Joi.string().required()
                .description('Text message for the post')
        })
    },
    handler: async (request, h) => {

        const { apiService, instagramService, metaService, authService } = request.services();
        const { businessId } = request.params;
        const { imageUrl, message } = request.payload;

        const { user, tokenPayload: { role } } = request.auth.credentials;
        await authService.checkRouteAuthorization({ role, route: 'get-facebook-status' });

        try {
            // 1. Get user's Meta integration status with full data
            const integrationStatus = await metaService.getIntegrationStatus(user.id, businessId);

            if (!integrationStatus) {
                throw new ValidationError('Instagram account not connected');
            }

            // 4. Create the post
            const result = await instagramService.createMediaPost(
                integrationStatus.integration.access_token,
                businessId,
                imageUrl,
                message
            );

            return apiService.formatResponse(h, {
                success: true,
                postId: result.postId,
                photoId: result.photoId,
                businessId
            });
        }
        catch (error) {
            request.log('error', {
                message: 'Failed to create Facebook post',
                error: {
                    name: error.name,
                    message: error.message,
                    stack: error.stack
                },
                payload: {
                    businessId,
                    message: `${message?.substring(0, 50)}...`,
                    imageUrl
                }
            });

            throw error instanceof ValidationError
                ? error
                : new ValidationError('Failed to create Facebook post');
        }
    }
});
