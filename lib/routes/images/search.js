'use strict';

const { createRoute } = require('../../helpers/route_factory');
const {  search } = require('../../validation-schema/content/payloads');

module.exports = createRoute({
    documentation: {
        resourceTag: 'content',
        description: 'Return content (images and videos) based on metadata and semantic search criteria with cursor-based pagination',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/content/search',
    auth: ['jwt', 'api-key'],
    validate: {
        payload: search
    },
    handler: async (request, h) => {

        const { apiService, contentService, authService } = request.services();
        const { user, tokenPayload: { role } } = request.auth.credentials;
        const {
            filters: {
                date,
                services,
                aspectRatios,
                metadataSearchQuery,
                visualSearchQuery,
                contentTypes,
                clientId,
                collectionId,
                status,
                shootId
            },
            pagination: {
                limit,
                cursor
            }
        } = request.payload;

        try {
            await authService.checkRouteAuthorization({ role, route: 'search-content' });
            const result = await contentService.getContent({
                user,
                role,
                limit,
                clientId,
                collectionId,
                cursor,
                starts_at: date?.from,
                ends_at: date?.to,
                services,
                aspectRatios,
                metadataSearchQuery,
                visualSearchQuery,
                contentTypes,
                status,
                shootId
            });
            const content = result?.content || [];
            const pagination = {
                total: result?.pagination?.total ?? 0,
                hasMore: !!(result && (result?.pagination?.nextCursor || (Array.isArray(content) && content.length > 0))),
                nextCursor: result?.pagination?.nextCursor ?? null
            };
            return apiService.formatResponse(h, {
                content,
                pagination
            });
        }
        catch (error) {
            throw new Error(`Failed to search images ${error}`);
        }
    }
});
