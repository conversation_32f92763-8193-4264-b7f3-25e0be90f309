'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'images',
        description: 'Return a list of videos with signed URLs for playback',
        response: Joi.object({
            videos: Joi.array().optional().items(Joi.object({
                id: Joi.number().required(),
                processing_status: Joi.string().optional().allow(null),
                placeholder_url: Joi.string().optional().allow(null),
                preview_url: Joi.string().optional().allow(null),
                video_hls_url: Joi.string().optional().allow(null)
            })).optional()
        })
    },
    method: 'POST',
    path: '/videos/processing-status',
    auth: ['jwt', 'api-key'],
    validate: {
        payload: Joi.object({
            videoIds: Joi.array().items(Joi.number()).required()
        }).required()
    },
    handler: async (request, h) => {

        const { apiService, contentService } = request.services();
        const { payload: { videoIds } } = request;

        try {
            const videos = await contentService.getVideosProcessingStatus(videoIds);

            const result = {
                videos
            };

            return apiService.formatResponse(h, result);
        }
        catch (error) {
            console.error(error);
            request.logger.error({ error }, 'Failed to get signed cookies');
            return apiService.formatResponse(h, null, 500, ['Error']);
        }
    }
});
