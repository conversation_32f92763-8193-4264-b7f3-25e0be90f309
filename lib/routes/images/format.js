'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'images',
        description: 'Format and resize images using CloudFront URLs',
        response: Joi.object({
            images: Joi.array().items(Joi.object({
                id: Joi.number().required(),
                url: Joi.string().allow(null),
                error: Joi.string().optional()
            })).required()
        })
    },
    method: 'POST',
    path: '/images/format',
    auth: ['jwt', 'api-key'],
    validate: {
        payload: Joi.object({
            imageIds: Joi.array().items(Joi.number()).min(1).required()
                .description('Array of image IDs to process'),
            formatOptions: Joi.object({
                width: Joi.number().integer().positive()
                    .description('Desired width in pixels'),
                height: Joi.number().integer().positive()
                    .description('Desired height in pixels'),
                fit: Joi.string()
                    .valid('fit-in', 'adaptive', 'cover')
                    .default('fit-in')
                    .description('Fit mode for resizing'),
                format: Joi.string()
                    .valid('jpeg', 'webp')
                    .default('jpeg')
                    .description('Output image format'),
                quality: Joi.number().integer().min(1).max(100)
                    .default(85)
                    .description('Image quality (1-100)'),
                preserveAspectRatio: Joi.boolean()
                    .default(true)
                    .description('Maintain aspect ratio during resize'),
                watermark: Joi.object({
                    image: Joi.string().required()
                        .description('Base64 watermark image string'),
                    x: Joi.string().required()
                        .description('X position (e.g. -10p, center)'),
                    y: Joi.string().required()
                        .description('Y position (e.g. 10p, center)'),
                    alpha: Joi.number().integer().min(0).max(100).required()
                        .description('Watermark opacity'),
                    w_ratio: Joi.string().required()
                        .description('Width as percentage of image'),
                    h_ratio: Joi.string().required()
                        .description('Height ratio')
                }).optional()
            }).required()
        })
    },
    handler: async (request, h) => {

        const { apiService, contentService, authService } = request.services();
        const { imageIds, formatOptions } = request.payload;
        const { tokenPayload: { role } } = request.auth.credentials;

        try {
            await authService.checkRouteAuthorization({ role, route: 'format-images' });
            // Going to be refactored
            const formattedImages = await contentService.formatImages({
                imageIds,
                formatOptions
            });
            return apiService.formatResponse(h, { images: formattedImages });
        }
        catch (error) {
            throw new Error(`Failed to format images: ${error.message}`);
        }
    }
});
