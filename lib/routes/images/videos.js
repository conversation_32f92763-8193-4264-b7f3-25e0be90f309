'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'images',
        description: 'Return images based on metadata and visual search criteria with infinite scroll pagination',
        response: Joi.object({
            videos: Joi.array().required()
        })
    },
    method: 'GET',
    path: '/videos',
    auth: ['jwt'],
    validate: {},
    handler: async (request, h) => {

        const { apiService, storageService } = request.services();

        try {
            const videoUrl = await storageService.signCloudFrontUrl({
                url: 'https://cdn.app.flashy-staging.ae/323/e72c8aa4-dae3-495b-bd1f-54932950c851/hls/stream.m3u8' });

            const videos = [
                {
                    title: 'Platform Tutorial',
                    description: 'Learn how to navigate and use the main features of our platform',
                    url: videoUrl,
                    thumbnail: 'https://example.com/thumbnails/tutorial.jpg'
                }
            ];

            const result = {
                videos
            };

            return apiService.formatResponse(h, result);
        }
        catch (error) {
            console.error(error);
            request.logger.error({ error }, 'Failed to get signed cookies');
            return apiService.formatResponse(h, null, 500, ['Error']);
        }

    }
});
