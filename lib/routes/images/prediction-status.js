'use strict';

const Joi = require('joi');
const Boom = require('@hapi/boom');
const { createRoute } = require('../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'images',
        description: 'Get prediction status',
        disableResponseValidation: true
    },
    method: 'POST',
    auth: ['jwt', 'api-key'],
    path: '/images/{imageId}/prediction-status',
    validate: {
        params: Joi.object({
            imageId: Joi.string().required().description('Image ID')
        }),
        payload: Joi.object({
            predictionId: Joi.string().description('Prediction ID')
        })
    },
    handler: async (request, h) => {

        const { imageId } = request.params;
        const predictionId = request.payload?.predictionId || imageId;
        const { replicateService, apiService } = request.services();

        try {
            const prediction = await replicateService.getPrediction(predictionId);

            return apiService.formatResponse(h, {
                prediction: {
                    id: prediction.id,
                    status: prediction.status,
                    output: prediction.output,
                    error: prediction.error,
                    metrics: prediction.metrics,
                    created_at: prediction.created_at,
                    started_at: prediction.started_at,
                    completed_at: prediction.completed_at
                }
            });
        }
        catch (error) {
            if (error.code === 'REPLICATE_API_ERROR') {
                throw Boom.badGateway('Failed to fetch prediction status from Replicate');
            }

            throw error;
        }
    }
});
