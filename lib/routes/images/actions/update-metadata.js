'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        disableResponseValidation: true,
        resourceTag: 'images',
        description: 'updates image metadata'
    },
    method: 'POST',
    path: '/{content}/{id}/action/update-metadata',
    auth: ['jwt', 'api-key'],
    validate: {
        payload: Joi.object({
            name: Joi.string().required()
        }),
        params: Joi.object({
            id: Joi.string().required(),
            content: Joi.string().valid('image', 'video').required()
        })
    },
    handler: async (request, h) => {

        const { apiService, contentService } = request.services();
        const { params: { id, content }, payload: { name } } = request;
        const { user, tokenPayload: { role } } = request.auth.credentials;

        try {
            await contentService.checkUserImageAccess({ user, role, imageId: id });
            // Update the image status and feedback
            const updatedImage = await contentService.updateContentMetadata({
                contentId: id,
                name,
                type: content
            });

            return apiService.formatResponse(h, {
                content: updatedImage
            });
        }
        catch (error) {
            console.error('Error updating image metadata:', error);
            // Handle the error
            return apiService.formatResponse(h, {
                error: 'Failed to update image metadata'
            }, 500);
        }
    }
});
