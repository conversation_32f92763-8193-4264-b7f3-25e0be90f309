'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'images',
        description: 'Add an image to a stack.',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/images/{id}/stack',
    auth: ['jwt', 'api-key'],
    validate: {
        params: Joi.object({
            id: Joi.string().required().description('The ID of the image.')
        }),
        payload: Joi.object({
            stackId: Joi.number().required().description('The ID of the stack to add the image to.'),
            versionName: Joi.string().optional().allow(null).description('Version name'),
            versionComment: Joi.string().optional().allow(null).description('Version comment'),
            versionImageId: Joi.number().required().description('The ID of the version image.')
        })
    },
    handler: async (request, h) => {

        const { contentService, apiService } = request.services();
        const { id } = request.params;
        const { stackId, versionName, versionComment, versionImageId } = request.payload;
        const { user } = request.auth.credentials;

        const updatedImage = await contentService.addImageToStack(stackId, id, versionImageId, versionName, versionComment);
        await contentService.addComment({ content: versionComment, user_id: user.id, user_name: user.name, image_id: id });
        return apiService.formatResponse(h, { image: updatedImage });
    }
});
