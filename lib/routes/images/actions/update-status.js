'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');
const { image } = require('../../../validation-schema/shoots/responses.js');
const { types } = require('../../../services/NotificationService.js');

const encodeURLComponent = (str) => encodeURIComponent(str).replace(/%2F/g, '/').replace(/%3A/g, ':');
module.exports = createRoute({
    documentation: {
        resourceTag: 'images',
        description: 'updates image status',
        response: Joi.object({
            image
        })
    },
    method: 'POST',
    path: '/image/{id}/action/update-status',
    auth: ['jwt', 'api-key'],
    validate: {
        payload: Joi.object({
            newStatus: Joi.string().required(),
            feedback: Joi.string().when('newStatus', {
                is: 'resubmitted',
                then: Joi.required()
            }).allow(null)
        }),
        params: Joi.object({
            id: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const { apiService,  notificationService, contentService } = request.services();
        const { params: { id }, payload: { newStatus, feedback } } = request;

        try {
            // Update the image status and feedback
            const updatedImage = await contentService.updateImageStatus({
                imageId: id,
                newStatus,
                feedback
            });

            if (newStatus === 'resubmitted') {
                await notificationService.sendNotification({
                    type: types.adminImageResubmitted,
                    metaData: {
                        shoot_id: updatedImage.shoot_id,
                        url: encodeURLComponent(updatedImage.url),
                        preview_url: encodeURLComponent(updatedImage.preview_url),
                        feedback: updatedImage.feedback
                    }
                });
            }

            return apiService.formatResponse(h, {
                image: updatedImage
            });
        }
        catch (error) {
        }
    }
});
