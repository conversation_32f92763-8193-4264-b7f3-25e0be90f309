'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'images',
        description: 'Add a comment to an image, only the owner or an admin can comment.',
        response: Joi.object({
            comment: Joi.object().keys({
                id: Joi.number(),
                content: Joi.string(),
                user_id: Joi.number(),
                user_name: Joi.string(),
                image_id: Joi.number(),
                created_at: Joi.date().iso(),
                updated_at: Joi.date().iso()
            })
        })
    },
    method: 'POST',
    path: '/images/{id}/add-comment',
    auth: ['jwt', 'api-key'],
    validate: {
        params: Joi.object({
            id: Joi.string().required().description('The ID of the image to comment on.')
        }),
        payload: Joi.object({
            content: Joi.string().required().description('The comment content.')
        })
    },
    handler: async (request, h) => {

        const { contentService, apiService } = request.services();
        const { id } = request.params;
        const { user, tokenPayload: { role } } = request.auth.credentials;
        const { content  } = request.payload;
        const {  Image } = request.server.models();
        // Get user and image records
        const image = await Image.query().findById(id);
        if (!image) {
            return h.response({ message: `Image with id ${id} does not exist.` }).code(404);
        }

        if (!user.id || !user.name) {
            return h.response({ message: `User with id ${user.id} does not exist.` }).code(404);
        }

        // Only owner or admin can comment
        if (!(image.user_id === user.id || role === 'admin')) {
            return h.response({ message: 'Only the owner of the image or an admin can comment.' }).code(401);
        }

        // Add comment - the service now returns the comment with timestamps
        const comment = await contentService.addComment({ content, user_id: user.id, user_name: user.name, image_id: id });
        return apiService.formatResponse(h, { comment });
    }
});
