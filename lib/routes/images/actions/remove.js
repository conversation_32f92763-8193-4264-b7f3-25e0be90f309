'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'images',
        description: 'delete image metadata',
        response: Joi.object({
            success: Joi.boolean().example(true)
        })
    },
    method: 'DELETE',
    path: '/image/{id}/action/delete',
    auth: ['jwt', 'api-key'],
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const { apiService, contentService } = request.services();
        const { params: { id } } = request;
        const { user, tokenPayload: { role } } = request.auth.credentials;

        try {
            // Check if the user has permission to delete the image
            await contentService.checkUserImageAccess({ user, role, imageId: id });
            // Update the image status and feedback
            const { success } = await contentService.deleteImage({
                imageId: id
            });

            return apiService.formatResponse(h, {
                success
            });
        }
        catch (error) {
            console.error('Error updating image metadata:', error);
            // Handle the error
            return apiService.formatResponse(h, {
                error: 'Failed to delete image metadata'
            }, 500);
        }
    }
});
