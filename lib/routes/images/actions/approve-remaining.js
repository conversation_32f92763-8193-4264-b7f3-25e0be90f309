'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');
const { UnauthorizedError } = require('../../../helpers/errors');
const { image } = require('../../../validation-schema/shoots/responses.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'images',
        description: 'Approve remaining images',
        response: Joi.object({
            images: Joi.array().items(image)
        })
    },
    method: 'POST',
    path: '/image/action/approve-remaining',
    auth: ['jwt', 'api-key'],
    validate: {
        payload: Joi.object({
            shootId: Joi.number().required()
        })
    },
    handler: async (request, h) => {

        const { apiService, contentService } = request.services();
        const { payload: { shootId } } = request;
        const { user, tokenPayload: { role } } = request.auth.credentials;

        // Check if the approve remaining action is allowed for the user
        if (role !== 'client' && role !== 'admin') {
            throw new UnauthorizedError('Approve remaining action is not allowed for this user.');
        }

        // Approve all 'pending' images in the shoot with ownership check, but leave 'resubmitted' ones
        const updatedImages = await contentService.approveRemainingImages(shootId, user);

        return apiService.formatResponse(h, {
            images: updatedImages
        });
    }
});
