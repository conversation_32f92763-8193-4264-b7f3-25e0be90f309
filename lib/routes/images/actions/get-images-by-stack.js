'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'images',
        description: 'Get images by stack',
        disableResponseValidation: true
    },
    method: 'GET',
    path: '/images/stack/{stackId}',
    auth: ['jwt', 'api-key'],
    validate: {
        params: Joi.object({
            stackId: Joi.number().required().description('The ID of the stack.')
        })
    },
    handler: async (request, h) => {

        const { contentService, apiService } = request.services();
        const { stackId } = request.params;

        const images = await contentService.getImagesByStackId(stackId);
        return apiService.formatResponse(h, { images });
    }
});
