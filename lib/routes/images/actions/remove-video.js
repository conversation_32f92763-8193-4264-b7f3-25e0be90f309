'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'videos',
        description: 'delete video metadata',
        response: Joi.object({
            success: Joi.boolean().example(true)
        })
    },
    method: 'DELETE',
    path: '/video/{id}/action/delete',
    auth: ['jwt', 'api-key'],
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const { apiService, contentService } = request.services();
        const { params: { id } } = request;
        const { user, tokenPayload: { role } } = request.auth.credentials;

        try {
            // Check if the user has permission to delete the image
            await contentService.checkUserVideoAccess({ user, role, videoId: id });
            // Update the image status and feedback
            const { success } = await contentService.deleteVideo({
                videoId: id
            });

            return apiService.formatResponse(h, {
                success
            });
        }
        catch (error) {
            console.error('Error removing video metadata:', error);
            // Handle the error
            return apiService.formatResponse(h, {
                error: 'Failed to delete video metadata'
            }, 500);
        }
    }
});
