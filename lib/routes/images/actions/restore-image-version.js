'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'images',
        description: 'Restore an image version to a stack.',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/images/{id}/stack/restore',
    auth: ['jwt', 'api-key'],
    validate: {
        params: Joi.object({
            id: Joi.string().required().description('The ID of the image.')
        })
    },
    handler: async (request, h) => {

        const { contentService, apiService } = request.services();
        const { id } = request.params;

        await contentService.restoreImageVersion(id);
        return apiService.formatResponse(h, { success: true });
    }
});
