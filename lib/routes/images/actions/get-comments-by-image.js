'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'images',
        description: 'Get comments by image',
        disableResponseValidation: true
    },
    method: 'GET',
    path: '/images/{id}/comments',
    auth: ['jwt', 'api-key'],
    validate: {
        params: Joi.object({
            id: Joi.string().required().description('The ID of the image to comment on.')
        })
    },
    handler: async (request, h) => {

        const { contentService, apiService } = request.services();
        const { id } = request.params;
        const { Image } = request.server.models();
        // Get user and image records
        const image = await Image.query().findById(id);
        if (!image) {
            return h.response({ message: `Image with id ${id} does not exist.` }).code(404);
        }

        // Add comment - the service now returns the comment with timestamps
        const comments = await contentService.getCommentsByImageId(id);
        return apiService.formatResponse(h, { comments });
    }
});
