'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'images',
        description: 'Add a label to an image.',
        response: Joi.object({
            labels: Joi.array().items(Joi.string())
        })
    },
    method: 'POST',
    path: '/images/{id}/labels',
    auth: ['jwt', 'api-key'],
    validate: {
        params: Joi.object({
            id: Joi.string().required().description('The ID of the image to add the label to.')
        }),
        payload: Joi.object({
            label: Joi.string().required().description('The label to add to the image.')
        })
    },
    handler: async (request, h) => {

        const { contentService, apiService } = request.services();
        const { id } = request.params;
        const { label } = request.payload;
        const updatedLabels = await contentService.addLabelToImage(id, label);

        return apiService.formatResponse(h, { labels: updatedLabels });
    }
});
