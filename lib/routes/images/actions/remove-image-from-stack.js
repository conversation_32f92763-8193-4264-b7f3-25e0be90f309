'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');
const { image } = require('../../../validation-schema/shoots/responses.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'images',
        description: 'Remove an image from a stack.',
        response: Joi.object({
            image
        })
    },
    method: 'DELETE',
    path: '/images/{id}/stack',
    auth: ['jwt', 'api-key'],
    validate: {
        params: Joi.object({
            id: Joi.string().required().description('The ID of the image.')
        })
    },
    handler: async (request, h) => {

        const { contentService, apiService } = request.services();
        const { id } = request.params;
        const updatedImage = await contentService.removeImageFromStack(id);

        return apiService.formatResponse(h, { image: updatedImage });
    }
});
