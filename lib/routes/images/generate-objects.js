'use strict';

const Joi = require('joi');
const Boom = require('@hapi/boom');
const { createRoute } = require('../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'images',
        description: 'Generative fill (inpainting) for images using mask',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/images/{id}/generate-objects',
    auth: ['jwt', 'api-key'],
    options: {
        payload: {
            output: 'stream',
            parse: true,
            multipart: true,
            allow: 'multipart/form-data',
            maxBytes: 10 * 1024 * 1024
        }
    },
    validate: {
        params: Joi.object({
            id: Joi.number().integer().required()
        }),
        query: Joi.object({
            prompt: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const { apiService, replicateService } = request.services();
        const { image, mask } = request.payload;
        const { prompt } = request.query;

        try {
            // Call Replicate API for generative fill through the service
            const predictionId = await replicateService.generateObjects({
                image: image._data, mask: mask._data, prompt
            });

            return apiService.formatResponse(h, {
                prediction_id: predictionId,
                status: 'processing'
            });
        }
        catch (error) {
            request.log(['error'], error);

            if (error.isBoom) {
                throw error;
            }

            switch (error.code) {
                case 'AccessDenied':
                    throw Boom.forbidden('Access denied to S3 bucket. Please check bucket permissions.');
                case 'UNSUPPORTED_FILE_TYPE':
                    throw Boom.unsupportedMediaType('Invalid file type. Supported file types are jpeg, jpg, png, webp');
                case 'DOWNLOAD_ERROR':
                    throw Boom.preconditionFailed('Failed to download image');
                case 'RATE_LIMIT_EXCEEDED':
                    throw Boom.tooManyRequests('Request limit exceeded');
                case 'REPLICATE_API_ERROR':
                    throw Boom.badGateway('Failed to process image with Replicate API');
                default:
                    throw Boom.badImplementation('An error occurred while processing the image');
            }
        }
    }
});
