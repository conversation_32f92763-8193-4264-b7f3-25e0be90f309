'use strict';

const Joi = require('joi');
const Boom = require('@hapi/boom');
const { createRoute } = require('../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'images',
        description: 'Remove objects from images using manual brush mask',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/images/{id}/remove-objects',
    auth: ['jwt', 'api-key'],
    options: {
        payload: {
            output: 'stream',
            parse: true,
            multipart: true,
            allow: 'multipart/form-data',
            maxBytes: 10 * 1024 * 1024
        }
    },
    validate: {
        params: Joi.object({
            id: Joi.number().integer().required()
        })
    },
    handler: async (request, h) => {

        const { apiService, replicateService } = request.services();

        try {

            // Call Replicate API for object removal through the service
            const predictionId = await replicateService.removeObject({
                image: request.payload.image._data,
                mask: request.payload.mask._data
            });

            return apiService.formatResponse(h, {
                prediction_id: predictionId,
                status: 'processing'
            });
        }
        catch (error) {
            request.log(['error'], error);

            if (error.isBoom) {
                throw error;
            }

            switch (error.code) {
                case 'AccessDenied':
                    throw Boom.forbidden('Access denied to S3 bucket. Please check bucket permissions.');
                case 'UNSUPPORTED_FILE_TYPE':
                    throw Boom.unsupportedMediaType('Invalid file type. Supported file types are jpeg, jpg, png, webp');
                case 'DOWNLOAD_ERROR':
                    throw Boom.preconditionFailed('Failed to download image');
                case 'RATE_LIMIT_EXCEEDED':
                    throw Boom.tooManyRequests('Request limit exceeded');
                case 'REPLICATE_API_ERROR':
                    throw Boom.badGateway('Failed to process image with Replicate API');
                default:
                    throw Boom.badImplementation('An error occurred while processing the image');
            }
        }
    }
});
