'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'images',
        description: 'Return images based on metadata and visual search criteria with infinite scroll pagination',
        response: Joi.object({
            success: Joi.boolean().required(),
            cookies: Joi.object().required()
        })
    },
    method: 'POST',
    path: '/contents/signed-cookies',
    auth: ['jwt'],
    validate: {
        payload: Joi.object({
            url: Joi.string().required()
        }).required()
    },
    handler: async (request, h) => {

        const { apiService, contentService } = request.services();
        const { payload: { url } } = request;

        try {
            const urlPattern = contentService.extractUrlPattern(url);
            const cookies = await contentService.getSignedCookies(urlPattern);

            const result = {
                success: true,
                cookies
            };

            return apiService.formatResponse(h, result);
        }
        catch (error) {
            console.error(error);
            request.logger.error({ error }, 'Failed to get signed cookies');
            return apiService.formatResponse(h, null, 500, ['Error']);
        }

    }
});
