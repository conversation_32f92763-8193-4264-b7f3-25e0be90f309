'use strict';

const Joi = require('joi');
const Boom = require('@hapi/boom');
const { createRoute } = require('../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'images',
        description: 'Outpaints an image using flux fill pro',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/images/{id}/outpaint',
    auth: ['jwt', 'api-key'],
    options: {
        payload: {
            output: 'stream',
            parse: true,
            multipart: true,
            allow: 'multipart/form-data',
            maxBytes: 10 * 1024 * 1024
        }
    },
    validate: {
        params: Joi.object({
            id: Joi.number().integer().required()
        }),
        query: Joi.object({
            // eslint-disable-next-line max-len
            outpaint: Joi.string().valid('None', 'Zoom out 1.5x', 'Zoom out 2x', 'Make square', 'Left outpaint', 'Right outpaint', 'Top outpaint', 'Bottom outpaint'),
            prompt: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const { apiService, replicateService } = request.services();

        try {

            // Call Replicate API for object removal through the service
            const predictionId = await replicateService.outpaint({
                image: request.payload.image._data,
                outpaint: request.query.outpaint,
                prompt: request.query.prompt
            });

            return apiService.formatResponse(h, {
                prediction_id: predictionId,
                status: 'processing'
            });
        }
        catch (error) {
            request.log(['error'], error);

            if (error.isBoom) {
                throw error;
            }

            switch (error.code) {
                case 'AccessDenied':
                    throw Boom.forbidden('Access denied to S3 bucket. Please check bucket permissions.');
                case 'UNSUPPORTED_FILE_TYPE':
                    throw Boom.unsupportedMediaType('Invalid file type. Supported file types are jpeg, jpg, png, webp');
                case 'DOWNLOAD_ERROR':
                    throw Boom.preconditionFailed('Failed to download image');
                case 'RATE_LIMIT_EXCEEDED':
                    throw Boom.tooManyRequests('Request limit exceeded');
                case 'REPLICATE_API_ERROR':
                    throw Boom.badGateway('Failed to process image with Replicate API');
                default:
                    throw Boom.badImplementation('An error occurred while processing the image');
            }
        }
    }
});
