'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');
const { UnauthorizedError } = require('../../../helpers/errors');
const { types } = require('../../../services/NotificationService');

module.exports = createRoute({
    documentation: {
        resourceTag: 'orders',
        description: 'checkout an order',
        response: Joi.object({
            payment_intent_id: Joi.string(),
            payment_client_secret: Joi.string(),
            status: Joi.string(),
            invoice_url: Joi.string()
        })
    },
    method: 'POST',
    path: '/order/{order_id}/action/checkout',
    auth: ['jwt'],
    validate: {
        params: Joi.object({
            order_id: Joi.string().required()
        }),
        payload: Joi.object({
            shoot_id: Joi.string(), //TODO extend support for arrays
            payment_method_id: Joi.string(),
            payment_intent_id: Joi.string(),
            notes: Joi.string()
        })
    },
    handler: async (request, h) => {

        const { apiService, authService, paymentService, orderService, shootService, shootStatusService,
            userService, notificationService } = request.services();

        const {
            payload: {
                shoot_id,
                payment_method_id,
                payment_intent_id,
                notes
            },
            params: { order_id }
        } = request;
        const { user, tokenPayload: { role } } = request.auth.credentials;

        authService.checkRouteAuthorization({ role, route: 'checkout-shoot' });
        //check order
        const { order } = await orderService.getOrder({ id: order_id, user, role });

        //check shoot
        const { shoot } = await shootService.getShoot({ id: shoot_id, user, role });

        //check stripe customer id
        const { details: { stripe_customer_id } } = await userService.getPaymentDetails({ user });

        if (order.payment_confirmed === true) {
            throw new UnauthorizedError('Action can not be performed');
        }

        //payment process
        const { intent, invoice, billing_address } = await paymentService.performPayment({
            payment_method_id,
            payment_intent_id,
            amount: order.price,
            stripe_customer_id,
            description: shoot?.name || 'shoot' });

        const { id, client_secret: payment_client_secret, status } = intent;

        if (status === 'succeeded') {
            //updates shoot status
            await shootStatusService.updatePayedShootStatus({ shoot, user, role });

            await shootService.updateShoot({
                shoot,
                role,
                user,
                newValues: {
                    notes
                }
            });

            //updates order
            await orderService.updateOrder({
                id: order.id,
                user,
                role,
                newValues: {
                    purchase_type: 'stripe-card',
                    payment_confirmed: true,
                    payment_date: new Date(),
                    billing_address
                }
            });

            const metaData = {
                shootId: shoot.id,
                invoiceLink: invoice.invoice_pdf
            };
            notificationService.sendNotification({ type: types.invoice, target: user, metaData });
        }

        return apiService.formatResponse(h, {
            payment_intent_id: id,
            payment_client_secret,
            status,
            invoice_url: invoice.invoice_pdf
        });
    }
});
