'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { collection } = require('../../validation-schema/collections/responses.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'collections',
        description: 'retrieves all collections for the current user with item counts',
        response: Joi.object({
            collections: Joi.array().items(
                collection.keys({
                    item_count: Joi.number().integer().min(0)
                })
            )
        })
    },
    method: 'GET',
    path: '/collections',
    auth: ['jwt'],
    handler: async (request, h) => {

        const { collectionService, apiService, authService } = request.services();
        const { tokenPayload: { id: userId, role } } = request.auth.credentials;
        await authService.checkRouteAuthorization({ role, route: 'manage-collections' });

        // Get all collections for the user with item counts
        const collections = await collectionService.getUserCollections(userId);

        return apiService.formatResponse(h, {
            collections
        });
    }
});
