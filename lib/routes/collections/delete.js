'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'collections',
        description: 'deletes a collection and its associations',
        response: Joi.object({
            success: Joi.boolean().example(true)
        })
    },
    method: 'DELETE',
    path: '/collections/{id}',
    auth: ['jwt'],
    validate: {
        params: Joi.object({
            id: Joi.number().integer().required().description('Collection ID')
        })
    },
    handler: async (request, h) => {

        const { collectionService, apiService, authService } = request.services();
        const { id } = request.params;
        const { tokenPayload: { id: userId, role } } = request.auth.credentials;
        await authService.checkRouteAuthorization({ role, route: 'manage-collections' });

        // Delete the collection and its associations
        await collectionService.deleteCollection(id, userId);

        return apiService.formatResponse(h, {
            success: true
        });
    }
});
