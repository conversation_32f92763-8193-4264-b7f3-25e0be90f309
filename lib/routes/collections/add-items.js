'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { collection } = require('../../validation-schema/collections/responses.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'collections',
        description: 'Add multiple images to a collection with duplicate prevention',
        response: Joi.object({
            collection: collection.keys({
                item_count: Joi.number().integer()
            })
        })
    },
    method: 'POST',
    path: '/collections/{id}/items',
    auth: ['jwt'],
    validate: {
        params: Joi.object({
            id: Joi.number().integer().required().description('The ID of the collection')
        }),
        payload: Joi.object({
            content: Joi.array().items(
                Joi.object({
                    id: Joi.number().required().description('The ID of the image to add to the collection'),
                    type: Joi.string().valid('image', 'video').required().description('The type of content (image or video)')
                })
            )
        })
    },
    handler: async (request, h) => {

        const { collectionService, apiService, authService } = request.services();
        const { id: collectionId } = request.params;
        const { content } = request.payload;
        const { tokenPayload: { id: userId, role } } = request.auth.credentials;
        await authService.checkRouteAuthorization({ role, route: 'manage-collections' });

        const data = content.reduce((acc, { id, type }) => {

            if (!acc[type]) {
                acc[type] = [];
            }

            acc[type].push(id);
            return acc;
        }, {});

        if (!data.image && !data.video) {
            return apiService.formatResponse(h, {
                error: 'No images or videos provided'
            }, 400);
        }

        let updatedImageCollection = null; let updatedVideoCollection = null;

        if (data.image && data.image.length > 0) {
            updatedImageCollection = await collectionService.addItemsToCollection(collectionId, userId, data.image, 'image');
        }

        if (data.video && data.video.length > 0) {
            updatedVideoCollection = await collectionService.addItemsToCollection(collectionId, userId, data.video, 'video');
        }

        return apiService.formatResponse(h, {
            collection: {
                item_count: updatedImageCollection?.item_count || updatedVideoCollection?.item_count || 0
            }
        });
    }
});
