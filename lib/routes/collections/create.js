'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { collection } = require('../../validation-schema/collections/responses.js');
const { create } = require('../../validation-schema/collections/payloads.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'collections',
        description: 'creates a collection',
        response: Joi.object({
            collection
        })
    },
    method: 'POST',
    path: '/collections',
    auth: ['jwt'],
    validate: {
        payload: create
    },
    handler: async (request, h) => {

        const { collectionService, apiService, authService } = request.services();
        const { name } = request.payload;
        const { tokenPayload: { id: userId, role } } = request.auth.credentials;
        await authService.checkRouteAuthorization({ role, route: 'manage-collections' });
        // Create the collection
        const newCollection = await collectionService.createCollection(userId, name);

        return apiService.formatResponse(h, {
            collection: newCollection
        });
    }
});
