'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'collections',
        description: 'Remove multiple images from a collection',
        response: Joi.object({
            success: Joi.boolean().example(true),
            removed_count: Joi.number().integer().example(3)
        })
    },
    method: 'DELETE',
    path: '/collections/{id}/items',
    auth: ['jwt'],
    validate: {
        params: Joi.object({
            id: Joi.number().integer().required().description('The ID of the collection')
        }),
        payload: Joi.object({
            image_ids: Joi.array().items(Joi.number().integer()).min(1).required()
                .description('Array of image IDs to remove from the collection')
        })
    },
    handler: async (request, h) => {

        const { collectionService, apiService, authService } = request.services();
        const { id: collectionId } = request.params;
        const { image_ids: imageIds } = request.payload;
        const { tokenPayload: { id: userId, role } } = request.auth.credentials;
        await authService.checkRouteAuthorization({ role, route: 'manage-collections' });

        // Remove images from the collection
        const result = await collectionService.removeItemsFromCollection(collectionId, userId, imageIds);

        return apiService.formatResponse(h, {
            success: true,
            removed_count: result.removedCount
        });
    }
});
