'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { collection } = require('../../validation-schema/collections/responses.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'collections',
        description: 'updates a collection',
        response: Joi.object({
            collection
        })
    },
    method: 'PUT',
    path: '/collections/{id}',
    auth: ['jwt'],
    validate: {
        params: Joi.object({
            id: Joi.number().integer().required().description('Collection ID')
        }),
        payload: Joi.object({
            name: Joi.string().required().description('New collection name')
        })
    },
    handler: async (request, h) => {

        const { collectionService, apiService, authService } = request.services();
        const { id } = request.params;
        const { name } = request.payload;
        const { tokenPayload: { id: userId, role } } = request.auth.credentials;
        await authService.checkRouteAuthorization({ role, route: 'manage-collections' });

        // Update the collection
        const updatedCollection = await collectionService.updateCollection(id, userId, { name });

        return apiService.formatResponse(h, {
            collection: updatedCollection
        });
    }
});
