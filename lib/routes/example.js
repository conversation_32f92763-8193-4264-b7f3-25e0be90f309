'use strict';

const Joi = require('joi');
const { createRoute } = require('../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'example',
        description: 'example route under authentication',
        response: Joi.object({
            credentials: Joi.object(),
            path: Joi.string()
        })
    },
    method: 'GET',
    path: '/example',
    auth: ['jwt', 'api-key'],
    handler: (request, h) => {

        const { apiService } = request.services();

        return apiService.formatResponse(h, {
            credentials: request.auth.credentials
        });
    }
});
