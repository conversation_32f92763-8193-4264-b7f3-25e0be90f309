'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'settings',
        description: 'returns static settings',
        response: Joi.object({
            pictures_static_url: Joi.string(),
            stripe_public: Joi.string()
        })
    },
    method: 'GET',
    path: '/settings',
    auth: false,
    validate: {

    },
    handler: (request, h) => {

        const { apiService, storageService } = request.services();

        const pictures_static_url = storageService.getPicturesStaticUrl();

        const { STRIPE_PUBLIC_ACCESS_KEY: stripe_public } = process.env;

        return apiService.formatResponse(h,
            {
                pictures_static_url,
                stripe_public
            }
        );
    }
});
