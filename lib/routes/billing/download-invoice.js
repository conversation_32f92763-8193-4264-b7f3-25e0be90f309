
'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'billings',
        description: 'downloads an invoice by ID',
        response: Joi.object({
            invoice_url: Joi.string().optional().allow(null)
        })
    },
    method: 'GET',
    path: '/billings/invoice/{subscriptionId}',
    auth: ['jwt'],
    options: {
        validate: {
            params: Joi.object({
                subscriptionId: Joi.string().required()
            })
        }
    },
    handler: async (request, h) => {

        const { authService, stripeService, apiService } = request.services();
        const { subscriptionId } = request.params;
        const { tokenPayload: { role } } = request.auth.credentials;
        authService.checkRouteAuthorization({ role, route: 'billing-management' });

        try {
            // Get the invoice to verify ownership
            const invoice = await stripeService.getLatestInvoiceBySubscription(subscriptionId);

            if (!invoice) {
                // Return the invoice PDF URL
                return apiService.formatResponse(h, {
                    invoice_url: null
                });
            }

            // Return the invoice PDF URL
            return apiService.formatResponse(h, {
                invoice_url: invoice.pdf_url
            });
        }
        catch (error) {
            console.error(`Error downloading invoice: ${error.message}`);
            return apiService.formatResponse(h, {
                error: 'Failed to download invoice'
            }, 500);
        }
    }
});
