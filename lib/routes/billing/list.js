'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const Billing = require('../../models/Billing');

module.exports = createRoute({
    documentation: {
        resourceTag: 'billings',
        description: 'retrieves all billings for the current user with item counts',
        response: Joi.object({
            billings: Joi.array().items(Joi.object(Billing.JoiSchema))
        })
    },
    method: 'GET',
    path: '/billings',
    auth: ['jwt'],
    handler: async (request, h) => {

        const { authService, subscriptionService, apiService } = request.services();
        const { user, tokenPayload: { role } } = request.auth.credentials;
        authService.checkRouteAuthorization({ role, route: 'billing-management' });

        // Get all collections for the user with item counts
        const billings = await subscriptionService.getBillingHistory({ user });

        return apiService.formatResponse(h, {
            billings
        });
    }
});
