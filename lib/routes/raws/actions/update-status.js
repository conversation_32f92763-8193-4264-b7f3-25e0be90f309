'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');
const { rawImage } = require('../../../validation-schema/shoots/responses.js'); // Make sure to define 'rawImage' schema

module.exports = createRoute({
    documentation: {
        resourceTag: 'raws',
        description: 'Updates raw image status',
        response: Joi.object({
            rawImage
        })
    },
    method: 'POST',
    path: '/raw/{id}/action/update-status',
    auth: ['jwt', 'api-key'],
    validate: {
        payload: Joi.object({
            newStatus: Joi.string().valid('pending', 'selected', 'not-selected').required(),
            notes: Joi.string().optional().allow(null)
        }),
        params: Joi.object({
            id: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const { apiService, contentService } = request.services();
        const { params: { id }, payload: { newStatus, notes } } = request;

        try {
            // Update the raw image status and optional notes
            const updatedRawImage = await contentService.updateRawImageStatus({
                imageId: id,
                newStatus,
                notes
            });
            return apiService.formatResponse(h, {
                rawImage: updatedRawImage
            });
        }
        catch (error) {
            // Error handling logic here
        }
    }
});
