'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { shoot }  = require('../../validation-schema/shoots/responses.js');
const { search } = require('../../validation-schema/shoots/payloads.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'return shoots',
        response: Joi.object({
            shoots: Joi.array().items(shoot).required(),
            total: Joi.number().integer().required()
        })
    },
    method: 'POST',
    path: '/shoots/search',
    auth: ['jwt', 'api-key'],
    validate: {
        payload: search
    },
    handler: async (request, h) => {

        const { apiService, shootService } = request.services();

        const {
            filters: {
                date = {},
                services,
                statuses,
                cities,
                searchQuery
            },
            pagination: {
                size, offset
            }
        } = request.payload;
        const { user, tokenPayload: { role } } = request.auth.credentials;
        // changed something

        const { shoots, total } = await shootService.getShoots({
            limit: size,
            offset,
            starts_at: date.from,
            ends_at: date.to,
            services,
            statuses,
            cities,
            user,
            role,
            searchQuery
        });

        return apiService.formatResponse(h, {
            shoots,  total
        });
    }
});
