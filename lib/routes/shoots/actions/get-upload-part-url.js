// init-multipart-upload.js
'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');
const { NotFoundError, rethrow } = require('../../../helpers/errors');
const Shoot = require('../../../models/Shoot');

// get-upload-part-url.js
module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'Get presigned URL for uploading a part',
        response: Joi.object({
            presignedUrl: Joi.string().required()
        })
    },
    method: 'GET',
    path: '/shoot/{id}/action/upload-part-url',
    auth: ['jwt', 'api-key'],
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        }),
        query: Joi.object({
            uploadId: Joi.string().required(),
            partNumber: Joi.number().required().min(1).max(10000),
            key: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const { apiService, shootService, authService, storageService } = request.services();
        const { params: { id }, query, server } = request;
        const { user, tokenPayload: { role } } = request.auth.credentials;

        try {
            const { shoot } = await shootService.getShoot({ id, user, role })
                .catch(rethrow(NotFoundError, 'Unable to find the shoot'));

            await authService.checkShootAuthorization({
                shoot,
                role,
                action: 'upload-raw',
                photographer_id: user.photographer_id || null,
                allowedRoles: ['admin', 'photographer'],
                allowedStatuses: [Shoot.statuses.confirmed, Shoot.statuses.rawsUploaded]
            });

            const presignedUrl = await storageService.getMultipartPresignedUrl({
                bucket: server.settings.app.amazon.raw_photos_bucket_name,
                key: query.key,
                uploadId: query.uploadId,
                partNumber: query.partNumber,
                expires: 3600
            });

            return apiService.formatResponse(h, { presignedUrl });
        }
        catch (error) {
            throw new Error(`Failed to get part upload URL:${error}`);
        }
    }
});
