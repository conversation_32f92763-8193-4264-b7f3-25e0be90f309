'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');
const { NotFoundError, rethrow } = require('../../../helpers/errors');
const Shoot = require('../../../models/Shoot');

module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'Enqueue a download job for shoot content',
        response: Joi.object({
            id: Joi.number().integer().optional(),
            shootId: Joi.number().integer().optional(),
            status: Joi.string().optional(),
            message: Joi.string().optional(),
            downloadUrl: Joi.string().optional().allow(''),
            createdAt: Joi.date().iso().optional().allow(null),
            updatedAt: Joi.date().iso().optional().allow(null),
            progress: Joi.number().optional()
        })
    },
    method: 'POST',
    path: '/shoot/{id}/action/enqueue-download-job',
    auth: 'jwt',
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const {
            downloadContentService,
            apiService,
            authService,
            shootService
        } = request.services();

        const { user, tokenPayload: { role } } = request.auth.credentials;
        const { params: { id } } = request;

        // Check general route authorization
        authService.checkRouteAuthorization({
            role,
            route: 'enqueue-download-job'
        });

        // Get and verify shoot
        const { shoot } = await shootService.getShoot({ id, user, role })
            .catch(rethrow(NotFoundError, 'Unable to find the shoot'));

        // Check shoot-specific authorization
        await authService.checkShootAuthorization({
            shoot,
            role,
            action: 'download-processed',
            allowedRoles: ['admin', 'client'],
            allowedStatuses: [
                Shoot.statuses.photosReady,
                Shoot.statuses.completed,
                Shoot.statuses.photosUploaded
            ]
        });

        try {
            // Enqueue the download job
            const jobRecord = await downloadContentService.enqueueDownloadJob({
                shootId: shoot.id,
                message: `Download requested by ${user.email}`
            });

            jobRecord.shootId = shoot.id;
            // Return the formatted job record
            return apiService.formatResponse(h, jobRecord);
        }
        catch (error) {
            request.logger.error('Error enqueueing download job:', error);
            return apiService.error(h, 400, [error.message]);
        }
    }
});
