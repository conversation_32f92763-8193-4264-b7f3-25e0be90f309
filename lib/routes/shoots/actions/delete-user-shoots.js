'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');
const { NotFoundError, rethrow, ValidationError } = require('../../../helpers/errors');

module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'deletes all shoots related to a user',
        response: Joi.object({
            status: Joi.string().required()
        })
    },
    method: 'DELETE',
    path: '/shoot/{user_id}/action/shoots',
    auth: 'jwt',
    validate: {
        params: Joi.object({
            user_id: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const { apiService, shootService, authService, userService, mondayService } = request.services();

        const { user_id } = request.params;

        const user = await userService
            .getUser({ id: user_id })
            .catch(rethrow(NotFoundError, 'Shoots can not be canceled due to non-existent user'));

        if (user.role !== 'client') {
            throw new ValidationError('Shoots can only be canceled for clients');
        }

        const { role: loggedRole } = request.auth.credentials.tokenPayload;

        authService.checkRouteAuthorization({ role: loggedRole, route: 'delete-user-shoots' });

        const { shootIds, possibleUploadedShootIds } = await shootService.deleteShootsByUser({ user, loggedRole });

        await shootService.deleteAllStorageResourcesByShootIds({ shoot_ids: possibleUploadedShootIds });

        await mondayService.deleteShoots({ shoot_ids: shootIds });

        return apiService.formatResponse(h, {
            status: 'success'
        });
    }
});
