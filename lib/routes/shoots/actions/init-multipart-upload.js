// init-multipart-upload.js
'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');
const { NotFoundError, rethrow } = require('../../../helpers/errors');
const Shoot = require('../../../models/Shoot');

module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'Initialize multipart upload for RAW content',
        response: Joi.object({
            uploadId: Joi.string().required(),
            key: Joi.string().required()
        })
    },
    method: 'POST',
    path: '/shoot/{id}/action/init-multipart-upload',
    auth: ['jwt', 'api-key'],
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        })

    },
    handler: async (request, h) => {

        const { apiService, shootService, authService, storageService } = request.services();
        const { params: { id },  server } = request;
        const { user, tokenPayload: { role } } = request.auth.credentials;

        try {
            const { shoot } = await shootService.getShoot({ id, user, role })
                .catch(rethrow(NotFoundError, 'Unable to find the shoot'));

            await authService.checkShootAuthorization({
                shoot,
                role,
                action: 'upload-raw',
                photographer_id: user.photographer_id || null,
                allowedRoles: ['admin', 'photographer'],
                allowedStatuses: [Shoot.statuses.confirmed, Shoot.statuses.rawsUploaded]
            });

            const key = `${shoot.id}/raw-photos`;
            const result = await storageService.initiateMultipartUpload({
                bucket: server.settings.app.amazon.raw_photos_bucket_name,
                key
            });

            return apiService.formatResponse(h, result);
        }
        catch (error) {
            throw new Error(`Failed to init multipart upload URL:${error}`);
        }
    }
});
