'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');
const { NotFoundError, rethrow, ValidationError } = require('../../../helpers/errors');
const { shootDetail } = require('../../../validation-schema/shoots/responses.js');
const Shoot = require('../../../models/Shoot');

module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'assigns photographer to shoot',
        response: Joi.object({
            shootDetail
        })
    },
    method: 'POST',
    path: '/shoot/{id}/action/assign-photographer',
    auth: 'jwt',
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        }),
        payload: Joi.object({
            photographer_id: Joi.number().integer().required()
        })
    },
    handler: async (request, h) => {

        try {
            const { apiService, shootService, authService, userService, mondayService } = request.services();

            const {
                params: {
                    id
                },
                payload: {
                    photographer_id
                }
            } = request;

            const { user, tokenPayload: { role } } = request.auth.credentials;

            await userService
                .getPhotographerById({ photographer_id })
                .catch(rethrow(ValidationError, 'Shoot can not be assigned to non-existent photographer'));

            const { shoot } = await shootService.getShoot({ id, user, role })
                .catch(rethrow(NotFoundError, 'Unable to find the shoot'));

            authService.checkShootAuthorization({
                shoot,
                role,
                action: 'assign-photographer',
                allowedRoles: ['admin'],
                allowedStatuses: [Shoot.statuses.scheduled, Shoot.statuses.photographerAssigned, Shoot.statuses.confirmed]
            });

            const updatedShoot = await shootService.updateShoot({ shoot, user, role, newValues: { photographer_id } });

            if (process.env.NODE_ENV !== 'test') {
                await mondayService.updateShoot({
                    shoot_id: updatedShoot.id, boardType: updatedShoot.type === 'express' ? 'FLASHYSHOOTS' : 'BESPOKE'
                });
            }

            return apiService.formatResponse(h, {
                shootDetail: updatedShoot
            });
        }
        catch (err) {
            throw new Error(`Failed to assign a photographer: ${err}`);
        }
    }
});
