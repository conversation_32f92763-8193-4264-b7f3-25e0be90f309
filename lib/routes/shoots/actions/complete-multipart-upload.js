// init-multipart-upload.js
'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');
const { NotFoundError, rethrow } = require('../../../helpers/errors');
const Shoot = require('../../../models/Shoot');

// complete-multipart-upload.js
module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'Complete multipart upload',
        response: Joi.object({
            location: Joi.string().required(),
            bucket: Joi.string().required(),
            key: Joi.string().required()
        })
    },
    method: 'POST',
    path: '/shoot/{id}/action/complete-multipart-upload',
    auth: ['jwt', 'api-key'],
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        }),
        payload: Joi.object({
            uploadId: Joi.string().required(),
            key: Joi.string().required(),
            parts: Joi.array().items(Joi.object({
                PartNumber: Joi.number().required(),
                ETag: Joi.string().required()
            })).required()
        })
    },
    handler: async (request, h) => {

        const { apiService, shootService, authService, storageService } = request.services();
        const { params: { id }, payload, server } = request;
        const { user, tokenPayload: { role } } = request.auth.credentials;

        try {
            const { shoot } = await shootService.getShoot({ id, user, role })
                .catch(rethrow(NotFoundError, 'Unable to find the shoot'));

            await authService.checkShootAuthorization({
                shoot,
                role,
                action: 'upload-raw',
                photographer_id: user.photographer_id || null,
                allowedRoles: ['admin', 'photographer'],
                allowedStatuses: [Shoot.statuses.confirmed, Shoot.statuses.rawsUploaded]
            });

            const result = await storageService.completeMultipartUpload({
                bucket: server.settings.app.amazon.raw_photos_bucket_name,
                key: payload.key,
                uploadId: payload.uploadId,
                parts: payload.parts
            });

            const response = {
                location: result.Location,
                bucket: result.Bucket,
                key: result.Key
            };

            return apiService.formatResponse(h, response);
        }
        catch (error) {
            throw new Error(`Failed to complete multipart upload:${error}`);
        }
    }
});
