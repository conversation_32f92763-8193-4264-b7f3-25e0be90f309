'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');
const { NotFoundError, UnauthorizedError, rethrow } = require('../../../helpers/errors');
const { shootDetail }  = require('../../../validation-schema/shoots/responses.js');

const Shoot = require('../../../models/Shoot');

module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'updates shoot status',
        response: Joi.object({
            shootDetail
        })
    },
    method: 'POST',
    path: '/shoot/{id}/action/update-status',
    auth: ['jwt', 'api-key'],
    validate: {
        payload: Joi.object({
            targetStatus: Joi.string().required()
        }),
        params: Joi.object({
            id: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const { apiService, shootService, shootStatusService,
            reportUserService, mondayService } = request.services();

        const { params: { id }, payload: { targetStatus } } = request;
        const { user, tokenPayload: { role } } = request.auth.credentials;
        const { shoot } = await shootService.getShoot({ id, user, role }).catch(rethrow(NotFoundError, 'Unable to find the shoot'));

        const starting_status = shoot.status;
        const isUpdateGranted = await shootStatusService.isLegitTransition({ shoot, user, role, targetStatus });
        if (!isUpdateGranted) {
            throw new UnauthorizedError('Update can not be performed');
        }

        const updatedShoot = await shootService.updateShoot({
            shoot,
            role,
            user,
            newValues: {
                status: targetStatus,
                ...(targetStatus === Shoot.statuses.confirmed && role === 'photographer' && { photographer_id: user.photographer_id })
            }
        });

        if (process.env.NODE_ENV !== 'test') {

            await mondayService.updateShoot({
                shoot_id: updatedShoot.id, boardType: updatedShoot.type === 'express' ? 'FLASHYSHOOTS' : 'BESPOKE'
            });

            if (targetStatus === Shoot.statuses.canceled) {
                await mondayService.cancelShoot({
                    shoot_id: updatedShoot.id, boardType: updatedShoot.type === 'express' ? 'FLASHYSHOOTS' : 'BESPOKE'
                });
            }
        }

        shootService.handleStatusChangeNotification({ oldShoot: shoot, newShoot: updatedShoot, user, role });

        if (request.auth.strategy !== 'api-key') {
            // NOTE: we do not track actions performed through API KEYs, since they do not have an associate user
            // TODO: remove this condition when the api-key auth strategy will link to existing users in the db
            reportUserService.createReportUser( { shoot, performer_user_id: user.id,
                target_photographer_id: shoot.photographer_id, starting_status, target_status: targetStatus } );
        }

        return apiService.formatResponse(h, {
            shootDetail: updatedShoot
        });
    }
});
