'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');
const { NotFoundError,  rethrow } = require('../../../helpers/errors');
const { shootDetail }  = require('../../../validation-schema/shoots/responses.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'Updates images_preview property for a shoot',
        response: Joi.object({
            shootDetail
        })
    },
    method: 'POST',
    path: '/shoot/{id}/action/update-preview',
    auth: ['jwt', 'api-key'],
    validate: {
        payload: Joi.object({
            imagesPreview: Joi.boolean().required()
        }),
        params: Joi.object({
            id: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const { shootService, apiService } = request.services();
        const { params: { id }, payload: { imagesPreview } } = request;
        const { user, tokenPayload: { role } } = request.auth.credentials;

        // Get the shoot by ID

        const { shoot } = await shootService.getShoot({ id, user, role }).catch(rethrow(NotFoundError, 'Unable to find the shoot'));

        // Update the images_preview property
        const updatedShoot = await shootService.updateShoot({
            shoot,
            newValues: {
                images_preview: imagesPreview
            }
        });

        return apiService.formatResponse(h, {
            shootDetail: updatedShoot
        });
    }
});
