'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');
const { NotFoundError, rethrow } = require('../../../helpers/errors');
const Shoot = require('../../../models/Shoot');

// abort-multipart-upload.js
module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'Abort multipart upload',
        response: Joi.object({
            message: Joi.string().required()
        })
    },
    method: 'POST',
    path: '/shoot/{id}/action/abort-multipart-upload',
    auth: ['jwt', 'api-key'],
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        }),
        payload: Joi.object({
            uploadId: Joi.string().required(),
            key: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const { apiService, shootService, authService, storageService } = request.services();
        const { params: { id }, payload, server } = request;
        const { user, tokenPayload: { role } } = request.auth.credentials;

        try {
            const { shoot } = await shootService.getShoot({ id, user, role })
                .catch(rethrow(NotFoundError, 'Unable to find the shoot'));

            await authService.checkShootAuthorization({
                shoot,
                role,
                action: 'upload-raw',
                photographer_id: user.photographer_id || null,
                allowedRoles: ['admin', 'photographer'],
                allowedStatuses: [Shoot.statuses.confirmed, Shoot.statuses.rawsUploaded]
            });

            await storageService.abortMultipartUpload({
                bucket: server.settings.app.amazon.raw_photos_bucket_name,
                key: payload.key,
                uploadId: payload.uploadId
            });

            return apiService.formatResponse(h, {
                message: 'Multipart upload aborted successfully'
            });
        }
        catch (error) {
            throw new Error(`Failed to abort multipart upload:${error}`);
        }
    }
});
