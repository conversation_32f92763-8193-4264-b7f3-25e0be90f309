'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory.js');
const { NotFoundError, rethrow } = require('../../../helpers/errors.js');
const { shootDetail } = require('../../../validation-schema/shoots/responses.js');
const { types } = require('../../../services/NotificationService.js');

const encodeURLComponent = (str) => encodeURIComponent(str).replace(/%2F/g, '/').replace(/%3A/g, ':');

module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'Updates raws_property property for a shoot',
        response: Joi.object({
            shootDetail
        })
    },
    // changed something hello world
    method: 'POST',
    path: '/shoot/{id}/action/update-raws-preview',
    auth: ['jwt', 'api-key'],
    validate: {
        payload: Joi.object({
            rawsPreview: Joi.boolean().required()
        }),
        params: Joi.object({
            id: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const { shootService, apiService, notificationService } = request.services();
        const { params: { id }, payload: { rawsPreview } } = request;
        const { user, tokenPayload: { role } } = request.auth.credentials;
        try {
            // Get the shoot by ID
            const { shoot } = await shootService.getShoot({ id, user, role }).catch(rethrow(NotFoundError, 'Unable to find the shoot'));

            // Update the raws_selection property
            const updatedShoot = await shootService.updateShoot({
                shoot,
                newValues: {
                    raws_preview: rawsPreview
                }
            });

            if (role === 'client') {
                const selectedRaws = await updatedShoot.raws.filter((raw) => raw.status === 'selected');
                // Transform selectedRaws into plain data
                const plainSelectedRaws = await selectedRaws.map((item) => {

                    return {
                        id: item.id,
                        shoot_id: item.shoot_id,
                        url: encodeURLComponent(item.url),
                        preview_url: encodeURLComponent(item.preview_url),
                        placeholder_url: encodeURLComponent(item.placeholder_url),
                        filename: item.filename,
                        folder: item.folder,
                        filepath: item.filepath,
                        status: item.status,
                        notes: item.notes ? item.notes : 'No editing notes'
                    };
                });

                await notificationService.sendNotification({
                    type: types.adminRawsSelection,
                    metaData: {
                        shoot_id: updatedShoot.id,
                        raws: plainSelectedRaws
                    }
                });
            }

            return apiService.formatResponse(h, {
                shootDetail: updatedShoot
            }, 200);

        }
        catch (error) {
            // Log the error for debugging and traceability
            return apiService.error(h, 500, 'Failed to update raws selection');
        }
    }
});
