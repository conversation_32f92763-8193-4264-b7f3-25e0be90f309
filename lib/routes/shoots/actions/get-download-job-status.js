'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');
const { NotFoundError, rethrow } = require('../../../helpers/errors');
const Shoot = require('../../../models/Shoot');

module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'Get the download job status',
        response: Joi.object({
            id: Joi.number().integer().required(),
            shootId: Joi.number().integer().required(),
            status: Joi.string().required(),
            message: Joi.string().required(),
            downloadUrl: Joi.string().optional().allow(''),
            createdAt: Joi.date().iso().required(),
            updatedAt: Joi.date().iso().required(),
            progress: Joi.number().optional()
        })
    },
    method: 'GET',
    path: '/shoot/{id}/action/enqueue-download-job/status',
    auth: 'jwt',
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const {
            downloadContentService,
            apiService,
            authService,
            shootService
        } = request.services();

        const { user, tokenPayload: { role } } = request.auth.credentials;
        const { params: { id } } = request;

        // Check route authorization
        authService.checkRouteAuthorization({
            role,
            route: 'get-download-job-status'
        });

        // Get and verify shoot
        const { shoot } = await shootService.getShoot({ id, user, role })
            .catch(rethrow(NotFoundError, 'Unable to find the shoot'));

        // Check shoot-specific authorization
        await authService.checkShootAuthorization({
            shoot,
            role,
            action: 'download-processed',
            allowedRoles: ['admin', 'client'],
            allowedStatuses: [
                Shoot.statuses.photosReady,
                Shoot.statuses.completed,
                Shoot.statuses.photosUploaded
            ]
        });

        try {
            // Get the job status using the new service method
            const jobRecord = await downloadContentService.getJobStatus({
                shootId: shoot.id
            });
            // Return the formatted job record
            return apiService.formatResponse(h, jobRecord);
        }
        catch (error) {
            request.logger.error('Error getting download job status:', error);
            return apiService.error(h, 400, [error.message]);
        }
    }
});
