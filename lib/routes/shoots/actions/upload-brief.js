'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');
const { NotFoundError, rethrow } = require('../../../helpers/errors');

const Shoot = require('../../../models/Shoot');

module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'handle brief upload',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/shoot/{id}/action/upload-brief',
    auth: 'jwt',
    options: {
        payload: {
            output: 'stream',
            parse: false,
            allow: 'application/octet-stream',
            maxBytes: process.env.MAX_UPLOAD_BRIEF_SIZE_MB * 1024 * 1024
        }
    },
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        }),
        query: Joi.object({
            filename: Joi.string()
        })
    },
    handler: async (request, h) => {

        const allowedRoles = ['admin', 'client'];

        const allowedStatuses = [Shoot.statuses.redeemable, Shoot.statuses.toSchedule, Shoot.statuses.scheduled, Shoot.statuses.photographerAssigned];
        const { apiService, shootService, authService } = request.services();
        const { params: { id }, payload: brief, query: { filename = 'brief.pdf' } } = request;
        const { user, tokenPayload: { role } } = request.auth.credentials;

        const { shoot } = await shootService.getShoot({ id, user, role }).catch(rethrow(NotFoundError, 'Unable to find the shoot'));

        await authService.checkShootAuthorization({
            shoot,
            role,
            action: 'upload-brief',
            allowedRoles,
            allowedStatuses });

        await shootService.uploadBrief({ shoot, brief, filename });

        await shootService.updateShoot({
            shoot,
            role,
            user,
            newValues: {
                is_brief_uploaded: true
            }
        });

        if (shoot.redeemable_total || shoot.redeemable_counter) {
            await shootService.updateRedeemedShootsBrief({ shoot, user, is_brief_uploaded: true });
        }

        return apiService.formatResponse(h,
            'brief uploaded'
        );
    }
});
