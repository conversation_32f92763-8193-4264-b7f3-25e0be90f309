'use strict';

const { createRoute } = require('../../../helpers/route_factory');
const Joi = require('joi');
const { rethrow, NotFoundError } = require('../../../helpers/errors');
const Shoot = require('../../../models/Shoot');
const { batchContent } = require('../../../validation-schema/content/payloads');

module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'uploads content with different content types',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/shoot/{id}/action/add-content',
    auth: ['jwt', 'api-key'],
    validate: {
        params: Joi.object({
            id: Joi.string().required().description('Use "0" for non-shoot uploads')
        }),
        payload: batchContent
    },
    handler: async (request, h) => {

        const { id } = request.params;
        const { content } = request.payload;
        const { shootService, apiService, authService, contentService, usageSummaryService } = request.services();
        const { user, tokenPayload: { role } } = request.auth.credentials;
        authService.checkRouteAuthorization({ role, route: 'add-content' });

        let shoot = null;
        if (id !== '0') {
            const result = await shootService.getShoot({ id, user, role }).catch(rethrow(NotFoundError, 'Unable to find the shoot'));
            // eslint-disable-next-line prefer-destructuring
            shoot = result.shoot;
            await authService.checkShootAuthorization({
                shoot,
                role,
                action: 'upload-processed',
                allowedRoles: [],
                allowedStatuses: [Shoot.statuses.photosReady, Shoot.statuses.completed, Shoot.statuses.photosUploaded, Shoot.statuses.confirmed]
            });
        }

        // Add user_id to images if not provided
        const processedContent = content.map((image) => ({
            ...image,
            user_id: image.user_id || user.id,
            shoot_id: id === '0' ? null : shoot.id
        }));

        const { images, videos } = await contentService.createContent(processedContent, id === '0' ? null : shoot.id, user.id, user.name);
        await usageSummaryService.storeUsageSummary({ user }, true);
        return apiService.formatResponse(h, {
            content: [...images, ...videos]
        });
    }
});
