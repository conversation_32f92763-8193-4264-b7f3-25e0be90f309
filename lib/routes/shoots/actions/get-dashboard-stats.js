'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'Get dashboard statistics for shoots',
        response: Joi.object({
            upcoming_shoots: Joi.number().required(),
            upcoming_shoots_growth: Joi.number().required(),
            completed_shoots: Joi.number().required(),
            completed_shoots_growth: Joi.number().required(),
            monthly_shoots: Joi.number().required(),
            monthly_shoots_growth: Joi.number().required(),
            total_images: Joi.number().required(),
            monthly_images: Joi.number().required(),
            monthly_images_growth: Joi.number().required()
        })
    },
    method: 'GET',
    path: '/shoot/action/dashboard-stats',
    auth: 'jwt',
    handler: async (request, h) => {

        const { authService, apiService, shootService } = request.services();
        const { user, tokenPayload: { role } } = request.auth.credentials;

        authService.checkRouteAuthorization({
            role,
            route: 'view-dashboard'
        });

        const stats = await shootService.getDashboardStats({ user, role });

        return apiService.formatResponse(h, stats);
    }
});
