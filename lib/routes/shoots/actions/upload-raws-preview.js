/* eslint-disable @hapi/scope-start */
/* eslint-disable @hapi/capitalize-modules */
'use strict';

// Import necessary modules and functions
const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');
const unzipper = require('unzipper');
const Path = require('path');
const sizeOf = require('image-size');

// Helper functions
const isSupportedImage = (fileName) => {
    return ['.png', '.jpg', '.jpeg', '.webp'].includes(Path.extname(fileName).toLowerCase());
};

const calculateAspectRatio = (buffer) => {
    try {
        const dimensions = sizeOf(buffer);
        return dimensions.width / dimensions.height;
    }
    catch (error) {
        throw new Error('Failed to calculate aspect ratio for the image.');
    }
};

const formatS3TagString = (tags) => {
    return Object.entries(tags)
        .map(([key, value]) => `${key}=${value}`)
        .join('&');
};
// Helper functions

// Define the route module for uploading and processing images
module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'Handle raws preview photos upload',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/shoot/{id}/action/upload-raws-preview',
    auth: ['jwt', 'api-key'],
    options: {
        payload: {
            output: 'stream',
            parse: false,
            allow: 'application/octet-stream',
            maxBytes: process.env.MAX_UPLOAD_PROCESSED_SIZE_MB * 1024 * 1024
        }
    },
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        })
    },
    handler: async (request, h) => {
        const { apiService, contentService, storageService } = request.services();
        const { params: { id }, payload } = request;
        const bucket = request.server.settings.app.amazon.processed_photos_bucket_name;

        const rootDirectory = `${id}-preview-raws`;
        const rawsDataArray = [];
        const tags = {
            lifecycle: 'preview'
        };

        // Process the zip stream directly
        try {
            const zip = payload.pipe(unzipper.Parse({ forceStream: true }));
            for await (const entry of zip) {
                const filePath = entry.path;

                // Skip __MACOSX directory and metadata files starting with ._
                if (filePath.startsWith('__MACOSX/') || Path.basename(filePath).startsWith('._') || filePath === '.DS_Store') {
                    await entry.autodrain();
                    continue;
                }

                if (!entry.isDirectory && isSupportedImage(filePath)) {
                    const buffer = await entry.buffer();
                    const fileName = Path.basename(filePath);

                    // Determine folder structure, excluding root directory
                    let folderName = '';
                    if (filePath.startsWith(rootDirectory)) {
                        folderName = Path.dirname(filePath).slice(rootDirectory.length + 1); // Remove root directory
                    }
                    else {
                        folderName = Path.dirname(filePath); // Extract directory name (excluding path)
                    }

                    let s3FilePath = `${id}/preview-raws/${folderName}/${fileName}`;

                    if (folderName === '') {
                        s3FilePath = `${id}/preview-raws/${fileName}`;
                    }

                    const uploadOptions = {
                        ACL: 'public-read',
                        ContentType: 'image/jpeg',
                        Body: buffer,
                        Tagging: formatS3TagString(tags)
                    };

                    // Upload the file sequentially
                    await storageService.uploadObject({
                        bucket,
                        filepath: s3FilePath,
                        object: buffer,
                        options: uploadOptions
                    });
                    const optimizedUrl = request.server.settings.app.amazon.cloudfront_url;  // Adjust this as needed
                    // const optimizedUrl = 'd3v4ubxdiok6mf.cloudfront.net';

                    // Add the rawsDataArray logic
                    rawsDataArray.push({
                        shoot_id: id,
                        url: `https://${bucket}.s3.amazonaws.com/${s3FilePath}`,
                        status: 'pending',
                        filename: fileName,
                        placeholder_url: `https://${optimizedUrl}/fit-in/30x20/${s3FilePath}`,
                        preview_url: `https://${optimizedUrl}/fit-in/300x200/${s3FilePath}`,
                        folder: folderName === '' ? '/' : folderName,
                        filepath: s3FilePath,
                        aspect_ratio: calculateAspectRatio(buffer)
                    });

                    // Clean up memory after each upload
                    entry.autodrain();
                }
                else {
                    // Clean up memory for non-image files
                    entry.autodrain();
                }
            }

            // Insert or update image metadata in the database
            await contentService.createRawsBatch(rawsDataArray);
            return apiService.formatResponse(h, 'Photos uploaded and metadata inserted successfully');
        }
        catch (error) {
            return h.response('Error processing ZIP file').code(500);
        }
    }

});
