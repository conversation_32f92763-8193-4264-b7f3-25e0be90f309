'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'Get dashboard recent images',
        response: Joi.object({
            images: Joi.array().items(Joi.object({
                id: Joi.number().required(),
                shoot_id: Joi.number().optional().allow(null),
                url: Joi.string().allow(null),
                optimized_url: Joi.string().allow(null),
                preview_url: Joi.string().allow(null),
                placeholder_url: Joi.string().allow(null),
                aspect_ratio: Joi.number().allow(null),
                height: Joi.number().allow(null),
                type: Joi.string().valid('image', 'video').optional(),
                width: Joi.number().allow(null),
                filename: Joi.string().required(),
                shoot_name: Joi.string().allow(null),
                created_at: Joi.date().optional().allow(null),
                relative_upload_time: Joi.string().allow(null)
            })).optional()
        })
    },
    method: 'GET',
    path: '/shoot/action/dashboard-recent-list',
    auth: 'jwt',
    handler: async (request, h) => {

        const { authService, apiService, contentService } = request.services();
        const { user, tokenPayload: { role } } = request.auth.credentials;

        authService.checkRouteAuthorization({
            role,
            route: 'view-dashboard'
        });

        const images = await contentService.getRecentContent({ user, role });

        return apiService.formatResponse(h, { images });
    }
});
