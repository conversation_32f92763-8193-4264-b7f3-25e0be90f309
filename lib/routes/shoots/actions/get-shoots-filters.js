'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'return get shoots filters',
        response: Joi.object({
            max_price: Joi.string(),
            cities: Joi.array().items(Joi.string()),
            states: Joi.array().items(Joi.string())
        })
    },
    method: 'GET',
    path: '/shoot/action/get-shoots-filters',
    auth: 'jwt',
    validate: {
    },
    handler: async (request, h) => {

        const { apiService, shootService } = request.services();

        const { user, tokenPayload: { role } } = request.auth.credentials;

        const max_price  = await shootService.getMaxPriceForUser({ user, role });

        const { cities }  = await shootService.getListOfCitiesAndStates({ user, role });

        return apiService.formatResponse(h, {
            max_price,
            cities
        });
    }
});
