'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');
const { NotFoundError, rethrow } = require('../../../helpers/errors');
const Shoot = require('../../../models/Shoot');
const Path = require('path');
const { v4: uuidv4 } = require('uuid');

module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'Get presigned URLs for uploading processed photos or videos',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/shoot/{id}/action/get-presigned-urls',
    auth: ['jwt', 'api-key'],
    validate: {
        params: Joi.object({
            id: Joi.string().required().description('Use "0" for non-shoot uploads')
        }),
        payload: Joi.object({
            files: Joi.array().items(Joi.object({
                name: Joi.string().required(),
                relativePath: Joi.string().required(),
                contentType: Joi.string().required().allow(''),
                duration: Joi.number().optional(), // Duration for videos
                aspectRatio: Joi.number() // Duration for videos
            })).required()
        })
    },
    handler: async (request, h) => {

        const { apiService, shootService, authService, storageService, subscriptionService } = request.services();
        const { params: { id }, payload, server } = request;
        const { user, tokenPayload: { role } } = request.auth.credentials;
        authService.checkRouteAuthorization({ role, route: 'get-presigned-urls' });

        try {
            let shoot = null;
            const supportedExtensions = ['.jpg', '.jpeg', '.mp4', '.mov'];
            await subscriptionService.validateActionWithCurrentPlans({ user });

            // Check if this is a shoot-related upload
            if (id !== '0') {
                const result = await shootService.getShoot({ id, user, role }).catch(rethrow(NotFoundError, 'Unable to find the shoot'));
                // eslint-disable-next-line prefer-destructuring
                shoot = result.shoot;

                await authService.checkShootAuthorization({
                    shoot,
                    role,
                    action: 'upload-processed',
                    allowedRoles: [],
                    allowedStatuses: [Shoot.statuses.photosReady, Shoot.statuses.completed, Shoot.statuses.photosUploaded, Shoot.statuses.confirmed]
                });

            }

            // Filter out unsupported files and ._ metadata files
            const filesToProcess = await payload.files.filter((file) => {

                const fileExtension = Path.extname(file.name).toLowerCase();
                const fileName = Path.basename(file.name);
                return supportedExtensions.includes(fileExtension) &&
                       fileName !== '.DS_Store' &&
                       !fileName.startsWith('._');
            });

            // Create a map of processed files by name for efficient lookup
            const processedFileNames = new Set(filesToProcess.map((file) => file.name));
            const skippedFiles = payload.files.filter((file) => !processedFileNames.has(file.name));
            const uuid = uuidv4();
            // Get presigned URLs for the files
            const presignedUrls = await storageService.getPresignedPutUrls({
                bucket: server.settings.app.amazon.processed_photos_bucket_name,
                prefix: id === '0' ? `${user.id}/${uuid}/` : `${shoot.id}/`,
                files: filesToProcess,
                expires: 3600 // 1 hour expiration
            });

            const bucketName = server.settings.app.amazon.processed_photos_bucket_name;
            const baseUrl = `https://${bucketName}.s3.eu-central-1.amazonaws.com`;
            const optimizedUrl = server.settings.app.amazon.cloudfront_url;

            // Generate metadata based on shoot type
            const contentMetadata = filesToProcess.map((file) => {

                const folderName = Path.dirname(file.relativePath);
                const fileName = Path.basename(file.name);
                const prefix = id === '0' ? `${user.id}/${uuid}/` : `${shoot.id}/`;
                const filePath = `${prefix}${file.relativePath}`;
                const url = `${baseUrl}/${filePath}`;

                return {
                    shoot_id: id === '0' ? null : shoot.id,
                    user_id: user.id,
                    url,
                    status: 'pending',
                    filename: fileName,
                    folder: folderName,
                    filepath: file.relativePath,
                    aspect_ratio: file.aspectRatio,
                    preview_url: `${optimizedUrl}/fit-in/300x200/${filePath}`,
                    placeholder_url: `${optimizedUrl}/filters:blur(5)/fit-in/30x20/${filePath}`,
                    // eslint-disable-next-line max-len
                    duration: file.contentType === 'image/jpeg' || file.contentType === 'image/jpg' || file.contentType === 'image/png' ? 0 : file.duration
                };

            });

            // Format response with a unified contentMetadata field
            const responseData = {
                message: 'Presigned URLs generated successfully',
                presignedUrls,
                processedCount: presignedUrls.length,
                skippedCount: skippedFiles.length,
                skippedFiles: skippedFiles.map((file) => file.name),
                contentMetadata // Use a single mediaMetadata field for both images and videos
            };

            return apiService.formatResponse(h, responseData);
        }
        catch (error) {
            throw new Error('Failed to get pre-signed urls');
        }
    }
});
