'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');
const { NotFoundError, rethrow } = require('../../../helpers/errors');
const { shootDetail }  = require('../../../validation-schema/shoots/responses.js');
const Shoot = require('../../../models/Shoot');

module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'unassign a photographer from a given shoot',
        response: Joi.object({
            shootDetail
        })
    },
    method: 'POST',
    path: '/shoot/{id}/action/unassign-photographer',
    auth: 'jwt',
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const { apiService, shootService, authService, reportUserService, mondayService } = request.services();

        const {
            params: {
                id
            }
        } = request;

        const { user, tokenPayload: { role } } = request.auth.credentials;

        const { shoot } = await shootService.getShoot({ id, user, role })
            .catch(rethrow(NotFoundError, 'Unable to find the shoot'));

        authService.checkShootAuthorization({
            shoot,
            role,
            photographer_id: user.photographer_id,
            action: 'unassign-photographer',
            allowedRoles: ['photographer', 'admin'],
            allowedStatuses: [Shoot.statuses.photographerAssigned, Shoot.statuses.confirmed]
        });

        const updatedShoot = await shootService.updateShoot({
            shoot,
            user,
            role,
            newValues: {
                photographer_id: null,
                status: Shoot.statuses.scheduled
            }
        });

        if (process.env.NODE_ENV !== 'test') {

            await mondayService.updateShoot({
                shoot_id: updatedShoot.id, boardType: updatedShoot.type === 'express' ? 'FLASHYSHOOTS' : 'BESPOKE'
            });
        }

        shootService.handleStatusChangeNotification({ oldShoot: shoot, newShoot: updatedShoot, user, role });

        reportUserService.createReportUser( { shoot, performer_user_id: user.id,
            target_photographer_id: shoot.photographer_id, starting_status: shoot.status, target_status: Shoot.statuses.scheduled } );

        return apiService.formatResponse(h, {
            shootDetail: updatedShoot
        });
    }
});
