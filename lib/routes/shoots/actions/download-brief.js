'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');
const { NotFoundError, rethrow } = require('../../../helpers/errors');

module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'handle brief download',
        disableResponseValidation: true
    },
    method: 'GET',
    path: '/shoot/{id}/action/download-brief',
    auth: 'jwt',
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        }),
        query: Joi.object({
            filename: Joi.string()
        })
    },
    handler: async (request, h) => {

        const { shootService } = request.services();

        const { params: { id }, query: { filename } } = request;
        const { user, tokenPayload: { role } } = request.auth.credentials;

        const { shoot } = await shootService.getShoot({ id, user, role }).catch(rethrow(NotFoundError, 'Unable to find the shoot'));

        const { contentLength, stream, filename: originalFilename } = await shootService.downloadBrief({ shoot });

        return h.response(stream)
            .header('Content-Length', contentLength)
            .header('Content-Type', 'application/octet-stream')
            .header('Content-Disposition', `attachment; filename=${filename || originalFilename || 'brief.pdf'}`);
    }
});
