'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');
const { NotFoundError, rethrow } = require('../../../helpers/errors');
const Shoot = require('../../../models/Shoot');

module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'handle raw photos download',
        disableResponseValidation: true
    },
    method: 'GET',
    path: '/shoot/{id}/action/download-raw',
    auth: ['jwt', 'api-key'],
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        }),
        query: Joi.object({
            filename: Joi.string()
        })
    },
    handler: async (request, h) => {
        // Download raws

        const allowedRoles = ['admin', 'editor'];
        const allowedStatuses = [Shoot.statuses.photosUploaded, Shoot.statuses.photosReady, Shoot.statuses.completed];

        const { shootService, authService } = request.services();

        const { params: { id } } = request;
        const { user, tokenPayload: { role } } = request.auth.credentials;

        const { shoot } = await shootService.getShoot({ id, user, role }).catch(rethrow(NotFoundError, 'Unable to find the shoot'));

        await authService.checkShootAuthorization({ shoot,
            role,
            action: 'download-raw',
            allowedRoles,
            allowedStatuses });

        const { contentLength, stream } = await shootService.downloadPhotos({ shoot, downloadType: 'raw' });
        return h.response(stream)
            .header('Content-Length', contentLength)
            .header('Content-Type', 'application/octet-stream')
            // eslint-disable-next-line max-len
            .header('Content-Disposition', `attachment; filename=${shoot.id}-${shoot.content === 'photography' ? 'raws' : 'draft'}.zip`);
    }
});
