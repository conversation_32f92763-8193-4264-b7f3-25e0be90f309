'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { NotFoundError, ValidationError, rethrow } = require('../../helpers/errors');
const { shoot }  = require('../../validation-schema/shoots/responses.js');
const { create } = require('../../validation-schema/shoots/payloads.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'create a shoot',
        response: Joi.object({
            shoot
        })
    },
    method: 'POST',
    path: '/shoot',
    auth: ['jwt'],
    validate: {
        payload: create
    },
    handler: async (request, h) => {

        const {
            apiService, shootService, authService, packageService, userService, shootStatusService, mondayService
        } = request.services();

        const {
            packages,
            photographer_id = null,
            client_id,
            location,
            total_price,
            total_revenue,
            time = {},
            notes,
            contact_phone,
            contact_name,
            is_payed,
            redeemable_total,
            redeemable_shoot_id,
            outlet_code,
            outlet_name,
            poc_email,
            poc_phone,
            poc_name,
            name,
            type,
            content
        } = request.payload;

        const { user, tokenPayload: { role } } = request.auth.credentials;

        let redeemable_shoot;

        authService.checkSubClientWritePermission({ role, redeemable_shoot_id, user });

        if (redeemable_shoot_id) {
            const { shoot } = await shootService
                .getShoot({ id: redeemable_shoot_id, user, role })
                .catch(rethrow(NotFoundError, 'Shoot can not be created due to non-existent redeemable shoot'));
            redeemable_shoot = shoot;

            shootStatusService.isLegitRedeem({ redeemable_shoot });
        }

        if (photographer_id) {
            await userService
                .getPhotographerById({ photographer_id })
                .catch(rethrow(NotFoundError, 'Shoot can not be created due to non-existent photographer'));
        }

        if (client_id) {
            await userService
                .getClientById({ client_id })
                .catch(rethrow(NotFoundError, 'Shoot can not be created due to non-existent client'));
        }

        authService.checkRouteAuthorization({ role, route: 'create-shoot' });

        let createdShoot;
        // Client is creating a new shoot ( not redeeming)
        if (!redeemable_shoot_id) {

            const { formattedPackages, totalFixedPrice, totalFixedRevenue } = await packageService.getFormattedPackages({ packages });
            const servicesIds = formattedPackages.map(({ service_id }) => service_id);
            if (servicesIds.length !== [...new Set(servicesIds)].length) {
                throw new ValidationError('Shoot can not be created due to equal services');
            }

            const { savedShoot } = await shootService.createShoot({
                packages: formattedPackages,
                photographer_id,
                client_id: (role === 'client') ? user.client_id : client_id,
                location,
                price: (total_price) ? total_price : totalFixedPrice,
                photographer_revenue: (total_revenue) ? total_revenue : totalFixedRevenue,
                time,
                duration: time.duration,
                notes,
                contact_phone,
                contact_name,
                is_payed,
                redeemable_total,
                role,
                user,
                name,
                type,
                content
            });
            createdShoot = savedShoot;

            if (redeemable_total === 0 || redeemable_total === undefined || redeemable_total === null) {
                await mondayService.createShoot({ shoot_id: createdShoot.id });
            }
        }
        else {
        // Client is Redeeming a shoot from an existent redeemable shoot
            createdShoot = await shootService.redeemShoot({
                redeemable_shoot,
                newValues: {
                    time,
                    location,
                    notes,
                    outlet_code,
                    outlet_name,
                    poc_email,
                    poc_phone,
                    poc_name,
                    name
                },
                user,
                role
            });
            // Only BESPOKE shoots can be redeemed
            await mondayService.createShoot({ shoot_id: createdShoot.id, boardType: 'BESPOKE' });
        }

        const { shoot } = await shootService.getShoot({ id: createdShoot.id, user, role });

        await shootService.sendCreateNotification( shoot );
        return apiService.formatResponse(h, {
            shoot
        });
    }
});
