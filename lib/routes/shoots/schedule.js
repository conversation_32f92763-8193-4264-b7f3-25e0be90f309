'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { rethrow, NotFoundError } = require('../../helpers/errors');
const { shootDetail }  = require('../../validation-schema/shoots/responses.js');
const { schedule } = require('../../validation-schema/shoots/payloads.js');
const Shoot = require('../../models/Shoot');

module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'schedules a shoot',
        response: Joi.object({
            shootDetail
        })
    },
    method: 'POST',
    path: '/shoot/schedule/{id}',
    auth: 'jwt',
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        }),
        payload: schedule
    },
    handler: async (request, h) => {

        const { apiService, authService, shootService, mondayService } = request.services();

        const {
            params: { id },
            payload: {
                datetime,
                notes
            }
        } = request;

        const { user, tokenPayload: { role } } = request.auth.credentials;

        const { shoot } = await shootService.getShoot({ id, user, role })
            .catch(rethrow(NotFoundError, 'Unable to find the shoot'));

        authService.checkShootAuthorization({
            shoot,
            role,
            action: 'schedule-shoot',
            allowedRoles: ['admin', 'client'],
            allowedStatuses: [
                Shoot.statuses.toBePayed,
                Shoot.statuses.toSchedule,
                Shoot.statuses.scheduled,
                Shoot.statuses.photographerAssigned,
                Shoot.statuses.confirmed
            ]
        });

        const  updatedShoot  = await shootService.updateShoot({
            shoot,
            role,
            user,
            newValues: {
                datetime,
                ...(datetime && { scheduled: true }),
                notes
            }
        });

        await mondayService.updateShoot({ shoot_id: updatedShoot.id });

        return apiService.formatResponse(h, {
            shootDetail: updatedShoot
        });
    }
});
