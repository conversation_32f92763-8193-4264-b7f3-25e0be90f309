'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { NotFoundError, rethrow } = require('../../helpers/errors');
const { shootDetail }  = require('../../validation-schema/shoots/responses.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'return shoot based on id',
        response: Joi.object({
            shoot: shootDetail
        })
    },
    method: 'GET',
    path: '/shoot/{id}',
    auth: 'jwt',
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const { apiService, shootService } = request.services();

        const { id } = request.params;

        const { user, tokenPayload: { role } } = request.auth.credentials;

        const { shoot } = await shootService.getShoot({ id, user, role })
            .catch(rethrow(NotFoundError, 'Unable to find the shoot'));

        return apiService.formatResponse(h, {
            shoot
        });
    }
});
