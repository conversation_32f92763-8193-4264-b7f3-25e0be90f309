'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { rethrow, NotFoundError, ValidationError } = require('../../helpers/errors');
const { shootDetail }  = require('../../validation-schema/shoots/responses.js');
const { updateFull } = require('../../validation-schema/shoots/payloads.js');
const Shoot = require('../../models/Shoot');

module.exports = createRoute({
    documentation: {
        resourceTag: 'shoots',
        description: 'edits a shoot',
        response: Joi.object({
            shootDetail
        })
    },
    method: 'POST',
    path: '/shoot/edit/{id}',
    auth: 'jwt',
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        }),
        payload: updateFull
    },
    handler: async (request, h) => {

        const { apiService, authService, shootService, mondayService, packageService } = request.services();

        const {
            params: { id },
            payload: {
                notes,
                total_price,
                total_revenue,
                location,
                time: {
                    from,
                    duration
                },
                name,
                packages,
                photographer_id,
                content
            }
        } = request;

        const { user, tokenPayload: { role } } = request.auth.credentials;

        const { shoot } = await shootService.getShoot({ id, user, role })
            .catch(rethrow(NotFoundError, 'Unable to find the shoot'));
        authService.checkShootAuthorization({
            shoot,
            role,
            action: 'edit-shoot',
            allowedRoles: ['admin'],
            allowedStatuses: [
                Shoot.statuses.scheduled,
                Shoot.statuses.photographerAssigned,
                Shoot.statuses.confirmed,
                Shoot.statuses.photosUploaded,
                Shoot.statuses.canceled,
                Shoot.statuses.completed
            ]
        });

        if (shoot.photographer_id !== photographer_id) {
            authService.checkShootAuthorization({
                shoot,
                role,
                action: 'assign-photographer',
                allowedRoles: ['admin'],
                allowedStatuses: [Shoot.statuses.scheduled, Shoot.statuses.photographerAssigned, Shoot.statuses.confirmed]
            });
        }

        const { formattedPackages, totalFixedPrice, totalFixedRevenue } = await packageService.getFormattedPackages({ packages });

        const servicesIds = formattedPackages.map(({ service_id }) => service_id);
        if (servicesIds.length !== [...new Set(servicesIds)].length) {
            throw new ValidationError('Shoot can not be created due to equal services');
        }

        const updatedShoot = await shootService.updateShoot({
            shoot,
            role,
            user,
            newValues: {
                notes,
                price: (total_price) ? total_price : totalFixedPrice,
                photographer_revenue: (total_revenue) ? total_revenue : totalFixedRevenue,
                address: location,
                datetime: from,
                duration,
                name,
                scheduled: (from) ? true : false,
                picture_number: formattedPackages[0].package.picture_number || 0,
                video_number: formattedPackages[0].package.video_number || 0,
                video_duration: formattedPackages[0].package.video_duration || 0,
                photographer_id: photographer_id ? photographer_id : null,
                content
            },
            hardUpdate: true,
            packages: formattedPackages
        });

        await shootService.sendEditNotification(shoot, updatedShoot, user);

        await mondayService.updateShoot({
            shoot_id: updatedShoot.id, boardType: updatedShoot.type === 'express' ? 'FLASHYSHOOTS' : 'BESPOKE'
        });

        return apiService.formatResponse(h, {
            shootDetail: updatedShoot
        });
    }
});
