'use strict';

const { createRoute } = require('../../helpers/route_factory');
const { ValidationError } = require('../../helpers/errors');

/**
 * Route: POST /facebook/disconnect
 * Purpose: Remove Facebook integration for a user
 *
 * Flow:
 * 1. Check if user is authorized
 * 2. Delete Meta integration if exists
 *
 * Error cases:
 * - Unauthorized access
 * - No integration found
 * - Database errors
 */
module.exports = createRoute({
    documentation: {
        resourceTag: 'facebook',
        description: 'Disconnect Facebook integration',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/facebook/disconnect',
    auth: ['jwt'],
    handler: async (request, h) => {

        const { apiService, metaService, authService } = request.services();
        const { user, tokenPayload: { role } } = request.auth.credentials;
        await authService.checkRouteAuthorization({ role, route: 'get-facebook-status' });

        try {
            await metaService.deleteIntegration(user.id);

            return apiService.formatResponse(h, {
                success: true,
                message: 'Facebook integration successfully disconnected'
            });
        }
        catch (error) {
            request.log('error', {
                message: 'Facebook disconnect failed',
                error: error.message,
                stack: error.stack
            });

            throw error instanceof ValidationError
                ? error
                : new ValidationError('Failed to disconnect Facebook account');
        }
    }
});
