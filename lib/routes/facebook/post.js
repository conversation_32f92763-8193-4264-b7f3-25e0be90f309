'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { ValidationError } = require('../../helpers/errors');

module.exports = createRoute({
    documentation: {
        resourceTag: 'meta',
        description: 'Post image to Facebook page',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/facebook/pages/{pageId}/post',
    auth: ['jwt'],
    validate: {
        params: Joi.object({
            pageId: Joi.string().required().description('Facebook Page ID')
        }),
        payload: Joi.object({
            imageUrl: Joi.string().uri().required()
                .description('URL of the image to post'),
            message: Joi.string().required()
                .description('Text message for the post')
        })
    },
    handler: async (request, h) => {

        const { apiService, metaService, authService } = request.services();
        const { pageId } = request.params;
        const { imageUrl, message } = request.payload;
        const { user, tokenPayload: { role } } = request.auth.credentials;
        await authService.checkRouteAuthorization({ role, route: 'get-facebook-status' });

        try {
            // 1. Get user's Meta integration status with full data
            const integrationStatus = await metaService.getIntegrationStatus(user.id);

            if (!integrationStatus.connected) {
                throw new ValidationError('Facebook account not connected');
            }

            // 2. Find the page in integration data
            const pageData = integrationStatus.integration.facebook_pages?.find(
                (p) => p.id === pageId
            );

            if (!pageData) {
                throw new ValidationError('Facebook page not found in connected pages');
            }

            // 3. Validate page status and permissions
            if (!pageData.isPublished) {
                throw new ValidationError('Cannot post to unpublished Facebook page');
            }

            if (!pageData.tasks?.includes('CREATE_CONTENT')) {
                throw new ValidationError('Insufficient permissions to create posts on this page');
            }

            if (!pageData.accessToken) {
                throw new ValidationError('Page access token not found');
            }

            // 4. Create the post
            const result = await metaService.createPostWithImage(
                pageId,
                pageData.accessToken,
                { imageUrl, message }
            );

            return apiService.formatResponse(h, {
                success: true,
                postId: result.postId,
                photoId: result.photoId,
                pageId,
                pageName: pageData.name
            });
        }
        catch (error) {
            request.log('error', {
                message: 'Failed to create Facebook post',
                error: {
                    name: error.name,
                    message: error.message,
                    stack: error.stack
                },
                payload: {
                    pageId,
                    message: `${message?.substring(0, 50)}...`,
                    imageUrl
                }
            });

            throw error instanceof ValidationError
                ? error
                : new ValidationError('Failed to create Facebook post');
        }
    }
});
