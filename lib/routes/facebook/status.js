'use strict';

const { createRoute } = require('../../helpers/route_factory');
const { ValidationError } = require('../../helpers/errors');

module.exports = createRoute({
    documentation: {
        resourceTag: 'meta',
        description: 'Check Meta Business integration status',
        disableResponseValidation: true
    },
    method: 'GET',
    path: '/facebook/status',
    auth: ['jwt'],
    handler: async (request, h) => {

        const { apiService, metaService, authService } = request.services();
        const { user, tokenPayload: { role } } = request.auth.credentials;

        await authService.checkRouteAuthorization({ role, route: 'get-facebook-status' });
        try {
            const status = await metaService.getIntegrationStatus(user.id);

            // If not connected, return the status as is
            if (!status.connected) {
                return apiService.formatResponse(h, status);
            }

            // Format the response
            const formattedResponse = {
                connected: true,
                integration: {
                    businessId: status.integration.fb_business_id,
                    businessName: status.integration.business_name,
                    pages: status.integration.facebook_pages.map((page) => ({
                        id: page.id,
                        name: page.name,
                        category: page.category,
                        isPublished: page.isPublished,
                        verificationStatus: page.verificationStatus,
                        followersCount: page.followersCount,
                        tasks: page.tasks
                    })),
                    instagramAccounts: status.integration.instagram_accounts.map((account) => ({
                        id: account.id,
                        username: account.username,
                        isPrivate: account.isPrivate,
                        isPublished: account.isPublished,
                        mediaCount: account.mediaCount,
                        followCount: account.followCount,
                        followedByCount: account.followedByCount,
                        profilePic: account.profilePic,
                        hasProfilePicture: account.hasProfilePicture,
                        connectedPageId: account.connectedPageId,
                        connectedPageName: account.connectedPageName,
                        instagramBusinessId: account.instagramBusinessId
                    })),
                    connectedAt: status.integration.created_at
                }
            };

            return apiService.formatResponse(h, formattedResponse);
        }
        catch (error) {
            request.log('error', {
                message: 'Failed to check Meta integration status',
                error
            });
            throw error instanceof ValidationError
                ? error
                : new ValidationError('Failed to check Meta integration status');
        }
    }
});
