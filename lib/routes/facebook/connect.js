'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { ValidationError } = require('../../helpers/errors');

module.exports = createRoute({
    documentation: {
        resourceTag: 'meta',
        description: 'Connect to Meta Business Platform',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/facebook/connect',
    auth: ['jwt'],
    validate: {
        payload: Joi.object({
            code: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const { apiService, metaService, authService } = request.services();
        const { code } = request.payload;

        const { user, tokenPayload: { role } } = request.auth.credentials;
        await authService.checkRouteAuthorization({ role, route: 'get-facebook-status' });

        try {
            // 1. Exchange code for access token
            const verificationData = await metaService.exchangeCodeForToken(code);
            const { accessToken } = verificationData;

            // 2. Verify token and permissions
            await metaService.verifyToken(accessToken);

            // 3. Get business details with connected accounts
            const { businessId, businessName, pages, instagramAccounts } = await metaService.getBusinessAssets(accessToken);

            // 4. Store the integration
            await metaService.storeIntegration(user.id, {
                businessId,
                businessName,
                accessToken: verificationData,
                pages,
                instagramAccounts
            });

            return apiService.formatResponse(h, {
                businessId,
                businessName,
                pages: pages.map((page) => ({
                    id: page.id,
                    name: page.name,
                    category: page.category,
                    tasks: page.tasks,
                    isPublished: page.isPublished,
                    categoryList: page.categoryList,
                    followersCount: page.followersCount,
                    verificationStatus: page.verificationStatus
                })),
                instagramAccounts
            });
        }
        catch (error) {
            request.log('error', {
                message: 'Meta business connection failed',
                error
            });

            throw error instanceof ValidationError
                ? error
                : new ValidationError('Failed to connect Meta business account');
        }
    }
});
