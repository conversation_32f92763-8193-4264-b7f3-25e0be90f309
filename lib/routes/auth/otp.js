'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'users',
        description: 'send an otp to the user',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/otp',
    auth: false,
    validate: {
        payload: Joi.object({
            phone_number: Joi.string().pattern(/^\+[1-9]\d{1,14}$/).required()
            // Validates the phone number in E.164 format
        })
    },

    handler: async (request, h) => {

        try {
            const { apiService, userService } = request.services();
            const { phone_number } = request.payload;

            // auth0 start passwordless auth
            await userService.startPasswordlessAuth({ phone_number });

            return apiService.formatResponse(h, 'OTP has been sent successfully');
        }
        catch (error) {
            if (error.statusCode === 429) {
                throw new Error(`Rate limit exceeded : ${error}`);
            }

            throw new Error(`Failed to sent OTP : ${error}`);
        }
    }
});
