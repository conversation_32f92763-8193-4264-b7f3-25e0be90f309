'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'users',
        description: 'reset password',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/reset-password',
    auth: false,
    validate: {
        payload: Joi.object({
            email: Joi.string().email().required()
        })
    },

    handler: async (request, h) => {

        const { apiService, userService } = request.services();

        try {
            const { email } = request.payload;

            const result = await userService.requestPasswordReset({ email });
            return apiService.formatResponse(h, result, 200);
        }
        catch (error) {
            request.logger.error({ error }, 'Failed to reset password');
            if (error.statusCode === 429) {
                return apiService.formatResponse(h, null, 429, ['Rate limit exceeded']);
            }

            return apiService.formatResponse(h, null, 500, ['Error']);
        }
    }
});
