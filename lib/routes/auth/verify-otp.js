'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'users',
        description: 'verify otp',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/otp/verify',
    auth: false,
    validate: {
        payload: Joi.object({
            // Validates the phone number in E.164 format
            phone_number: Joi.string().pattern(/^\+[1-9]\d{1,14}$/).required(),
            otp: Joi.string().pattern(/^[0-9]{6}$/).required()
        })
    },
    handler: async (request, h) => {

        const { apiService, userService } = request.services();
        const { phone_number, otp } = request.payload;

        try {
            await userService.verifyOtp({ phone_number, otp });
            return apiService.formatResponse(h, 'Phone number has been verified succesfully');
        }
        catch (err) {
            throw new Error(`Failed to verify the phone number : ${err}`);
        }
    }
});
