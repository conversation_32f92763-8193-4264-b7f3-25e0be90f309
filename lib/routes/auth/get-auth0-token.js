'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'users',
        description: 'Get auth0 token via grant type',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/token',
    auth: false,
    validate: {
        payload: Joi.object({
            username: Joi.string().required(),
            password: Joi.string().required(),
            grant_type: Joi.string().valid('password').required()
        })
    },
    handler: async (request, h) => {

        const { apiService, userService, authService, paymentMethodService, usageSummaryService } = request.services();
        const { username, password, grant_type } = request.payload;

        try {
            const { id_token } = await userService.getAuth0Token({ username, password, grant_type });

            if (!id_token) {
                return apiService.formatResponse(h, null, 401, ['Unauthorized']);
            }

            const { user, token } = await authService.preparePayloadForAuth0FromToken( { id_token } );

            try {
                await userService.sendUserToCustomerIO({ userId: user.id });
            }
            catch (error) {
                request.logger.error('Error sending user to Customer.io:', error);
            }

            try {

                await paymentMethodService.connectPaymentGateway({
                    user
                });

                await usageSummaryService.storeUsageSummary({ user });
            }
            catch (error) {
                request.logger.error('Error for connecting stripe:', error);
            }

            return apiService.formatResponse(h, { jwt: token }, 200);
        }
        catch (error) {
            request.logger.error({ error }, 'Failed to get auth0 token');
            return h.response({ error: 'Failed to get auth0 token' }).code(500);
        }
    }
});
