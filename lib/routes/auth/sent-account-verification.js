'use strict';

const { createRoute } = require('../../helpers/route_factory.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'users',
        description: 'resent account verification',
        disableResponseValidation: true
    },
    method: 'GET',
    path: '/verification/sent',
    auth: ['jwt'],
    handler: async (request, h) => {

        const { apiService, userService } = request.services();
        const { user } = request.auth.credentials;

        try {
            await userService.verifyAuth0User({ user });
            return apiService.formatResponse(h, 'Resent verification email successfully');
        }
        catch (err) {
            throw new Error(`${err}`);
        }
    }
});
