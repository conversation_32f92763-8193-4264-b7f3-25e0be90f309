'use strict';

const { createRoute } = require('../../helpers/route_factory.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'users',
        description: 'Verify account',
        disableResponseValidation: true
    },
    method: 'GET',
    path: '/verify-account',
    auth: ['jwt'],
    handler: async (request, h) => {

        const { apiService, userService } = request.services();
        const { user } = request.auth.credentials;

        try {
            await userService.updateAccountVerified({ user });
            return apiService.formatResponse(h, 'Verified email successfully');
        }
        catch (err) {
            throw new Error(`${err}`);
        }
    }
});
