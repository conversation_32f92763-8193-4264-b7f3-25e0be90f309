'use strict';

const Joi = require('joi');
const { UnauthenticatedError } = require('../../helpers/errors');

module.exports = {
    method: 'GET',
    path: '/login',
    options: {
        auth: 'oauth2'
    },
    handler: async (request, h) => {

        const { jwtService, userService } = request.services();

        const { credentials } = request.auth;

        request.logger.info({ credentials }, 'User is logging in');

        const { profile = {} } = credentials;
        const { email, email_verified = false, sub, given_name, family_name, name } = profile;

        const redirectToLoginWithError = ({ error, email, extraData = {} }) => {

            const { config: { domain }, client_id } = request.server.settings.app.auth.provider;
            const { info, path } = request;

            const logoutURL = `https://${domain}/v2/logout`; // NOTE: for now, this supports only auth0 as an auth provider
            const redirectProtocol = 'https'; // NOTE: we hardcode `https` as we know it's the one that will always be used from the "outside"
            const redirectBasePath = `${redirectProtocol}://${info.host}${path}`;
            const flashyDataBase64 = Buffer.from(JSON.stringify({ error, email, extraData })).toString('base64');
            const fullRedirectURL = `${redirectBasePath}?__flashy_data=${flashyDataBase64}`;
            request.logger.info({ error, email, logoutURL, fullRedirectURL },
                'Redirecting user to auth0 login (through a logout) due to an authorization error');

            return h.redirect(`${logoutURL}?client_id=${client_id}&returnTo=${encodeURI(fullRedirectURL)}`);
        };

        if (!email || !email_verified) {
            return redirectToLoginWithError({ error: 'Email not verified', email });
        }

        let user = await userService.getUserByEmail({ email });

        // Automatic user creation for Google OAuth2 users
        if (!user && sub && sub.startsWith('google-oauth2|')) {
            request.logger.info({ email, sub }, 'Creating new user for Google OAuth2');

            try {
                // Create a new user with default role as 'client'
                const newUser = {
                    email,
                    name: name || `${given_name || ''} ${family_name || ''}`.trim(),
                    role: 'client',
                    status: 'active'
                    // Add any other required fields with default values
                };

                user = await userService.createUser(newUser);

                request.logger.info({ userId: user.id, email }, 'New user created successfully for Google OAuth2');
            }
            catch (error) {
                request.logger.error({ error, email }, 'Failed to create new user for Google OAuth2');
                return redirectToLoginWithError({
                    error: 'Failed to create new user',
                    email,
                    extraData: { message: error.message }
                });
            }
        }

        if (!user) {
            return redirectToLoginWithError({ error: 'Unable to find user', email });
        }

        if (user.status !== 'active') {
            return redirectToLoginWithError({ error: 'User not active', email, extraData: { status: user.status } });
        }

        const payload = { role: user.role, email: user.email, name: user.name, id: user.id };

        if (user.role === 'client') {
            const client = await userService.getFormattedUser({ id: user.id, role: user.role });
            if (client.registration_guid === null) {
                // For Google OAuth2 users, we may not have auth0 metadata
                try {
                    const authUser = await userService.getAuth0UserByEmail({ email: client.email });
                    if (authUser && authUser.user_metadata && authUser.user_metadata.subClientRole) {
                        payload.subClientRole = authUser.user_metadata.subClientRole;
                    }
                    else if (sub && sub.startsWith('google-oauth2|')) {
                        // Set a default subClientRole for Google users if needed
                        payload.subClientRole = 'standard';
                    }
                }
                catch (error) {
                    request.logger.error({ error, email }, 'Error getting Auth0 user metadata');
                    // Continue without subClientRole for Google users
                }
            }

            payload.company_name = client.company_name;
            payload.parent_client_id = client.parent_client_id;
            payload.client_id = client.client_id;
        }
        else if (user.role === 'photographer') {
            const photographer = await userService.getFormattedUser({ id: user.id, role: user.role });
            payload.is_internal = photographer.is_internal;
        }

        const accessToken = await jwtService.generateToken({
            payload
        });

        request.logger.info({ email, role: user.role, accessToken, user }, 'User login successful');

        const redirectUriCookie = request.state[request.server.settings.app.cookies.redirectUri];

        const isDomainAllowed = (request.server.settings.app.auth.allowedCallbackDomains || ['https://app.flashy-local.com'])
            .map((domain) => redirectUriCookie.startsWith(domain))
            .reduce((a, b) => a || b, false);

        const result = Joi.string().uri({
            relativeOnly: !isDomainAllowed, // If the domain validation is not passed, validate only relative URIs
            allowRelative: true
        }).default('/').validate(redirectUriCookie);

        if (result.error) {
            throw new UnauthenticatedError(isDomainAllowed ? result.error : 'Invalid redirect URI (domain is not allowed)');
        }

        const redirectUri = result.value;
        try {
            await userService.sendUserToCustomerIO({ userId: user.id });
        }
        catch (error) {
            request.logger.error('Error sending user to Customer.io:', error);
        }

        h.unstate(request.server.settings.app.cookies.redirectUri);
        return h.redirect(`${redirectUri}?__bearer_access_token=${accessToken}`);
    }
};
