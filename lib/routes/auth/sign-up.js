'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');

const { createUser } = require('../../validation-schema/users/payloads.js');
const User = require('../../models/User');

const { types: typesNotification } = require('../../services/NotificationService.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'users',
        description: 'handle photographers signup process',
        response: Joi.object({
            user: Joi.object(User.JoiSchema)
        })
    },
    method: 'POST',
    path: '/signup/{role}',
    auth: 'optional',
    validate: {
        params: Joi.object({
            role: Joi.string().valid('photographer', 'client')
        }),
        payload: createUser,
        query: Joi.object({
            parent: Joi.string(),
            subClientRole: Joi.string().valid('manager', 'viewer')
        })

    },
    handler: async (request, h) => {

        const { apiService, userService, notificationService, usageSummaryService, subscriptionService } = request.services();
        const { role } = request.params;
        const { additionalInformations, user, paymentDetail = { type: 'test', details: {} }, password } = request.payload;
        const { parent: parentGuid, subClientRole } = request.query;
        const loggedInUser = request?.auth?.credentials?.user;

        if (subClientRole) {
            await subscriptionService.validateActionWithCurrentPlans({ user: loggedInUser });
        }

        //password checker according to auth0 password policies
        const savedUser = await userService.createUser({
            user, password, paymentDetail,
            additionalInformations, role, parentGuid,
            loggedInUser, subClientRole, isVerified: false });

        if (role === 'photographer') {
            // send welcome email to the photographer
            await notificationService.sendNotification({
                type: typesNotification.welcome,
                target: { photographer_id: savedUser.photographer_id, email: savedUser.email },
                metaData: { photographer_id: savedUser.photographer_id }
            });
            // send photographer registration notification
            await notificationService.sendNotification({
                type: typesNotification.newPhotographerRegistered,
                target: {},
                metaData: {
                    phId: savedUser.photographer_id,
                    phName: savedUser.name,
                    email: savedUser.email,
                    phone: savedUser.phone,
                    country: additionalInformations.country,
                    location: additionalInformations.main_location

                }
            });
        }
        else if (role === 'client') {
            if (!parentGuid) {
                // send welcome email to client
                notificationService.sendNotification({
                    type: typesNotification.welcome,
                    target: { client_id: savedUser.client_id, email: savedUser.email },
                    metaData: { client_id: savedUser.client_id }
                });

            }
            else {
                if (!password) {
                    // Generate a passwrd-reset link (ticket)
                    const response = await userService.generatePasswordResetLink({ email: user.email });

                    const type = typesNotification.accountCreated;
                    const metaData = {
                        ticket: response.ticket
                    };

                    const target = {
                        email: savedUser.email,
                        client_id: savedUser.client_id
                    };
                    notificationService.sendNotification({ type, target, metaData });
                }
            }

            // send an email to the admin
            // notificationService.sendNotification({
            //     type: typesNotification.newCustomerRegistered,
            //     metaData: {
            //         clientId: savedUser.client_id,
            //         company: savedUser.name,
            //         clientName: savedUser.name,
            //         email: savedUser.email,
            //         phone: savedUser.phone,
            //         country: additionalInformations.country,
            //         location: additionalInformations.main_location
            //     }
            // });

            if (subClientRole) {
                await usageSummaryService.storeUsageSummary({ user: loggedInUser }, true);
            }
        }

        return apiService.formatResponse(h, { user: savedUser });
    }
});
