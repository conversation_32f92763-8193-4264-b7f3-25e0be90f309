'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { UnauthorizedError } = require('../../helpers/errors');

const { createUser } = require('../../validation-schema/users/payloads.js');
const User = require('../../models/User');

const { types: typesNotification } = require('../../services/NotificationService.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'users',
        description: 'handle client registeration on behalf of the client',
        response: Joi.object({
            user: Joi.object(User.JoiSchema)
        })
    },
    method: 'POST',
    path: '/signup/admin/{role}',
    auth: ['jwt'],
    validate: {
        params: Joi.object({
            role: Joi.string().valid('photographer', 'client')
        }),
        payload: createUser,
        query: Joi.object({
            parent: Joi.string()
        })
    },
    handler: async (request, h) => {

        const { apiService, userService, notificationService } = request.services();
        const { additionalInformations, user, paymentDetail, password } = request.payload;
        const { parent: parentGuid } = request.query;

        const { tokenPayload: { role } } = request.auth.credentials;

        // Check if the user is authorized
        if (role !== 'admin') {
            throw new UnauthorizedError('Action is can\'t be performed by normal users');
        }

        // Create an auto-verified auth0 user
        const savedUser = await userService.createUser({
            user, password, paymentDetail,
            additionalInformations, role: 'client', parentGuid, verified: true
        });

        // Generate a passwrd-reset link (ticket)
        const response = await userService.generatePasswordResetLink({ email: user.email });

        const type = typesNotification.accountCreated;
        const metaData = {
            ticket: response.ticket
        };

        const target = {
            email: savedUser.email,
            client_id: savedUser.client_id
        };
        notificationService.sendNotification({ type, target, metaData });

        // send email notification
        notificationService.sendNotification({
            type: typesNotification.newCustomerRegistered,
            metaData: {
                clientId: savedUser.client_id,
                company: additionalInformations.company_name,
                clientName: savedUser.name,
                email: savedUser.email,
                phone: savedUser.phone,
                country: additionalInformations.country,
                location: additionalInformations.main_location,
                industry: additionalInformations.industry
            }
        });

        return apiService.formatResponse(h, { user: savedUser });
    }
});
