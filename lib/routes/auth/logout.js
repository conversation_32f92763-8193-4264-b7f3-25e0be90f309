'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'users',
        description: 'logout a user',
        response: Joi.object({
            success: Joi.boolean().example(true)
        })
    },
    method: 'POST',
    path: '/logout',
    auth: 'jwt',
    validate: {},
    handler: async (request, h) => {

        const { apiService, authService } = request.services();
        const { user } = request.auth.credentials;

        try {
            await authService.logoutUserSession(user.id);
        }
        catch ( error ) {

            return apiService.formatResponse(h, {
                success: false
            });
        }

        return apiService.formatResponse(h, {
            success: true
        });
    }
});
