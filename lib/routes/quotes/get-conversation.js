'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'conversations',
        description: 'Get the conversation history for a specific conversation',
        disableResponseValidation: true
    },
    method: 'GET',
    path: '/conversations/{conversationId}',
    auth: 'jwt',
    validate: {
        params: Joi.object({
            conversationId: Joi.number().integer().positive().required()
        })
    },
    handler: async (request, h) => {

        const { conversationsService, apiService } = request.services();
        const { user } = request.auth.credentials;
        const { conversationId } = request.params;

        try {

            // If the conversation exists and belongs to the user, get the history
            const history = await conversationsService.getConversationHistory(conversationId, user.id, 60);

            return apiService.formatResponse(h, {
                messages: history
            });
        }
        catch (error) {
            request.log(['error', 'get-conversation-history'], error);
            return h.response({ error: 'An error occurred while fetching the conversation history.' }).code(500);
        }
    }
});
