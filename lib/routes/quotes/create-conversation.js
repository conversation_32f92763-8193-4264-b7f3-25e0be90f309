'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'conversations',
        description: 'Create a new conversation for a user',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/conversations',
    auth: 'jwt',
    validate: {
        payload: Joi.object({
            metadata: Joi.object().default({})
        })
    },
    handler: async (request, h) => {

        const { conversationsService, apiService } = request.services();
        const { user } = request.auth.credentials;
        const { metadata } = request.payload;

        try {
            const conversation = await conversationsService.createConversation(user.id, metadata);
            return apiService.formatResponse(h, {
                conversation
            });
        }
        catch (error) {
            request.log(['error', 'create-conversation'], error);
            return h.response({ error: 'An error occurred while creating the conversation.' }).code(500);
        }
    }
});
