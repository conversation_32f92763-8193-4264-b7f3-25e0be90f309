'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { UnauthorizedError } = require('../../helpers/errors');
const { Readable } = require('stream');

module.exports = createRoute({
    documentation: {
        resourceTag: 'quotes',
        description: 'Generates a quote based on provided services and streams the result',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/quote',
    auth: 'jwt',
    validate: {
        payload: Joi.object({
            services: Joi.array().items(Joi.object({
                description: Joi.string().required(),
                quantity: Joi.number().required()
            })).min(1).required()
        })
    },
    handler: async (request, h) => {

        const { quoteService } = request.services();
        const { services } = request.payload;
        const { tokenPayload: { role } } = request.auth.credentials;

        if (role !== 'admin') {
            throw new UnauthorizedError('Only admins can generate quotes');
        }

        try {
            const { quoteStream, vectorStoreResults } = await quoteService.generateQuoteStream(services);

            const metadata = await vectorStoreResults.map((result) => result.metadata);

            // Create a new stream that combines the quote and metadata
            const combinedStream = new Readable({
                read() {}
            });

            // Send the metadata first
            combinedStream.push(`${JSON.stringify({ type: 'metadata', data: metadata })  }\n`);

            // Pipe the quote stream to the combined stream
            quoteStream.on('data', (chunk) => {

                combinedStream.push(`${JSON.stringify({ type: 'quote', data: chunk.toString() })  }\n`);
            });

            quoteStream.on('end', () => {

                combinedStream.push(null);
            });

            return h.response(combinedStream)
                .type('text/event-stream')
                .header('Cache-Control', 'no-cache')
                .header('Connection', 'keep-alive')
                .header('Content-Encoding', 'none')
                .header('Transfer-Encoding', 'chunked');
        }
        catch (error) {
            request.log(['error', 'quote-generation'], error);
            return h.response({ error: 'An error occurred while generating the quote.' }).code(500);
        }
    }
});
