'use strict';

const { createRoute } = require('../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'conversations',
        description: 'Get all conversations for the authenticated user',
        disableResponseValidation: true
    },
    method: 'GET',
    path: '/conversations',
    auth: 'jwt',
    handler: async (request, h) => {

        const { conversationsService, apiService } = request.services();
        const { user } = request.auth.credentials;

        try {
            const conversations = await conversationsService.getUserConversations(user.id);
            return apiService.formatResponse(h, {
                conversations
            });
        }
        catch (error) {
            request.log(['error', 'get-conversations'], error);
            return h.response({ error: 'An error occurred while fetching conversations.' }).code(500);
        }
    }
});
