/* eslint-disable @hapi/scope-start */
'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { Readable } = require('stream');

module.exports = createRoute({
    documentation: {
        resourceTag: 'invoices',
        description: 'Query the AI assistant and stream the response and context',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/query-invoices',
    auth: 'jwt',
    validate: {
        payload: Joi.object({
            query: Joi.string().required(),
            conversationId: Joi.number().integer().positive().optional()
        })
    },
    handler: (request, h) => {
        const { assistantService } = request.services();
        const { query, conversationId } = request.payload;
        const { user } = request.auth.credentials;

        try {
            const stream = new Readable({
                read() {}
            });

            // Start the query process
            const queryProcess = assistantService.query(query, conversationId, user.id);

            queryProcess.then(async (result) => {
                // Stream the answer chunks and context
                for await (const chunk of result.stream) {
                    stream.push(`${JSON.stringify(chunk)}\n`);
                }

                stream.push(null); // End of stream
            }).catch((error) => {
                stream.push(`${JSON.stringify({
                    type: 'error',
                    data: 'An error occurred while processing your request.'
                })}\n`);
                stream.push(null); // End of stream
                throw new Error('error query invoices', error);

            });

            return h.response(stream)
                .type('text/event-stream')
                .header('Cache-Control', 'no-cache')
                .header('Connection', 'keep-alive')
                .header('Content-Encoding', 'none')
                .header('Transfer-Encoding', 'chunked');
        }
        catch (error) {
            request.log(['error', 'assistant-query'], error);
            return h.response({ error: 'An error occurred while querying the assistant.' }).code(500);
        }
    }
});
