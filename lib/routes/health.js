'use strict';

const { createRoute } = require('../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        description: 'Health check endpoint',
        response: {
            message: 'Server is healthy'
        }
    },
    method: 'GET',
    path: '/health',
    auth: false,
    handler: (request, h) => {

        return h.response({ message: 'Server is healthy' }).code(200);
    }
});
