'use strict';

const Joi = require('joi');
const Boom = require('@hapi/boom');
const { createRoute } = require('../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'videos',
        description: 'Generate a video using the Minimax Video-01-Director model with predefined camera movements',
        response: Joi.object({
            prediction_id: Joi.string().required(),
            status: Joi.string().required()
        })
    },
    method: 'POST',
    path: '/videos/generate-with-director',
    auth: ['jwt', 'api-key'],
    options: {
        payload: {
            output: 'stream',
            parse: true,
            multipart: true,
            allow: 'multipart/form-data',
            maxBytes: 10 * 1024 * 1024
        }
    },
    validate: {
        payload: Joi.object({
            content_type: Joi.string().valid('ecommerce', 'interiors', 'fnb', 'headshots').required()
                .description('The type of content (ecommerce, interiors, fnb, headshots)'),
            label: Joi.string().required()
                .description('The specific camera movement label within the content type')
        }).required()
    },
    handler: async (request, h) => {

        const { apiService, replicateService } = request.services();
        const { content_type, label } = request.payload;
        const image = request.payload.image._data;

        try {
            // Call Replicate API for video generation through the service
            const predictionId = await replicateService.generateVideoWithDirector({
                content_type,
                label,
                first_frame_image: image
            });

            return apiService.formatResponse(h, {
                prediction_id: predictionId,
                status: 'processing'
            });
        }
        catch (error) {
            request.log(['error'], error);

            if (error.isBoom) {
                throw error;
            }

            switch (error.code) {
                case 'AccessDenied':
                    throw Boom.forbidden('Access denied to S3 bucket. Please check bucket permissions.');
                case 'UNSUPPORTED_FILE_TYPE':
                    throw Boom.unsupportedMediaType('Invalid file type. Supported file types are jpeg, jpg, png, webp');
                case 'DOWNLOAD_ERROR':
                    throw Boom.preconditionFailed('Failed to download image');
                case 'RATE_LIMIT_EXCEEDED':
                    throw Boom.tooManyRequests('Request limit exceeded');
                case 'REPLICATE_API_ERROR':
                    throw Boom.badGateway('Failed to process video with Replicate API');
                case 'INVALID_PARAMETERS':
                    throw Boom.badRequest(`Invalid parameters: ${error.message}`);
                default:
                    throw Boom.badImplementation('An error occurred while processing the video');
            }
        }
    }
});
