'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { UnauthorizedError, NotFoundError } = require('../../helpers/errors');

module.exports = createRoute({
    documentation: {
        resourceTag: 'users',
        description: 'Updates the role of a sub-user',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/user/update-sub-role/{id}',
    auth: 'jwt',
    validate: {
        params: Joi.object({
            id: Joi.number().required() // The ID of the user whose role is being updated
        }),
        payload: Joi.object({
            role: Joi.string().valid('manager', 'viewer').required() // Ensuring role is either 'manager' or 'viewer'
        })
    },
    handler: async (request, h) => {

        try {
            const { userService, authService, apiService } = request.services();
            const { id } = request.params; // The Auth0 user ID
            const { role: subClientRole } = request.payload; // The new role
            const { tokenPayload: { role, parent_client_id, company_name, id: mainUserId } } = request.auth.credentials;

            const isSubUser = parent_client_id !== null;
            const isB2C = company_name && company_name.trim().endsWith('B2C');

            // Check if the user is authorized to perform the action
            if (role !== 'client' || isSubUser || isB2C) {
                throw new UnauthorizedError('You are not allowed to perform this action');
            }

            // Check if the user is authorized to access this route
            authService.checkRouteAuthorization({ role, route: 'update-sub-role' });

            // Fetch main user, sub user, and sub client
            const mainUser = await userService.getUser({ id: mainUserId });
            const subUser = await userService.getFormattedUser({ id, role });

            if (!subUser) {
                throw new NotFoundError('Sub user not found');
            }

            // Check user ownership
            if (subUser.parent_client_id !== mainUser.client_id) {
                throw new UnauthorizedError('You are not allowed to perform this action');
            }

            // Use the updateAuth0UserMetadata function to update the user's role
            const updatedUser = await userService.updateAuth0UserMetadata(subUser.email, subClientRole);

            return apiService.formatResponse(h, {
                user: updatedUser
            });

        }
        catch (error) {
            // Handle errors
            if (error instanceof UnauthorizedError || error instanceof NotFoundError) {
                return h.response({ message: error.message }).code(error.statusCode);
            }

            return h.response({ message: 'An unexpected error occurred' }).code(500);

        }
    }
});
