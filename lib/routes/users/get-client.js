'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { softClient }  = require('../../validation-schema/users/responses.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'users',
        description: 'return client based on its guid',
        response: Joi.object({
            user: softClient
        })
    },
    method: 'GET',
    path: '/user/client/{guid}',
    auth: false,
    validate: {
        params: Joi.object({
            guid: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const { apiService, userService } = request.services();

        const { guid } = request.params;

        const formattedUser = await userService.getClientByGuid({ guid });

        return apiService.formatResponse(h, {
            user: formattedUser
        });
    }
});
