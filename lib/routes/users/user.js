'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { softUser }  = require('../../validation-schema/users/responses.js');
const { convertStorageSizeStringToBytes } = require('../../helpers/utils');
const { ROLES } = require('../../helpers/role.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'users',
        description: 'return user based on id and role',
        response: Joi.object({
            user: softUser
        })
    },
    method: 'POST',
    path: '/user/{id}',
    auth: 'jwt',
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        }),
        payload: Joi.object({
            requestedUserRole: Joi.string().valid('admin', 'photographer', 'client')
        })
    },
    handler: async (request, h) => {

        const { apiService, userService, authService, subscriptionService, usageSummaryService } = request.services();

        const { params: { id }, payload: { requestedUserRole } } = request;

        const { user, tokenPayload: { role } } = request.auth.credentials;

        await authService.checkUserOwnership({
            affectedUser: { id },
            user,
            role
        });

        const formattedUser = await userService.getFormattedUser({ id, user, role: requestedUserRole });

        if (formattedUser && !formattedUser.account_verified) {
            formattedUser.account_verified = !formattedUser.account_verified && !formattedUser.auth_provider_reference;
        }

        if (formattedUser && formattedUser.role === 'client') {
            const { plan, billing } = await subscriptionService.getCurrentPlanFromUser({ user });
            const client = await user.client;
            const currentUsageSummary = await usageSummaryService.getCurrentUsageSummary({ client });

            if (currentUsageSummary) {

                plan.total_members = currentUsageSummary.total_members;
                plan.total_storage_size = currentUsageSummary.total_storage_size;
                plan.plans.max_storage_size = convertStorageSizeStringToBytes(plan.plans.max_storage_size);

                formattedUser.plan = plan;
                formattedUser.billing = billing;
                formattedUser.sub_client_role = user.subClientRole ? user.subClientRole : ROLES.SUB_CLIENT.ADMIN;
            }
        }

        return apiService.formatResponse(h, {
            user: formattedUser
        });
    }
});
