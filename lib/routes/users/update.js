'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { rethrow, NotFoundError } = require('../../helpers/errors');
const { user }  = require('../../validation-schema/users/responses.js');
const { updateUser } = require('../../validation-schema/users/payloads.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'users',
        description: 'updates a user',
        response: Joi.object({
            user
        })
    },
    method: 'POST',
    path: '/user/update/{id}',
    auth: 'jwt',
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        }),
        payload: updateUser
    },
    handler: async (request, h) => {

        const { apiService, authService, userService } = request.services();

        const {
            params: { id },
            payload: {
                role: userToBeUpdatedRole,
                additionalInformations,
                user: newUserValues,
                status,
                services
            }
        } = request;

        const { user, tokenPayload: { role } } = request.auth.credentials;

        const affectedUser = await userService.getUserByAffectedRole({ id, role: userToBeUpdatedRole })
            .catch(rethrow(NotFoundError, 'Unable to find the user'));

        // if (affectedUser.role !== userToBeUpdatedRole) {
        //     throw new ValidationError('Invalid role');
        // }

        authService.checkUserAuthorization({
            affectedUser,
            user,
            role,
            action: 'update-user'
        });

        await authService.checkUserOwnership({
            affectedUser,
            user,
            role
        });

        const authorizeStatusUpdate = authService.checkUpdateUserStatus({
            userToBeUpdatedRole,
            role,
            affectedUser,
            user
        });

        const updatedUser = await userService.updateUser({
            affectedUser,
            userToBeUpdatedRole,
            additionalInformations,
            user: newUserValues,
            ...(userToBeUpdatedRole === 'photographer' && role === 'admin' && { services }),
            ...(authorizeStatusUpdate && { status })
        });

        return apiService.formatResponse(h, {
            user: updatedUser
        });
    }
});
