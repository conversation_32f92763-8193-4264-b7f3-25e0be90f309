'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { UnauthorizedError, NotFoundError } = require('../../helpers/errors');

module.exports = createRoute({
    documentation: {
        resourceTag: 'users',
        description: 'Deletes a sub-user based on id',
        disableResponseValidation: true
    },
    method: 'DELETE',
    path: '/user/delete/{id}',
    auth: 'jwt',
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        try {
            const { userService, authService, apiService } = request.services();
            const { tokenPayload: { role, parent_client_id, company_name, id: mainUserId } } = request.auth.credentials;
            const { id } = request.params;

            const isSubUser = parent_client_id !== null;
            const isB2C = company_name && company_name.trim().endsWith('B2C');

            // Check if the user is authorized to perform the action
            if (role !== 'client' || isSubUser || isB2C) {
                throw new UnauthorizedError('You are not allowed to perform this action');
            }

            // Check if the user is authorized to access this route
            authService.checkRouteAuthorization({ role, route: 'delete-sub-user' });

            // Fetch main user, sub user, and sub client
            const mainUser = await userService.getUser({ id: mainUserId });
            const subUser = await userService.getFormattedUser({ id, role });

            if (!subUser) {
                throw new NotFoundError('Sub user not found');
            }

            // Check user ownership
            if (subUser.parent_client_id !== mainUser.client_id) {
                throw new UnauthorizedError('You are not allowed to perform this action');
            }

            const deleted = await userService.deleteSubClientById({ subUser, affectedUser: mainUser });

            if (!deleted) {
                throw new NotFoundError('Failed to delete sub-user');
            }

            return apiService.formatResponse(h, {
                subUser
            });

        }
        catch (error) {
            // Handle known errors
            if (error instanceof UnauthorizedError || error instanceof NotFoundError) {
                return h.response({ message: error.message }).code(error.statusCode);
            }

            // Handle unknown errors
            return h.response({ message: 'An unexpected error occurred' }).code(500);
        }
    }
});
