'use strict';

const { createRoute } = require('../../../helpers/route_factory');

const Joi = require('joi');

module.exports = createRoute({
    documentation: {
        resourceTag: 'users',
        description: 'synchronize client payment details given user',
        response: Joi.object({
            status: Joi.string()
        })
    },
    method: 'POST',
    path: '/users/action/synchronize-client-payment-data',
    auth: ['jwt'],
    validate: {
        payload: Joi.object({
            billing_address: Joi.object({
                city: Joi.string().required(),
                country: Joi.string().required(),
                line1: Joi.string().required(),
                line2: Joi.string(),
                postal_code: Joi.string().required(),
                state: Joi.string().required()
            }).required(),
            tax_id: Joi.string()
        })
    },
    handler: async (request, h) => {

        const { apiService, userService, authService } = request.services();

        const { billing_address, tax_id } = request.payload;

        const { user, tokenPayload: { role } } = request.auth.credentials;

        authService.checkRouteAuthorization({ role, route: 'synchronize-client-payment-data' });

        await userService.synchronizePaymentData({ user, billing_address, tax_id });

        return apiService.formatResponse(h, {
            status: 'OK'
        });
    }
});
