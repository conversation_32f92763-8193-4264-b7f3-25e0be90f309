'use strict';

const { createRoute } = require('../../../helpers/route_factory');
const { search } = require('../../../validation-schema/users/payloads.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'users',
        description: 'Return list of users',
        disableResponseValidation: true // Response validation is disabled for now
    },
    method: 'POST',
    path: '/users/action/search',
    auth: ['jwt'],
    validate: {
        payload: search
    },
    handler: async (request, h) => {

        const { apiService, userService, authService } = request.services();

        // Extract filters and parameters from the payload
        const {
            filters: {
                role: filteredRole,
                services,
                locations,
                statuses,
                searchQuery,
                contentType,
                revenueRange
            },
            pagination: {
                size,
                offset
            } = {}, // Default pagination to an empty object if unset
            fetchAll = false // Default fetchAll to false if unset
        } = request.payload;

        const { user, tokenPayload: { role } } = request.auth.credentials;

        // Authorization check for the route
        authService.checkRouteAuthorization({ role, route: 'user-search' });

        // Handle different roles with specific logic
        if (filteredRole === 'photographer') {
            const { photographers, total } = await userService.getPhotographers({
                size,
                offset,
                searchQuery,
                services,
                locations,
                statuses,
                contentType
            });

            return apiService.formatResponse(h, {
                users: photographers,
                total
            });
        }
        else if (filteredRole === 'client') {
            // Only clients support fetchAll
            const { customers, total } = await userService.getCustomers({
                searchQuery,
                locations,
                statuses,
                revenueRange,
                fetchAll, // Pass fetchAll to the service for internal handling
                offset,
                size
            });

            return apiService.formatResponse(h, {
                users: customers,
                total: fetchAll ? customers.length : total // Adjust total based on fetchAll
            });
        }
        else if (filteredRole === 'subClient') {
            const subClients = await userService.getSubClients({
                parentId: user.client_id
            });

            return apiService.formatResponse(h, {
                users: subClients
            });
        }
    }
});
