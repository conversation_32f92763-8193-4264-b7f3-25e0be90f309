'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');
const { checker }  = require('../../../validation-schema/users/responses.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'users',
        description: 'return existence of user with that specified email',
        response: Joi.object({
            checker
        })
    },
    method: 'GET',
    path: '/user/action/email-checker/{email}',
    auth: false,
    validate: {
        params: Joi.object({
            email: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const { apiService, userService } = request.services();

        const { email } = request.params;

        const checker = await userService.emailChecker({ email });

        return apiService.formatResponse(h, {
            checker
        });
    }
});
