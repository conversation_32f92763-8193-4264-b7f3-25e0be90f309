'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'client',
        description: '<PERSON><PERSON> abandoned registration by creating a client in Monday CRM',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/user/action/abandon',
    auth: false, // Update this based on your authentication requirement
    validate: {
        payload: Joi.object({
            firstName: Joi.string().allow(''),
            lastName: Joi.string().allow(''),
            email: Joi.string().email().allow(''),
            phone: Joi.string().allow(''),
            city: Joi.string().allow(''),
            status: Joi.string().default('1. City'),
            campaignSource: Joi.string().allow(''),
            campaignMedium: Joi.string().allow(''),
            campaignName: Joi.string().allow('')
        })
    },
    handler: async (request, h) => {

        const { apiService, mondayService } = request.services();
        const { payload } = request;

        try {
            const clientData = {
                ...payload,
                status: payload.status || 'Abandoned'
            };

            await mondayService.createClient(clientData, 'CANCELED');

            return apiService.formatResponse(h, 'Created abondoned client monday item');
        }
        catch (err) {
            throw new Error(`Failed to create an abandoned cart item { ERROR MSG }: ${err}`);
        }
    }
});
