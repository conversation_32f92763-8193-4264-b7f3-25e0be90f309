'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'users',
        description: 'Return overall client revenue range (Admin only)',
        response: Joi.object({
            range: Joi.object({
                low: Joi.number().integer(),
                high: Joi.number().integer()
            })
        })
    },
    method: 'GET',
    path: '/user/action/get-revenue-range',
    auth: {
        strategies: ['jwt']
    },
    validate: {},
    handler: async (request, h) => {

        const { apiService, userService } = request.services();
        const { tokenPayload: { role } } = request.auth.credentials;

        // Additional check to ensure the user is an admin
        if (role !== 'admin') {
            return apiService.error(h, 403, ['Admin access required']);
        }

        try {
            const range = await userService.getClientRevenueRange();
            return apiService.formatResponse(h, { range });
        }
        catch (error) {
            request.logger.error('Error fetching client revenue range:', error);
            return apiService.error(h, 500, [error.message]);
        }
    }
});
