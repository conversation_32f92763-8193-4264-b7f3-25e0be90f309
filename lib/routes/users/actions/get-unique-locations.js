'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'users',
        description: 'Return unique user locations (Admin only)',
        response: Joi.object({
            locations: Joi.array().items(Joi.string())
        })
    },
    method: 'GET',
    path: '/user/action/get-unique-locations/{targetRole}',
    auth: {
        strategies: ['jwt']
    },
    validate: {
        params: Joi.object({
            targetRole: Joi.string().valid('client').when(Joi.ref('$auth.credentials.tokenPayload.role'), {
                is: Joi.valid('admin'),
                then: Joi.valid('client', 'photographer')
            }).required()
        })
    },
    handler: async (request, h) => {

        const { apiService, userService } = request.services();
        const { tokenPayload: { role } } = request.auth.credentials;
        const { targetRole } = request.params;

        // Additional check to ensure the user is an admin
        if (role !== 'admin') {
            return apiService.error(h, 403, ['Admin access required']);
        }

        try {
            const locations = await userService.getAllUniqueLocations(targetRole);
            return apiService.formatResponse(h, { locations });
        }
        catch (error) {
            request.logger.error('Error fetching unique locations:', error);
            return apiService.error(h, 500, [error.message]);
        }
    }
});
