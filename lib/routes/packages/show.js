'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { pack }  = require('../../validation-schema/packages/responses.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'packages',
        description: 'returns package based on id',
        response: Joi.object({
            pack
        })
    },
    method: 'GET',
    path: '/package/{id}',
    auth: 'jwt',
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const { apiService, packageService } = request.services();

        const { id } = request.params;

        const { tokenPayload: { role } } = request.auth.credentials;

        const { pack } = await packageService.getPackage({ id, role });

        return apiService.formatResponse(h, {
            pack
        });
    }
});
