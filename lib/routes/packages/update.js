'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { rethrow, ValidationError } = require('../../helpers/errors');
const { pack }  = require('../../validation-schema/packages/responses.js');
const { create } = require('../../validation-schema/packages/payloads.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'packages',
        description: 'updates a package',
        response: Joi.object({
            pack
        })
    },
    method: 'POST',
    path: '/package/update/{id}',
    auth: ['jwt'],
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        }),
        payload: create
    },
    handler: async (request, h) => {

        const { apiService, authService, packageService, serviceService } = request.services();

        const {
            params: { id },
            payload: {
                name,
                service_id,
                price,
                photographer_revenue,
                duration,
                picture_number,
                description,
                is_plus,
                hide,
                package_type
            }
        } = request;

        const { tokenPayload: { role } } = request.auth.credentials;

        await serviceService.getService({ id: service_id }).catch(rethrow(ValidationError, 'Package can not be updated due to non-existent service'));

        authService.checkRouteAuthorization({ role, route: 'update-package' });

        const { updatedPackage } = await packageService.updatePackage({
            id,
            role,
            newValues: {
                name,
                service_id,
                price,
                photographer_revenue,
                duration,
                picture_number,
                description,
                is_plus,
                package_type,
                status: (hide) ? 'hidden' : 'none',
                last_update: new Date()
            } });

        return apiService.formatResponse(h, {
            pack: updatedPackage
        });
    }
});
