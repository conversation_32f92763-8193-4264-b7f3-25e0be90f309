'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { rethrow, ValidationError } = require('../../helpers/errors');
const { pack }  = require('../../validation-schema/packages/responses.js');
const { create } = require('../../validation-schema/packages/payloads.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'packages',
        description: 'creates a package',
        response: Joi.object({
            pack
        })
    },
    method: 'POST',
    path: '/package',
    auth: ['jwt'],
    validate: {
        payload: create
    },
    handler: async (request, h) => {

        const { apiService, authService, packageService, serviceService, stripeService } = request.services();

        const {
            name,
            service_id,
            price,
            photographer_revenue,
            duration,
            picture_number,
            description,
            is_plus,
            hide,
            package_type
        } = request.payload;

        const { tokenPayload: { role } } = request.auth.credentials;

        await serviceService.getService({ id: service_id }).catch(rethrow(ValidationError, 'Package can not be created due to non-existent service'));

        authService.checkRouteAuthorization({ role, route: 'create-package' });

        let productDetails = null;
        if (package_type === 'b2c') {
            const { product, price: productPrice } = await stripeService.createProduct({
                name,
                description,
                amount: `${price}00`
            });
            productDetails = { product, productPrice };
        }

        const { savedPackage } = await packageService.createPackage({
            name,
            service_id,
            price,
            photographer_revenue,
            duration,
            picture_number,
            description,
            is_plus,
            status: (hide) ? 'hidden' : 'none',
            package_type,
            stripe_product_id: productDetails ? productDetails.product.id : null,
            price_tag: productDetails ? productDetails.productPrice.id : null
        });

        return apiService.formatResponse(h, {
            pack: savedPackage
        });
    }
});
