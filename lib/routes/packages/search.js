'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { pack }  = require('../../validation-schema/packages/responses.js');
const { search } = require('../../validation-schema/packages/payloads.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'packages',
        description: 'returns packages',
        response: Joi.object({
            packages: Joi.array().items(pack).required()
        })
    },
    method: 'POST',
    path: '/packages/search',
    auth: 'jwt',
    options: {
        auth: {
            strategy: 'jwt',
            mode: 'optional'
        }
    },
    validate: {
        payload: search
    },
    handler: async (request, h) => {

        const { apiService, packageService } = request.services();

        //TODO add sorting if required

        const {
            filters: { services }
        } = request.payload;

        let role;

        // Check if the request is authenticated
        if (request.auth.isAuthenticated && request.auth.credentials) {
            const { tokenPayload: { role: userRole } } = request.auth.credentials;
            role = userRole;
        }
        else {
            role = 'client'; // Default role if not authenticated
        }

        const { packages } = await packageService.getPackages({ services, role });

        return apiService.formatResponse(h, {
            packages
        });
    }
});
