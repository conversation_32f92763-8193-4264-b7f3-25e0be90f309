'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { pack }  = require('../../validation-schema/packages/responses.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'packages',
        description: 'deletes package based on id',
        response: Joi.object({
            pack
        })
    },
    method: 'DELETE',
    path: '/package/delete/{id}',
    auth: 'jwt',
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const { apiService, packageService, authService } = request.services();

        const { id } = request.params;

        const { tokenPayload: { role } } = request.auth.credentials;

        authService.checkRouteAuthorization({ role, route: 'delete-package' });

        const { deletedPackage } = await packageService.deletePackage({ id });

        return apiService.formatResponse(h, {
            pack: deletedPackage
        });
    }
});
