'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const PaymentMethod = require('../../models/PaymentMethod');

module.exports = createRoute({
    documentation: {
        resourceTag: 'stripe',
        description: 'Get Stripe payment method list for a client',
        response: Joi.object({
            paymentMethods: Joi.array().items(Joi.object(PaymentMethod.JoiSchema))
        })
    },
    method: 'GET',
    path: '/stripe/payment-methods',
    auth: ['jwt'],
    validate: {},
    handler: async (request, h) => {

        const { authService, apiService, paymentMethodService } = request.services();
        const { user, tokenPayload: { role } } = request.auth.credentials;
        authService.checkRouteAuthorization({ role, route: 'payment-method-management' });

        try {
            const paymentMethods = await paymentMethodService.getPaymentMethods({ user });
            return apiService.formatResponse(h, { paymentMethods });
        }
        catch (err) {
            console.error('err', err);
            throw new Error(`Failed to get payment methods: ${err.message}`);
        }
    }
});
