'use strict';

const { createRoute } = require('../../helpers/route_factory');
const Joi = require('joi');
const Billing = require('../../models/Billing');

module.exports = createRoute({
    documentation: {
        resourceTag: 'stripe',
        description: 'Pay a Stripe setup subscription plan',
        response: Joi.object({
            billing: Joi.object(Billing.JoiSchema).optional().allow(null),
            required3ds: Joi.object({
                clientSecret: Joi.string().optional()
            }).allow(null).optional()
        })
    },
    method: 'POST',
    path: '/stripe/subscription-plan/pay',
    auth: ['jwt'],
    validate: {
        payload: Joi.object({
            plan_id: Joi.number().required()
        })
    },
    handler: async (request, h) => {

        const { authService, apiService, subscriptionService, stripeService } = request.services();
        const { plan_id } = request.payload;
        const { user, tokenPayload: { role } } = request.auth.credentials;
        authService.checkRouteAuthorization({ role, route: 'subscription-management' });

        try {

            const paymentGateways = await user.userPaymentGateways;

            if (!paymentGateways || paymentGateways.length === 0) {
                throw new Error('No payment gateway connected');
            }

            const paymentGatewayReference = paymentGateways[0].reference;

            const currentSubscription = await stripeService.getCurrentActiveSubscription({
                customer: paymentGatewayReference
            });

            if (currentSubscription) {
                const { subscription, required3ds } = await subscriptionService.upgradeSubscription({ user, plan_id, currentSubscription });
                return apiService.formatResponse(h, { billing: subscription, required3ds });
            }

            const { billing, required3ds } = await subscriptionService.paySubscriptionPlan({ user, plan_id });
            return apiService.formatResponse(h, { billing, required3ds });
        }
        catch (err) {
            throw new Error(`Failed to pay subscription plan: ${err.message}`);
        }
    }
});
