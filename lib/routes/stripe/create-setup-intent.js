'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'stripe',
        description: 'Create a Stripe setup intent for a client',
        response: Joi.object({
            client_secret: Joi.string(),
            id: Joi.string(),
            status: Joi.string()
        })
    },
    method: 'POST',
    path: '/stripe/create-setup-intent',
    auth: ['jwt'],
    validate: {},
    handler: async (request, h) => {

        const { apiService, stripeService, paymentMethodService } = request.services();
        const { user } = request.auth.credentials;

        try {
            let paymentProvider = await paymentMethodService.getProviderGateway({ user });

            if (!paymentProvider) {
                paymentProvider = await paymentMethodService.connectPaymentGateway({ user });
            }

            const { id, client_secret, status } = await stripeService.createSetupIntent(
                { user_id: user.id, reference: paymentProvider.reference });
            return apiService.formatResponse(h, { id, client_secret, status });
        }
        catch (err) {
            console.error('Cancel stripe subscription error', err);
            throw new Error(`Failed to create a setup intent: ${err.message}`);
        }
    }
});
