'use strict';

const { createRoute } = require('../../helpers/route_factory');
const Joi = require('joi');

module.exports = createRoute({
    documentation: {
        resourceTag: 'stripe',
        description: 'Cancel a Stripe setup subscription plan',
        response: Joi.object({
            success: Joi.boolean().example(true)
        })
    },
    method: 'POST',
    path: '/stripe/subscription-plan/cancel',
    auth: ['jwt'],
    validate: {},
    handler: async (request, h) => {

        const { authService, apiService, subscriptionService } = request.services();
        const { user, tokenPayload: { role } } = request.auth.credentials;
        authService.checkRouteAuthorization({ role, route: 'subscription-management' });

        try {
            await subscriptionService.cancelSubscription({ user, immediately: false });
            return apiService.formatResponse(h, { success: true });
        }
        catch (err) {
            console.error('Cancel stripe subscription error', err);
            return apiService.formatResponse(h, { success: false });
        }
    }
});
