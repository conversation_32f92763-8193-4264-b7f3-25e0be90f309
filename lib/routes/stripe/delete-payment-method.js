'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'stripe',
        description: 'Delete Stripe payment method for a client',
        response: Joi.object({
            success: Joi.boolean().example(true)
        })
    },
    method: 'DELETE',
    path: '/stripe/payment-methods/{id}',
    auth: ['jwt'],
    validate: {
        params: Joi.object({
            id: Joi.number().integer().required()
        })
    },
    handler: async (request, h) => {

        const { authService, apiService, paymentMethodService } = request.services();
        const { user, tokenPayload: { role } } = request.auth.credentials;
        authService.checkRouteAuthorization({ role, route: 'payment-method-management' });
        const { id } = request.params;

        try {
            await paymentMethodService.deletePaymentMethod({ user, id });
            return apiService.formatResponse(h, { success: true });
        }
        catch (err) {
            console.error('err', err);
            return apiService.formatResponse(h, { success: false });
        }
    }
});
