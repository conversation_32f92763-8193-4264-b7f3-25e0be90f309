'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'stripe',
        description: 'Create a Stripe session for a client',
        response: Joi.object({
            sessionId: Joi.string()
        })
    },
    method: 'POST',
    path: '/stripe/create-session',
    auth: false, // Update this based on your authentication requirement
    validate: {
        payload: Joi.object({
            name: Joi.string().optional(),
            email: Joi.string().email().required(),
            phone: Joi.string().optional(),
            country: Joi.string().optional(),
            main_location: Joi.string().required(),
            package_id: Joi.number().required(),
            city: Joi.string().required(),
            state: Joi.string().required(),
            formatted_address: Joi.string().required(),
            utc_offset_minutes: Joi.number().integer().required(),
            from: Joi.string().required(),
            duration: Joi.number().required(),
            utm_campaign: Joi.string().allow('').allow(null),
            utm_medium: Joi.string().allow('').allow(null),
            utm_source: Joi.string().allow('').allow(null),
            coupon_code: Joi.string().allow('').allow(null)
        })
    },
    handler: async (request, h) => {

        const { apiService, packageService, stripeService } = request.services();
        const { payload } = request;

        try {
            let customer;

            const fetchedCustomer = await stripeService.getCustomerByEmail({ email: payload.email });

            if (fetchedCustomer) {
                customer = fetchedCustomer;
            }
            else {
                const createdCustomer = await stripeService.createCustomer({ payload });
                customer = createdCustomer;
            }

            const pkg = await packageService.getPackage({ id: payload.package_id, role: 'client' });

            // Check if a coupon code exists in the payload
            let discounts;
            if (payload.coupon_code && payload.coupon_code !== '') {
                const coupon = await stripeService.getCouponByName(payload.coupon_code);
                if (coupon) {
                    // Check if the coupon is valid and has not expired
                    if (coupon.valid && (!coupon.valid_until || new Date(coupon.valid_until) > new Date())) {
                        discounts = [
                            {
                                coupon: coupon.id
                            }
                        ];
                    }
                    else {
                        throw new Error(`Coupon ${payload.coupon_code} is not valid or has expired.`);
                    }
                }
                else {
                    throw new Error(`Coupon ${payload.coupon_code} is not valid or does not exist.`);
                }
            }

            const currentHost = process.env.CURRENT_HOST || 'https://app.getflashy.com';

            const lineItem = {
                price: pkg.pack.price_tag, // Replace with your price ID
                quantity: 1 // Adjust as needed
            };

            // Add tax rates if they exist
            if (process.env.STRIPE_TAX_ID) {
                // tax_rates: ['txr_1KZ7E0KJrLtygmVmJoL7leoa'] // prod
                // tax_rates: ['txr_1OPmlQE4MlwVJK0Q7t21H7WL'] // dev
                lineItem.tax_rates = [process.env.STRIPE_TAX_ID];
            }

            // Create Stripe session with metadata and discounts
            const session = await stripeService.stripe.checkout.sessions.create({
                customer: customer.id,
                success_url: `${currentHost}/stripe-success`,
                cancel_url: `${currentHost}/stripe-error`,
                line_items: [
                    lineItem
                ],
                mode: 'payment',
                metadata: { ...payload, package_name: pkg.name },
                discounts // Apply discounts if they exist
            });

            return apiService.formatResponse(h, { sessionId: session.id });
        }
        catch (err) {
            throw new Error(`Failed to create a session: ${err.message}`);
        }
    }

});
