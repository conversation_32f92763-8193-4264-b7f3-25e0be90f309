'use strict';

const { createRoute } = require('../../helpers/route_factory');
const { types: typesNotification } = require('../../services/NotificationService.js');

const { convertIsoToTimestamp, convertIsoToDate } = require('../../helpers/utils');

module.exports = createRoute({
    documentation: {
        resourceTag: 'stripe',
        description: 'Handle Stripe webhook events',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/stripe/webhook',
    auth: false, // Disable standard authentication
    options: {
        payload: {
            parse: false // This is critical - it prevents <PERSON><PERSON> from parsing the payload
        }
    },
    handler: async (request, h) => {

        const { apiService, shootService, userService, notificationService, packageService, mondayService, stripeService } = request.services();
        const whsec = request.server.settings.app.stripe.stripe_whsec;

        const sig = request.headers['stripe-signature'];
        const { payload } = request; // Raw payload as a Buffer

        try {
            const event = stripeService.constructEvent(payload, sig, whsec);
            // Handle the event type (e.g., 'checkout.session.completed')
            if (event.type === 'checkout.session.completed') {
                const session = event.data.object;
                const phone = session?.metadata?.phone || session?.customer_details?.phone;
                const email = session?.metadata?.email || session?.customer_details?.email;
                const name = session?.metadata?.name || session?.customer_details?.name;

                const paymentDetail = {
                    type: 'card',
                    details: { stripe_customer_id: session.customer }
                };
                const additionalInformations = {
                    company_name: `${session.metadata.name} B2C`,
                    industry: 'Other',
                    country: 'AE',
                    contact_name: name,
                    contact_email: email,
                    contact_phone: phone ? phone : 'N/A',
                    main_location: session?.metadata?.main_location || 'N/A'
                };
                const user = {
                    name,
                    email,
                    phone: phone ? phone : 'N/A'
                };

                let savedUser;

                const fetchedUser = await userService.getUserByEmail({ email: user.email });

                if (fetchedUser) {
                    savedUser = fetchedUser;
                }
                else {
                    let existedAuth0User;

                    try {
                        existedAuth0User = await userService.getAuth0UserByEmail({ email: user.email });
                    }
                    catch (error) {
                        request.logger.error('Error fetching Auth0 user:', error);
                        existedAuth0User = null;
                    }

                    const { savedUser: createdUser } = await userService.createClient({
                        user, paymentDetail,
                        additionalInformations,
                        syncAuth0: !existedAuth0User
                    });
                    savedUser = createdUser;
                }

                const {
                    formattedPackages,
                    totalFixedPrice,
                    totalFixedRevenue
                } = await packageService.getFormattedPackages({ packages: [{ package_id: session.metadata.package_id }] });

                const timestamp = convertIsoToTimestamp(session.metadata.from);
                const formatedDate = convertIsoToDate(session.metadata.from);

                const { savedShoot } = await shootService.createShoot({
                    packages: formattedPackages,
                    client_id: savedUser.client_id,
                    user: savedUser,
                    photographer_id: null,
                    location: {
                        city: session.metadata.city,
                        country: 'AE',
                        state: session.metadata.state,
                        formatted_address: session.metadata.formatted_address,
                        utc_offset_minutes: session.metadata.utc_offset_minutes
                    },
                    price: totalFixedPrice,
                    photographer_revenue: totalFixedRevenue,
                    time: {
                        duration: session.metadata.duration,
                        from: timestamp
                    },
                    duration: session.metadata.duration,
                    contact_phone: session.metadata.phone,
                    contact_name: session.metadata.name,
                    notes: null,
                    is_payed: true,
                    role: 'client',
                    name: formattedPackages[0].package.name,
                    type: 'express',
                    content: 'photography'
                });

                const { shoot } = await shootService.getShoot({ id: savedShoot.id, user: savedUser, role: 'client' });

                await mondayService.createShoot({ shoot_id: savedShoot.id, boardType: 'FLASHYSHOOTS' });

                const names = session.metadata.name.split(' ');

                const mondayClient = {
                    id: savedUser.client_id,
                    firstName: names[0],
                    lastName: names[1],
                    email: session.metadata.email,
                    phone: session.metadata.phone,
                    city: session.metadata.city,
                    status: '9. COMPLETE',
                    campaignName: session.metadata.utm_campaign,
                    campaignMedium: session.metadata.utm_medium,
                    campaignSource: session.metadata.utm_source,
                    orderCount: 1,
                    orderDate: formatedDate
                };
                await mondayService.createClient(mondayClient);

                await shootService.sendCreateNotification(shoot);

                if (!fetchedUser) {
                    // send email notification
                    await notificationService.sendNotification({
                        type: typesNotification.newCustomerRegistered,
                        target: {},
                        metaData: {
                            customerName: savedUser.name,
                            customerId: savedUser.client_id
                        }
                    });

                    // Generate a passwrd-reset link (ticket)
                    const response = await userService.generatePasswordResetLink({ email: savedUser.email });

                    // send welcome email to client
                    const type = typesNotification.accountCreated;
                    const metaData = {
                        ticket: response.ticket
                    };

                    const target = {
                        email: savedUser.email,
                        client_id: savedUser.client_id
                    };
                    await notificationService.sendNotification({ type, target, metaData });

                    // e.g., create user, book shoot, etc.
                }

                return apiService.formatResponse(h, 'Payment completed');
            }

            if (event.type === 'customer.subscription.deleted') {
                // This fires when a subscription is canceled or has ended
                const subscription = event.data.object;

                // Get the Billing record associated with this subscription
                const { Billing } = request.server.models();
                const billingRecord = await Billing.query()
                    .where('reference', subscription.id)
                    .where('status', 'active')
                    .first();

                if (billingRecord) {
                    // Update the billing status
                    await Billing.query()
                        .where('id', billingRecord.id)
                        .patch({
                            status: 'canceled',
                            updated_at: new Date()
                        });

                    // Optional: Notify the user that their subscription has ended
                    const user = await userService.getUser({ id: billingRecord.user_id });
                    if (user) {
                        const endDate = new Date(subscription.current_period_end * 1000);

                        await notificationService.sendNotification({
                            type: 'subscriptionCancel',
                            target: {
                                email: user.email,
                                client_id: user.client_id
                            },
                            metaData: {
                                user: {
                                    first_name: user.name
                                },
                                plan_name: billingRecord.name,
                                subscription: {
                                    end_date: endDate.toISOString()
                                },
                                host: process.env.CURRENT_HOST
                            }
                        });
                    }
                }

                return apiService.formatResponse(h, 'Subscription ended successfully');
            }

            if (event.type === 'invoice.upcoming') {
                const invoice = event.data.object;

                // Check if this invoice is for a subscription
                if (invoice.subscription) {
                    // Get the subscription details
                    const subscription = await stripeService.getSubscription(invoice.subscription);

                    // Get the user associated with this subscription
                    const { Billing } = request.server.models();
                    const billingRecord = await Billing.query()
                        .where('reference', invoice.subscription)
                        .where('status', 'active')
                        .first();

                    if (billingRecord) {
                        const user = await userService.getUser({ id: billingRecord.user_id });
                        const endDate = new Date(subscription.current_period_end * 1000);
                        const currentDate = new Date();
                        const threeDaysInMs = 3 * 24 * 60 * 60 * 1000;

                        // Check if subscription is ending soon
                        if ((endDate - currentDate) <= threeDaysInMs) {
                            await notificationService.sendNotification({
                                type: 'subscriptionExpiry',
                                target: {
                                    email: user.email,
                                    client_id: user.client_id
                                },
                                metaData: {
                                    plan_name: billingRecord.name,
                                    user: {
                                        first_name: user.name
                                    },
                                    subscription: {
                                        end_date: endDate.toISOString()
                                    },
                                    host: process.env.CURRENT_HOST
                                }
                            });
                        }
                    }
                }

                return apiService.formatResponse(h, 'Upcoming invoice notification processed');
            }

            if (event.type === 'invoice.payment_succeeded') {
                const invoice = event.data.object;

                // Check if the invoice is for a subscription renewal
                if (invoice.billing_reason === 'subscription_cycle' && invoice.subscription) {
                    const subscriptionId = invoice.subscription;
                    const { Billing } = request.server.models();

                    // Find the corresponding active billing record
                    const billingRecord = await Billing.query()
                        .where('reference', subscriptionId)
                        // Ensure we are updating the correct active record,
                        // especially if old inactive records exist with the same reference
                        .where('status', 'active')
                        .first();

                    if (billingRecord) {
                        // Fetch the latest subscription details from Stripe to get accurate period dates
                        const stripeSubscription = await stripeService.getSubscription(subscriptionId);

                        // Update the billing record with the new period dates
                        await Billing.query()
                            .where('id', billingRecord.id)
                            .patch({
                                status: 'inactive',
                                updated_at: new Date()
                            });

                        const periodStart = new Date(stripeSubscription.current_period_start * 1000);
                        const periodEnd = new Date(stripeSubscription.current_period_end * 1000);

                        await Billing.query().insert({
                            user_id: billingRecord.user_id,
                            package_id: billingRecord.package_id,
                            name: billingRecord.name,
                            address: billingRecord.address,
                            status: 'active',
                            phone: billingRecord.phone,
                            email: billingRecord.email,
                            reference: subscriptionId,
                            description: billingRecord.description,
                            amount: billingRecord.amount,
                            currency: billingRecord.currency,
                            interval: stripeSubscription.items.data[0].plan.interval,
                            interval_count: stripeSubscription.items.data[0].plan.interval_count,
                            period_start: periodStart,
                            period_end: periodEnd,
                            created_at: new Date(),
                            updated_at: new Date()
                        });

                        const user = await userService.getUser({ id: billingRecord.user_id });
                        if (user) {
                            const packagePlan = await billingRecord.package;
                            await notificationService.sendNotification({
                                type: 'subscriptionSuccess',
                                target: {
                                    email: user.email,
                                    client_id: user.client_id
                                },
                                metaData: {
                                    plan_name: billingRecord.name,
                                    plans: packagePlan.plans,
                                    user: {
                                        first_name: user.name
                                    },
                                    subscription: {
                                        start_date: periodStart.toISOString(),
                                        renewal_date: periodEnd.toISOString()
                                    },
                                    host: process.env.CURRENT_HOST
                                }
                            });
                        }
                    }
                }

                // Acknowledge receipt of the event to Stripe
                return apiService.formatResponse(h, 'Invoice payment succeeded processed');
            }

            if (event.type === 'invoice.payment_failed') {

                const invoice = event.data.object;

                // Check if the invoice is for a subscription renewal
                if (invoice.billing_reason === 'subscription_cycle' && invoice.subscription) {
                    const subscriptionId = invoice.subscription;
                    const { Billing } = request.server.models();

                    // Find the corresponding active billing record
                    const billingRecord = await Billing.query()
                        .where('reference', subscriptionId)
                        .where('status', 'active')
                        .first();

                    if (billingRecord) {
                        // Update the billing record status to inactive
                        await Billing.query()
                            .where('id', billingRecord.id)
                            .patch({
                                status: 'inactive',
                                updated_at: new Date()
                            });

                        const user = await userService.getUser({ id: billingRecord.user_id });

                        if (user) {

                            await notificationService.sendNotification({
                                type: 'subscriptionCancel',
                                target: {
                                    email: user.email,
                                    client_id: user.client_id
                                },
                                metaData: {
                                    user: {
                                        first_name: user.name
                                    },
                                    plan_name: billingRecord.name,
                                    subscription: {
                                        end_date: billingRecord.period_end.toISOString()
                                    },
                                    host: process.env.CURRENT_HOST
                                }
                            });
                        }
                    }
                }

                return apiService.formatResponse(h, 'Invoice payment failed processed');
            }

            return apiService.formatResponse(h, 'No events listener');
        }
        catch (err) {
            console.error(`Failed to construct event // ERROR : ${err}`);
            return apiService.formatResponse(h, 'error occur');
        }
    }
});
