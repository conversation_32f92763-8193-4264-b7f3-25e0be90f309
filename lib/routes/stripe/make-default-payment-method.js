'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'stripe',
        description: 'Make default Stripe payment method for a client',
        response: Joi.object({
            success: Joi.boolean().example(true)
        })
    },
    method: 'PATCH',
    path: '/stripe/payment-methods/default/{id}',
    auth: ['jwt'],
    validate: {
        params: Joi.object({
            id: Joi.number().required()
        })
    },
    handler: async (request, h) => {

        const { authService, apiService, paymentMethodService, stripeService } = request.services();
        const { user, tokenPayload: { role } } = request.auth.credentials;
        authService.checkRouteAuthorization({ role, route: 'payment-method-management' });
        const { id } = request.params;

        try {
            const paymentMethod = await paymentMethodService.makeDefaultPaymentMethod({ user, id });
            let providerReference = await paymentMethodService.getProviderGateway({ user });

            if (!providerReference) {
                providerReference = await paymentMethodService.connectPaymentGateway({ user });
            }

            await stripeService.setDefaultPaymentMethod({ customer: providerReference.reference, payment_method: paymentMethod.reference });
            return apiService.formatResponse(h, { success: true });
        }
        catch (err) {
            console.error('err', err);
            return apiService.formatResponse(h, { success: false });
        }
    }
});
