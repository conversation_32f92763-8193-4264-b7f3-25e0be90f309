'use strict';

const { createRoute } = require('../../helpers/route_factory');
const Joi = require('joi');
const PackagePlan = require('../../models/PackagePlan');

module.exports = createRoute({
    documentation: {
        resourceTag: 'stripe',
        description: 'Create a Stripe setup subscription plan',
        response: Joi.object({
            packagePlan: Joi.object(PackagePlan.JoiSchema)
        })
    },
    method: 'POST',
    path: '/stripe/create-subscription-plan',
    auth: ['jwt'],
    validate: {
        payload: Joi.object({
            name: Joi.string().required(),
            label: Joi.string().required(),
            description: Joi.string().required(),
            amount: Joi.number().optional().allow(null),
            discount: Joi.number().integer().optional().allow(null),
            origin_price: Joi.number().optional().allow(null),
            weight: Joi.number().integer().required(),
            plans: Joi.object(),
            currency: Joi.string().required(),
            interval: Joi.string().valid('day', 'week', 'month', 'year').required(),
            interval_count: Joi.number().integer().required()
        })
    },
    handler: async (request, h) => {

        const { apiService, subscriptionService } = request.services();
        const { name, description, amount, currency, interval, interval_count, label, weight, discount, origin_price, plans } = request.payload;

        try {
            const packagePlan = await subscriptionService.createSubscriptionPlan({ name, description, amount, currency,
                interval, interval_count, label, weight, discount, origin_price, plans });
            return apiService.formatResponse(h, { packagePlan });
        }
        catch (err) {
            throw new Error(`Failed to create subscription plan: ${err.message}`);
        }
    }
});
