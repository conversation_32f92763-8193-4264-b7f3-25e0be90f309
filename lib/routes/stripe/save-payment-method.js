'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const PaymentMethod = require('../../models/PaymentMethod');

module.exports = createRoute({
    documentation: {
        resourceTag: 'stripe',
        description: 'Save a Stripe payment method for a client',
        response: Joi.object({
            paymentMethod: Joi.object(PaymentMethod.JoiSchema)
        })
    },
    method: 'POST',
    path: '/stripe/payment-method',
    auth: ['jwt'],
    validate: {
        payload: Joi.object({
            payment_method_id: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const { authService, apiService, stripeService, paymentMethodService } = request.services();
        const { user, tokenPayload: { role } } = request.auth.credentials;
        authService.checkRouteAuthorization({ role, route: 'payment-method-management' });
        const { payment_method_id } = request.payload;

        try {
            const stripePaymentMethod = await stripeService.retrievePaymentMethod({ payment_method_id });
            const paymentMethod = await paymentMethodService.savePaymentMethodFromStripe({ user, paymentMethod: stripePaymentMethod });
            let providerReference = await paymentMethodService.getProviderGateway({ user });

            if (!providerReference) {
                providerReference = await paymentMethodService.connectPaymentGateway({ user });
            }

            await stripeService.setDefaultPaymentMethod({ customer: providerReference.reference, payment_method: payment_method_id });
            return apiService.formatResponse(h, { paymentMethod });
        }
        catch (err) {
            throw new Error(`Failed to setup payment method: ${err.message}`);
        }
    }
});
