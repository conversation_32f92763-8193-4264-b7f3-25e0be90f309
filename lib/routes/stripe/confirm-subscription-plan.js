'use strict';

const { createRoute } = require('../../helpers/route_factory');
const Joi = require('joi');

module.exports = createRoute({
    documentation: {
        resourceTag: 'stripe',
        description: 'Confirm a Stripe setup subscription plan',
        response: Joi.object({
            success: Joi.boolean().example(true)
        })
    },
    method: 'POST',
    path: '/stripe/subscription-plan/confirm',
    auth: ['jwt'],
    validate: {
        payload: Joi.object({
            payment_intent_id: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const { authService, apiService, subscriptionService } = request.services();
        const { user, tokenPayload: { role } } = request.auth.credentials;
        authService.checkRouteAuthorization({ role, route: 'subscription-management' });

        const { payment_intent_id } = request.payload;

        try {
            await subscriptionService.confirmSubscriptionPayment(user, payment_intent_id);
            return apiService.formatResponse(h, { success: true });
        }
        catch (err) {
            console.error('Cancel stripe subscription error', err);
            return apiService.formatResponse(h, { success: false });
        }
    }
});
