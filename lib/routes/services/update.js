'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { rethrow, ValidationError } = require('../../helpers/errors');
const { service }  = require('../../validation-schema/services/responses.js');
const { create } = require('../../validation-schema/services/payloads.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'services',
        description: 'updates a service',
        response: Joi.object({
            service
        })
    },
    method: 'POST',
    path: '/service/update/{id}',
    auth: 'jwt',
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        }),
        payload: create
    },
    handler: async (request, h) => {

        const { apiService, authService, serviceService, purposeService } = request.services();

        const {
            params: { id },
            payload: {
                name,
                purpose_id
            }
        } = request;

        const { tokenPayload: { role } } = request.auth.credentials;

        await purposeService.getPurpose({ id: purpose_id }).catch(rethrow(ValidationError, 'Service can not be updated due to non-existent purpose'));

        authService.checkRouteAuthorization({ role, route: 'update-service' });

        const { updatedService } = await serviceService.updateService({
            id,
            newValues: {
                name,
                purpose_id,
                last_update: new Date()
            }
        });

        return apiService.formatResponse(h, {
            service: updatedService
        });
    }
});
