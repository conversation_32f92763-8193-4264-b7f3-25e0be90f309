'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');
const { rethrow, ValidationError } = require('../../../helpers/errors');
const { service }  = require('../../../validation-schema/services/responses.js');
const { reassign } = require('../../../validation-schema/services/payloads.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'services',
        description: 'updates a service',
        response: Joi.object({
            service
        })
    },
    method: 'POST',
    path: '/service/actions/reassign',
    auth: 'jwt',
    validate: {
        payload: reassign
    },
    handler: async (request, h) => {

        const { apiService, authService, serviceService } = request.services();

        const {
            from_id,
            to_id
        } = request.payload;

        const { tokenPayload: { role } } = request.auth.credentials;

        await serviceService.getService({ id: from_id })
            .catch(rethrow(ValidationError, 'Packages can not be updated due to non-existent starting service'));

        const { service } = await serviceService.getService({ id: to_id })
            .catch(rethrow(ValidationError, 'Packages can not be updated due to non-existent target service'));

        authService.checkRouteAuthorization({ role, route: 'reassign-service' });

        await serviceService.reassignPackagesToService({
            from_id,
            to_id
        });

        return apiService.formatResponse(h, {
            service
        });
    }
});
