'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { rethrow, ValidationError } = require('../../helpers/errors');
const { service }  = require('../../validation-schema/services/responses.js');
const { create } = require('../../validation-schema/services/payloads.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'services',
        description: 'creates a service',
        response: Joi.object({
            service
        })
    },
    method: 'POST',
    path: '/service',
    auth: 'jwt',
    validate: {
        payload: create
    },
    handler: async (request, h) => {

        const { apiService, authService, serviceService, purposeService } = request.services();

        const {
            name,
            purpose_id
        } = request.payload;

        const { tokenPayload: { role } } = request.auth.credentials;

        await purposeService.getPurpose({ id: purpose_id }).catch(rethrow(ValidationError, 'Service can not be created due to non-existent purpose'));

        authService.checkRouteAuthorization({ role, route: 'create-service' });

        const { savedService } = await serviceService.createService({
            name,
            purpose_id
        });

        return apiService.formatResponse(h, {
            service: savedService
        });
    }
});
