'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { service }  = require('../../validation-schema/services/responses.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'services',
        description: 'returns service based on id',
        response: Joi.object({
            service
        })
    },
    method: 'GET',
    path: '/service/{id}',
    auth: 'jwt',
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const { apiService, serviceService } = request.services();

        const { id } = request.params;

        const { service } = await serviceService.getService({ id });

        return apiService.formatResponse(h, {
            service
        });
    }
});
