'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { service }  = require('../../validation-schema/services/responses.js');
const { search } = require('../../validation-schema/services/payloads.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'services',
        description: 'return services',
        response: Joi.object({
            services: Joi.array().items(service).required()
        })
    },
    method: 'POST',
    path: '/services/search',
    auth: 'optional',
    validate: {
        payload: search
    },
    handler: async (request, h) => {

        const { apiService, serviceService, shootService } = request.services();
        const { user, tokenPayload } = request.auth.credentials || {};
        const role = tokenPayload ? tokenPayload.role : null;

        const {
            filters: { purposes, names }
        } = request.payload;

        let services;
        let existingServicesNames;

        if (user && role) {
            try {
                existingServicesNames = await shootService.getListServicesOfUsers({ user, role });
            }
            catch (error) {
                existingServicesNames = null;
            }
        }

        if (names && names.length > 0) {
            // If names are provided, check if they are in the existing services
            const validNames = names.filter((name) => existingServicesNames && existingServicesNames.services.includes(name));
            // If names are provided, use getServicesByName
            const { services: fetchedServices } = await serviceService.getServicesByName({ names: validNames });
            services = fetchedServices;
        }
        else {
            // Otherwise, use the existing getServices
            const { services: fetchedServices } = await serviceService.getServices({ purposes, names: existingServicesNames?.services });
            services = fetchedServices;
        }

        return apiService.formatResponse(h, {
            services
        });
    }
});
