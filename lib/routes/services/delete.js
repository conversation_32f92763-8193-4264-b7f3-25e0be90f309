'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { service }  = require('../../validation-schema/services/responses.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'services',
        description: 'deletes service based on id',
        response: Joi.object({
            service
        })
    },
    method: 'DELETE',
    path: '/service/delete/{id}',
    auth: 'jwt',
    validate: {
        params: Joi.object({
            id: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const { apiService, serviceService, authService } = request.services();

        const { id } = request.params;

        const { tokenPayload: { role } } = request.auth.credentials;

        authService.checkRouteAuthorization({ role, route: 'delete-service' });

        const { deletedService } = await serviceService.deleteService({ id });

        return apiService.formatResponse(h, {
            service: deletedService
        });
    }
});
