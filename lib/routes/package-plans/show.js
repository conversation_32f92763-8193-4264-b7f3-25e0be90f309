'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const PackagePlan = require('../../models/PackagePlan');

module.exports = createRoute({
    documentation: {
        resourceTag: 'package plans',
        description: 'returns packages list',
        response: Joi.object({
            packagePlans: Joi.array().items(
                Joi.object(PackagePlan.JoiSchema)
            )
        })
    },
    method: 'GET',
    path: '/package-plans/list',
    auth: 'jwt',
    validate: {},
    handler: async (request, h) => {

        const { apiService, packagePlanService } = request.services();
        const { user } = request.auth.credentials;

        const packagePlans = await packagePlanService.getPackagePlans({ user });

        return apiService.formatResponse(h, {
            packagePlans
        });
    }
});
