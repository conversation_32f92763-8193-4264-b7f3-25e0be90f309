'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'package plans',
        description: 'Request a custom package plan',
        response: Joi.object({
            success: Joi.boolean().required()
        })
    },
    method: 'GET',
    path: '/package-plans/custom',
    auth: 'jwt',
    validate: {},
    handler: async (request, h) => {

        const { apiService, packagePlanService } = request.services();
        const { user } = request.auth.credentials;
        await packagePlanService.requestCustomPackage({ user });

        return apiService.formatResponse(h, {
            success: true
        });
    }
});
