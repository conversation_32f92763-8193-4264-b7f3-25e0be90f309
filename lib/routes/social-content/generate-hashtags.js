'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { ValidationError } = require('../../helpers/errors');

module.exports = createRoute({
    documentation: {
        resourceTag: 'social-content',
        description: 'Generate relevant hashtags for social media post',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/social-content/generate-hashtags',
    auth: ['jwt'],
    validate: {
        payload: Joi.object({
            caption: Joi.string().required()
                .description('Post caption to generate hashtags for')
        })
    },
    handler: async (request, h) => {

        const { apiService, contentService } = request.services();
        const { caption  } = request.payload;

        try {
            const hashtags = await contentService.generateHashtags(
                caption
            );

            return apiService.formatResponse(h, {
                hashtags
            });
        }
        catch (error) {
            request.log('error', {
                message: 'Hashtag generation failed',
                error
            });

            throw error instanceof ValidationError
                ? error
                : new ValidationError('Failed to generate hashtags');
        }
    }
});
