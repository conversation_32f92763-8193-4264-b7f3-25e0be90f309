'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { ValidationError } = require('../../helpers/errors');

module.exports = createRoute({
    documentation: {
        resourceTag: 'social-content',
        description: 'Improve social media post caption using AI',
        disableResponseValidation: true
    },
    method: 'POST',
    path: '/social-content/improve-caption',
    auth: ['jwt'],
    validate: {
        payload: Joi.object({
            caption: Joi.string().required()
                .description('Original caption to improve')
        })
    },
    handler: async (request, h) => {

        const { apiService, contentService } = request.services();
        const { caption } = request.payload;

        try {
            const improvedCaption = await contentService.improveCaption(
                caption
            );

            return apiService.formatResponse(h, {
                improvedCaption
            });
        }
        catch (error) {
            request.log('error', {
                message: 'Caption improvement failed',
                error
            });

            throw error instanceof ValidationError
                ? error
                : new ValidationError('Failed to improve caption');
        }
    }
});
