'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { update } = require('../../validation-schema/notification-settings/payloads');

module.exports = createRoute({
    documentation: {
        resourceTag: 'notification-settings',
        description: 'update setting for notification by id',
        response: {}
    },
    method: 'POST',
    path: '/notificationsettings/{id}/update',
    auth: 'jwt',
    validate: {
        payload: update,
        params: Joi.object({
            id: Joi.string().required()
        })
    },
    handler: async (request, h) => {

        const { id } =  request.params;
        const { status } = request.payload;
        const { apiService, notificationSettingsService, authService } = request.services();

        const { tokenPayload: { role } } = request.auth.credentials;
        authService.checkRouteAuthorization({ role, route: 'update-notification-settings' });

        await notificationSettingsService.setSettingStatusById({ id, status });

        return apiService.formatResponse(h, { });
    }
});
