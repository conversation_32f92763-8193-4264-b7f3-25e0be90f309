'use strict';

const { createRoute } = require('../../helpers/route_factory');
const { settings } = require('../../../lib/validation-schema/notification-settings/responses');

module.exports = createRoute({
    documentation: {
        resourceTag: 'notification-settings',
        description: 'returns all settings for notification',
        response: settings
    },
    method: 'GET',
    path: '/notificationsettings',
    auth: 'jwt',
    validate: {
    },
    handler: async (request, h) => {

        const { apiService, notificationSettingsService, authService } = request.services();

        const { tokenPayload: { role } } = request.auth.credentials;
        authService.checkRouteAuthorization({ role, route: 'show-notification-settings' });
        const settings = await notificationSettingsService.getAllSettingsGrouppedForUserType();

        return apiService.formatResponse(h, settings);
    }
});
