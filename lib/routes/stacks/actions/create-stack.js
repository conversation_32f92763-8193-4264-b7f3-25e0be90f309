'use strict';

const Joi = require('joi');
const { createRoute } = require('../../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'stacks',
        description: 'Create a new stack.',
        response: Joi.object({
            stack: Joi.object({
                id: Joi.number().integer(),
                description: Joi.string().max(1024).optional(),
                user_id: Joi.number().integer().required(),
                created_at: Joi.date(),
                updated_at: Joi.date()
            })
        })
    },
    method: 'POST',
    path: '/stacks',
    auth: ['jwt', 'api-key'],
    validate: {
        payload: Joi.object({
            description: Joi.string().max(1024).optional().description('Description of the stack.'),
            primary_image_id: Joi.number().integer().optional().description('ID of the primary image in the stack.').allow(null)
        })
    },
    handler: async (request, h) => {

        const { contentService, apiService } = request.services();
        const { description, primary_image_id } = request.payload;
        const { user } = request.auth.credentials;

        const stack = await contentService.createStack({ description, userId: user.id, primary_image_id });

        if (!stack) {
            return apiService.formatResponse(h, null, 500, ['Error']);
        }

        return apiService.formatResponse(h, { stack });
    }
});
