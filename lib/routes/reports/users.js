'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { userReport }  = require('../../validation-schema/reports/responses.js');
const { searchUsers } = require('../../validation-schema/reports/payloads.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'reports',
        description: 'return users report',
        response: Joi.object({
            reports: Joi.array().items(userReport).required()
        })
    },
    method: 'POST',
    path: '/reports/users',
    auth: 'jwt',
    validate: {
        payload: searchUsers
    },
    handler: async (request, h) => {

        const { apiService, authService, reportUserService } = request.services();

        const {
            filters: {
                time,
                userType,
                name,
                userId: user_id,
                photographerId: photographer_id,
                clientId: client_id
            },
            pagination: { limit, offset } = {},
            sorting: { field: sortField, direction: sortDirection } = {}
        } = request.payload;

        const { tokenPayload: { role } } = request.auth.credentials;

        authService.checkRouteAuthorization({ role, route: 'reports-users' });
        const reports  = await reportUserService.getReportUserInTimeRange({
            time,
            userType,
            limit,
            offset,
            sortBy: sortField,
            sortDescending: sortDirection === 'desc',
            filters: { name, user_id, photographer_id, client_id }
        });

        return apiService.formatResponse(h, {
            reports
        });
    }
});
