'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');
const { dailyReport }  = require('../../validation-schema/reports/responses.js');
const { search } = require('../../validation-schema/reports/payloads.js');

module.exports = createRoute({
    documentation: {
        resourceTag: 'reports',
        description: 'return shoots report',
        response: Joi.object({
            reports: Joi.array().items(dailyReport) })
    },
    method: 'POST',
    path: '/reports/shoots',
    auth: 'jwt',
    validate: {
        payload: search
    },
    handler: async (request, h) => {

        const { apiService, authService, reportShootService } = request.services();

        const {
            filters: {
                time
            }
        } = request.payload;

        const { tokenPayload: { role } } = request.auth.credentials;

        authService.checkRouteAuthorization({ role, route: 'reports-shoots' });

        const reports = await reportShootService.getStatForTimeRange({ time });

        return apiService.formatResponse(h,
            { reports }
        );
    }
});
