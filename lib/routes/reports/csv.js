'use strict';

const { createRoute } = require('../../helpers/route_factory');
const { Parser, transforms: { unwind } } = require('json2csv');
const Joi = require('joi');

module.exports = createRoute({
    documentation: {
        resourceTag: 'reports',
        description: 'return csv file with report',
        disableResponseValidation: true
    },
    method: 'GET',
    path: '/reports/csv',
    auth: 'jwt',
    validate: {
        query: Joi.object({
            from: Joi.date().required(),
            to: Joi.date().min(Joi.ref('from')).required(),
            type: Joi.string().valid('shoot', 'client', 'photographer').default('shoot')
        })
    },
    handler: async (request, h) => {

        const { authService, reportShootService, reportUserService } = request.services();

        const { from, to, type } = request.query;

        const { tokenPayload: { role } } = request.auth.credentials;

        authService.checkRouteAuthorization({ role, route: 'reports-shoots' });

        let formattedReports;

        //get data
        if ( type === 'shoot' ) {
            let reports = await reportShootService.getStatForTimeRange({ time: { from, to } });
            reports = reports.map((object) => (Object.entries(object).map(([key, value]) => ({ key, ...value }) )) );
            reports = reports.flat();
            formattedReports = reports;
        }
        else {
            const reports = await reportUserService.getReportUserInTimeRange({ time: { from, to }, userType: type, limit: 'ALL' });
            formattedReports = reports;
        }

        //create csv
        const fields = Array.from(new Set(formattedReports.flatMap(
            (datum) => Object.entries(datum).flatMap(([k, v]) => ((v instanceof Object) ? Object.keys(v).map((el) => `${k}.${el}`) : k)))));
        const transforms = [unwind({ paths: fields })];
        const opts = { fields, transforms };
        const parser = new Parser(opts);
        const csvFile = parser.parse(formattedReports);

        return h.response(csvFile)
            .header('Content-Type', 'application/octet-stream')
            .header('Content-Disposition', `attachment; filename=${type}.csv`);
    }
});
