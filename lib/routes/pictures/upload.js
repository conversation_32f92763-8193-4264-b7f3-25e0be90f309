'use strict';

const Joi = require('joi');
const { createRoute } = require('../../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        resourceTag: 'pictures',
        description: 'upload a set of pictures (in zipfile) for a package/service',
        response: Joi.object({
            numUploadedPictures: Joi.number().integer().min(0).required()
        })
    },
    method: 'POST',
    path: '/action/upload-pictures',
    auth: ['jwt'],
    options: {
        payload: {
            output: 'file',
            parse: false,
            allow: 'application/octet-stream',
            maxBytes: process.env.MAX_UPLOAD_PACKAGE_PICTURES_SIZE_MB * 1024 * 1024
        }
    },
    validate: {
        query: Joi.object({
            type: Joi.string().valid('package', 'service').required(),
            id: Joi.number().integer().min(1).required()
        })
    },
    handler: async (request, h) => {

        const { apiService, picturesService, authService } = request.services();
        const { query: { type: resourceType, id: resourceId }, payload: zipFile } = request;
        const { tokenPayload: { role } } = request.auth.credentials;

        authService.checkRouteAuthorization({ role, route: 'upload-pictures' });

        const { num_pictures: numUploadedPictures } = await picturesService.uploadPictures({ zipFile, resourceType, resourceId });

        return apiService.formatResponse(h, { numUploadedPictures });
    }
});
