'use strict';

exports.up = function (knex) {

    return knex.schema
        .createTable('conversations', (table) => {

            table.increments('id').primary();
            table.integer('user_id').notNullable();
            table.jsonb('metadata');
            table.timestamp('created_at').defaultTo(knex.fn.now());
            table.timestamp('updated_at').defaultTo(knex.fn.now());

            // Add foreign key to users table
            table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');

            table.index('user_id');
        })
        .createTable('messages', (table) => {

            table.increments('id').primary();
            table.integer('conversation_id').notNullable();
            table.text('content').notNullable();
            table.string('sender', 255).notNullable();
            table.timestamp('created_at').defaultTo(knex.fn.now());

            table.foreign('conversation_id').references('id').inTable('conversations').onDelete('CASCADE');

            table.index('conversation_id');
        });
};

exports.down = function (knex) {

    return knex.schema
        .dropTableIfExists('messages')
        .dropTableIfExists('conversations');
};
