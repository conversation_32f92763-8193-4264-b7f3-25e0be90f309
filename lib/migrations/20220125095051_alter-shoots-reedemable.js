'use strict';

exports.up = async (knex) => {

    await knex.schema.alterTable('shoots', (table) => {

        table.integer('redeemable_counter').defaultTo(null);
        table.integer('redeemable_total').defaultTo(null);
    });
};

exports.down = async (knex) => {

    await knex.schema.alterTable('shoots', (table) => {

        table.dropColumn('redeemable_counter');
        table.dropColumn('redeemable_total');

    });
};
