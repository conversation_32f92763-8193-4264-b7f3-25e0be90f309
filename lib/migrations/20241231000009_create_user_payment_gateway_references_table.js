'use strict';

exports.up = (knex) => {

    return knex.schema.createTable('user_payment_gateways', (table) => {

        table.increments('id').primary(); // PK
        table
            .integer('user_id')
            .unsigned()
            .notNullable()
            .references('id')
            .inTable('users')
            .onDelete('CASCADE'); // FK to users
        table.string('reference').notNullable(); // Stripe subscription ID
        table
            .enum('status', ['active', 'inactive'])
            .notNullable()
            .defaultTo('active');
        table.string('provider').notNullable().defaultTo('stripe');
        table.timestamp('created_at').defaultTo(knex.fn.now());
        table.timestamp('updated_at').defaultTo(knex.fn.now());
    });
};

exports.down = (knex) => {

    return knex.schema.dropTable('user_payment_gateways');
};
