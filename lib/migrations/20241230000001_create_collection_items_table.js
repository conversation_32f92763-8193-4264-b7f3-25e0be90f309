'use strict';

exports.up = function (knex) {

    return knex.schema.createTable('collection_items', (table) => {

        table.increments('id').primary();
        table.integer('collection_id').unsigned().notNullable();
        table.integer('image_id').unsigned().notNullable();
        table.timestamp('added_at').defaultTo(knex.fn.now());

        // Add foreign key constraints with cascade delete
        table.foreign('collection_id').references('id').inTable('collections').onDelete('CASCADE');
        table.foreign('image_id').references('id').inTable('images').onDelete('CASCADE');

        // Add unique constraint to prevent duplicate entries
        table.unique(['collection_id', 'image_id']);
    });
};

exports.down = function (knex) {

    return knex.schema.dropTable('collection_items');
};
