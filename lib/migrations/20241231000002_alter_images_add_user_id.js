'use strict';

exports.up = async (knex) => {

    await knex.schema.alterTable('images', (table) => {

        table.integer('user_id').unsigned().nullable().references('id').inTable('users');
        table.integer('shoot_id').unsigned().nullable().alter();
    });
};

exports.down = async (knex) => {

    await knex.schema.alterTable('images', (table) => {

        table.dropForeign(['user_id']);
        table.dropColumn('user_id');
        table.integer('shoot_id').unsigned().notNullable().alter();
    });
};
