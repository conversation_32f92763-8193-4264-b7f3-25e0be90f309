'use strict';

exports.up = async (knex) => {

    await knex.schema.dropTableIfExists('reports').createTable('reports', (table) => {

        table.increments('id').primary();
        table.timestamp('date', { precision: 6 }).defaultTo(knex.fn.now(6)).unique();
    });

    await knex.schema.dropTableIfExists('reports_shoots').createTable('reports_shoots', (table) => {

        table.increments('id').primary();
        table.integer('report_id').notNullable().references('reports.id').onDelete('CASCADE');
        table.string('key').notNullable();
        table.integer('value').notNullable();
    });

};

exports.down = async (knex) => {

    await knex.schema.dropTable('reports_shoots');
    await knex.schema.dropTable('reports');
};
