'use strict';

exports.up = async (knex) => {

    await knex.schema.dropTableIfExists('notification_queue').createTable('notification_queue', (table) => {

        table.increments('id').primary();
        table.integer('shoot_id').references('shoots.id').onDelete('CASCADE');
        table.string('type').notNullable();
        table.json('metaData').notNullable();
        table.json('target').notNullable();
        table.timestamp('dispatch_time').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    });
};

exports.down = async (knex) => {

    await knex.schema.dropTable('notification_queue');
};
