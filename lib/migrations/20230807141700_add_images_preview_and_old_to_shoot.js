'use strict';

exports.up = function (knex) {

    return knex.schema.alterTable('shoots', (table) => {

        table.string('images_preview').defaultTo(false);
        table.boolean('old').notNullable().defaultTo(false);
    })
        .then(() => {

            return knex('shoots').update('old', true);
        });
};

exports.down = function (knex) {

    return knex.schema.alterTable('shoots', (table) => {

        table.dropColumn('images_preview');
        table.dropColumn('old');
    });
};
