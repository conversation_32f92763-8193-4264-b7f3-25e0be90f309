'use strict';

exports.up = async (knex) => {

    await knex.schema.alterTable('clients', (table) => {

        table.string('main_location');

    });
    await knex('clients')
        .whereNull('main_location')
        .update({ main_location: 'default_location' });

    await knex.schema.alterTable('clients', (table) => {

        table.string('main_location').alter().notNullable();

    });
};

exports.down = async (knex) => {

    await knex.schema.alterTable('clients', (table) => {

        table.dropColumn('main_location');

    });
};
