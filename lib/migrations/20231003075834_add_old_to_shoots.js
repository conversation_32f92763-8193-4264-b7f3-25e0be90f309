'use strict';

exports.up = function (knex) {

    return knex.schema.table('shoots', (table) => {
        // Add the 'old' column with default value as false
        table.boolean('old').defaultTo(false).notNullable();
    })
        .then(() => {
            // Update existing rows to set 'old' as true
            return knex('shoots').update({ old: true });
        });
};

exports.down = function (knex) {

    return knex.schema.table('shoots', (table) => {
        // Drop the 'old' column
        table.dropColumn('old');
    });
};
