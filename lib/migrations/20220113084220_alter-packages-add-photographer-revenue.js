'use strict';

exports.up = async (knex) => {

    await knex.schema.alterTable('packages', (table) => {

        table.decimal('photographer_revenue', 8, 2);

    });

    await knex('packages')
        .whereNull('photographer_revenue')
        .update({ photographer_revenue: 0 });

    await knex.schema.alterTable('packages', (table) => {

        table.decimal('photographer_revenue', 8, 2).alter().notNullable();

    });
};

exports.down = async (knex) => {

    await knex.schema.alterTable('packages', (table) => {

        table.dropColumn('photographer_revenue');

    });
};
