'use strict';

exports.up = function (knex) {

    return knex.schema.createTable('meta_integrations', (table) => {
        // Primary Key
        table.increments('id').primary();

        // Foreign Key: user_id
        table.integer('user_id').notNullable().unsigned()
            .references('id').inTable('users')
            .onDelete('CASCADE');

        // Business Information
        table.string('fb_business_id', 20).nullable();
        table.string('business_name', 255).nullable();

        // Access Token
        table.string('access_token', 1024).nullable();
        table.timestamp('token_expires_at').nullable();

        // JSONB Arrays
        table.jsonb('facebook_pages').defaultTo('[]');
        table.jsonb('instagram_accounts').defaultTo('[]');

        // Metadata
        table.timestamp('created_at').defaultTo(knex.fn.now());
        table.timestamp('updated_at').defaultTo(knex.fn.now());
    });
};

exports.down = function (knex) {

    return knex.schema.dropTableIfExists('meta_integrations');
};
