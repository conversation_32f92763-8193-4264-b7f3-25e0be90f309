'use strict';

exports.up = async function (knex) {

    const hasPriceTagColumn = await knex.schema.hasColumn('packages', 'price_tag');
    const hasStripeProductIdColumn = await knex.schema.hasColumn('packages', 'stripe_product_id');
    const hasPackageTypeColumn = await knex.schema.hasColumn('packages', 'package_type');

    return knex.schema.table('packages', (table) => {

        if (!hasPriceTagColumn) {
            table.string('price_tag').defaultTo(null);
        }

        if (!hasStripeProductIdColumn) {
            table.string('stripe_product_id').defaultTo(null);
        }

        if (!hasPackageTypeColumn) {
            table.string('package_type').defaultTo('b2b');
        }
    });
};

exports.down = async function (knex) {

    const hasPriceTagColumn = await knex.schema.hasColumn('packages', 'price_tag');
    const hasStripeProductIdColumn = await knex.schema.hasColumn('packages', 'stripe_product_id');
    const hasPackageTypeColumn = await knex.schema.hasColumn('packages', 'package_type');

    return knex.schema.table('packages', (table) => {

        if (hasPriceTagColumn) {
            table.dropColumn('price_tag');
        }

        if (hasStripeProductIdColumn) {
            table.dropColumn('stripe_product_id');
        }

        if (hasPackageTypeColumn) {
            table.dropColumn('package_type');
        }
    });
};
