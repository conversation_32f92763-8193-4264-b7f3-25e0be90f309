'use strict';

exports.up = async (knex) => {

    await knex.schema.alterTable('shoots_services', (table) => {

        table.dropPrimary();

        table.dropForeign('service_id');
    });

    await knex.schema.alterTable('shoots_services', (table) => {

        table.increments('id').primary();
    });

    await knex.schema.alterTable('shoots_services', (table) => {

        table.integer('service_id').nullable().alter();

        table
            .foreign('service_id')
            .references('services.id')
            .onDelete('SET NULL');

    });
};

exports.down = async (knex) => {

    await knex.schema.alterTable('shoots_services', (table) => {

        throw new Error('Rollback unsupported');
    });
};
