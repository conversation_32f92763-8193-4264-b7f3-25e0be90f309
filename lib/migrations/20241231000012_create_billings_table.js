'use strict';

exports.up = (knex) => {

    return knex.schema.createTable('billing', (table) => {

        table.increments('id').primary(); // PK
        table
            .integer('user_id')
            .unsigned()
            .notNullable()
            .references('id')
            .inTable('users')
            .onDelete('CASCADE'); // FK to users
        table
            .integer('package_id')
            .unsigned()
            .notNullable()
            .references('id')
            .inTable('package_plans')
            .onDelete('CASCADE'); // FK to package_plans
        table.string('name').notNullable();
        table.string('status').notNullable().defaultTo('active'); // e.g., 'active', 'inactive'
        table.string('address').notNullable();
        table.string('email');
        table.string('phone');
        table.text('description');
        table.decimal('amount').notNullable();
        table.string('currency').notNullable();
        table.string('interval');
        table.string('interval_count');
        table.string('reference').notNullable(); // Stripe subscription ID
        table.timestamp('period_start');
        table.timestamp('period_end');
        table.timestamp('canceled_at');
        table.timestamp('created_at').defaultTo(knex.fn.now());
        table.timestamp('updated_at').defaultTo(knex.fn.now());
    });
};

exports.down = (knex) => {

    return knex.schema.dropTable('billing');
};
