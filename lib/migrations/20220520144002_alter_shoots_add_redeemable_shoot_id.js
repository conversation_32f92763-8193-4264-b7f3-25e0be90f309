'use strict';

const shootsTableName = 'shoots';
const ordersTableName = 'orders';

exports.up = async (knex) => {

    await knex.schema.alterTable(shootsTableName, (table) => {

        table.integer('redeemable_shoot_id').defaultTo(null);
    });

    let orders = await knex(ordersTableName);
    orders = orders.map((x) => x.id);

    for (const order of orders) {
        const shoots = await knex(shootsTableName).where('order_id', order).orderBy('id', 'asc');
        if (shoots.length > 1) {
            const [redeemable, ...redeemed] = shoots;
            await knex(shootsTableName)
                .whereIn('id', redeemed.map(({ id }) => id))
                .update({
                    redeemable_shoot_id: redeemable.id,
                    is_brief_uploaded: redeemable.is_brief_uploaded
                });
        }
    }
};

exports.down = async (knex) => {

    await knex.schema.alterTable(shootsTableName, (table) => {

        table.dropColumn('redeemable_shoot_id');

    });
};
