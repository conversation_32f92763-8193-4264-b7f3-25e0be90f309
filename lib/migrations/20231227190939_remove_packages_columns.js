'use strict';

exports.up = function (knex) {

    return knex.schema.table('packages', async (table) => {
    // Use a raw SQL query to check if the 'price_tag' column exists in the database
        const hasPriceTagColumn = await knex.schema.hasColumn('packages', 'price_tag');

        if (hasPriceTagColumn) {
            table.dropColumn('price_tag');
        }

        // Repeat the same process for other columns
        const hasStripeProductIdColumn = await knex.schema.hasColumn('packages', 'stripe_product_id');
        if (hasStripeProductIdColumn) {
            table.dropColumn('stripe_product_id');
        }

        const hasPackageTypeColumn = await knex.schema.hasColumn('packages', 'package_type');
        if (hasPackageTypeColumn) {
            table.dropColumn('package_type');
        }
    });
};

exports.down = function (knex) {

    return knex.schema.table('packages', async (table) => {
    // Use raw SQL queries to check if columns exist before adding them back
        const hasPriceTagColumn = await knex.schema.hasColumn('packages', 'price_tag');
        if (!hasPriceTagColumn) {
            table.string('price_tag').defaultTo('');
        }

        const hasStripeProductIdColumn = await knex.schema.hasColumn('packages', 'stripe_product_id');
        if (!hasStripeProductIdColumn) {
            table.string('stripe_product_id');
        }

        const hasPackageTypeColumn = await knex.schema.hasColumn('packages', 'package_type');
        if (!hasPackageTypeColumn) {
            table.string('package_type');
        }
    });
};
