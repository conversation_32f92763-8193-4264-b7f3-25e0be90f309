'use strict';

exports.up = (knex) => {

    return knex.schema.createTable('image_versions', (table) => {

        // Primary key
        table.increments('id').primary();

        table.string('version_name').notNullable();
        table.integer('stack_id').unsigned().references('id').inTable('stacks').onDelete('CASCADE');
        table.integer('origin_image_id').unsigned().references('id').inTable('images').onDelete('CASCADE');
        table.integer('version_image_id').unsigned().references('id').inTable('images').onDelete('CASCADE');
        table.string('is_default').defaultTo(false);
        table.timestamp('created_at').defaultTo(knex.fn.now());
    });
};

exports.down = (knex) => {

    return knex.schema.dropTableIfExists('contents');
};
