'use strict';

exports.up = async (knex) => {

    await knex.schema.dropTableIfExists('photographers').createTable('photographers', (table) => {

        table.increments('id').primary();
        table.date('birthdate').notNullable();
        table.string('english_level', 100);
        table.string('arabic_level', 100);
        table.json('details').notNullable();
        table.integer('payment_details_id').references('payment_details.id').notNullable().onDelete('CASCADE');
        table.string('status', 10).notNullable().defaultTo('none');
        table.timestamp('creation_date').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
        table.timestamp('last_update').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));

    });
};

exports.down = async (knex) => {

    await knex.schema.dropTable('photographers');
};
