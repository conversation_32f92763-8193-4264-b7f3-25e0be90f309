'use strict';

exports.up = function (knex) {

    return knex.schema.table('images', (table) => {

        table.string('preview_url', 255).nullable();
        table.string('placeholder_url', 255).nullable();
        table.string('filename', 255).nullable();
    });
};

exports.down = function (knex) {

    return knex.schema.table('images', (table) => {

        table.dropColumn('preview_url');
        table.dropColumn('placeholder_url');
        table.dropColumn('filename');
    });
};
