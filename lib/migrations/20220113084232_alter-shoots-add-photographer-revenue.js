'use strict';

exports.up = async (knex) => {

    await knex.schema.alterTable('shoots', (table) => {

        table.decimal('photographer_revenue', 8, 2);

    });

    await knex('shoots')
        .whereNull('photographer_revenue')
        .update({ photographer_revenue: 0 });

    await knex.schema.alterTable('shoots', (table) => {

        table.decimal('photographer_revenue', 8, 2).alter().notNullable();

    });
};

exports.down = async (knex) => {

    await knex.schema.alterTable('shoots', (table) => {

        table.dropColumn('photographer_revenue');

    });
};
