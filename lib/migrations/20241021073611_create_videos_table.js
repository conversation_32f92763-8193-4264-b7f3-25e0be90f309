'use strict';

exports.up = (knex) => {

    return knex.schema.createTable('videos', (table) => {

        table.increments('id').primary();
        table.integer('shoot_id').notNullable().references('shoots.id').onDelete('CASCADE');
        table.string('url', 255).notNullable();
        table.string('filename', 255).notNullable();
        table.string('folder', 255).notNullable();
        table.string('filepath', 255).notNullable();
        table.string('status', 20).notNullable().defaultTo('pending');
        table.string('feedback').nullable();
        table.float('aspect_ratio').nullable();
        table.integer('duration').notNullable();
        table.string('codec', 255).nullable();
        table.integer('framerate').nullable();
        table.integer('bitrate').nullable();
    });
};

exports.down = (knex) => {

    return knex.schema.dropTable('videos');
};
