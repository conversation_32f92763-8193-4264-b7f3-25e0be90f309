'use strict';

exports.up = (knex) => {

    return knex.schema.createTable('user_access_histories', (table) => {

        table.increments('id').primary(); // PK
        table
            .integer('user_id')
            .unsigned()
            .notNullable()
            .references('id')
            .inTable('users')
            .onDelete('CASCADE'); // FK to users
        table.string('active_session_id').notNullable(); // Action performed
        table.timestamp('created_at').defaultTo(knex.fn.now());
        table.timestamp('updated_at').defaultTo(knex.fn.now());
    });
};

exports.down = (knex) => {

    return knex.schema.dropTable('user_access_histories');
};
