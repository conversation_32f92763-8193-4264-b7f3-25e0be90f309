'use strict';

const usersTableName = 'users';
const editorsTableName = 'editors';

exports.up = async (knex) => {

    await knex.schema.alterTable(usersTableName, (table) => {

        table.integer('editor_id').references(`${editorsTableName}.id`).unique().onDelete('RESTRICT');
    });

    await knex.schema.raw('ALTER TABLE users DROP CONSTRAINT users_check');

    await knex.schema.raw(
        `ALTER TABLE users ADD CHECK ((
            (photographer_id IS NULL AND client_id IS NULL)
            OR
            (photographer_id IS NULL AND editor_id IS NULL)
            OR
            (editor_id IS NULL AND client_id IS NULL)
        ))
    `);

};

exports.down = async (knex) => {

    await knex.schema.alterTable(usersTableName, (table) => {

        throw new Error('Rollback unsupported');
    });
};
