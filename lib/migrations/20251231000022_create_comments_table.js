'use strict';

exports.up = (knex) => {

    return knex.schema.createTable('comments', (table) => {

        table.increments('id').primary();
        table.text('content').notNullable();
        table.integer('user_id').unsigned().references('id').inTable('users').onDelete('CASCADE');
        table.integer('image_id').unsigned().references('id').inTable('images').onDelete('CASCADE');
        table.string('user_name', 30).notNullable();
        table.timestamp('created_at').defaultTo(knex.fn.now());
        table.timestamp('updated_at').defaultTo(knex.fn.now());
    });
};

exports.down = (knex) => {

    return knex.schema.dropTableIfExists('comments');
};
