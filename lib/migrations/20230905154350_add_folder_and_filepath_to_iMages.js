'use strict';

exports.up = async (knex) =>  {

    return await knex.schema.table('images', (table) => {

        table.string('folder');  // Adding a new column for folder
        table.string('filepath');  // Adding a new column for filepath
    });
};

exports.down = async (knex) => {

    return await knex.schema.table('images', (table) => {

        table.dropColumn('folder');  // Removing the folder column
        table.dropColumn('filepath');  // Removing the filepath column
    });
};
