'use strict';

exports.up = async function (knex) {

    await knex.schema.alterTable('download_request_jobs', (table) => {
        // Add progress column
        table.integer('progress').defaultTo(0);
    });
};

exports.down = async function (knex) {

    await knex.schema.alterTable('download_request_jobs', (table) => {
        // Revert back to not null if needed
        table.dropColumn('progress');
    });
};
