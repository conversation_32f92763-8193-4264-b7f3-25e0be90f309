'use strict';

exports.up = async (knex) => {

    await knex.schema.alterTable('orders', (table) => {

        table.decimal('price', 8, 2);
    });

    const ordersToBeUpdated = await knex('orders').whereNull('price');

    for (const { id, payment_details } of ordersToBeUpdated) {
        await knex('orders')
            .where('id', id)
            .update('price', payment_details?.price || 0)
            .update('payment_details', {});
    }

    await knex.schema.alterTable('orders', (table) => {

        table.string('price').alter().notNullable();
    });
};

exports.down = async (knex) => {

    await knex.schema.alterTable('orders', (table) => {

        table.dropColumn('price');
    });
};
