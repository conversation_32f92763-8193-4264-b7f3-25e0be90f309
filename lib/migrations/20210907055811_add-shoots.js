'use strict';

exports.up = async (knex) => {

    await knex.schema.dropTableIfExists('shoots').createTable('shoots', (table) => {

        table.increments('id').primary();
        table.integer('photographer_id').references('photographers.id').onDelete('SET NULL');
        table.integer('order_id').references('orders.id').notNullable().onDelete('CASCADE');
        table.timestamp('datetime');
        table.json('address');
        table.decimal('latitude');
        table.decimal('longitude');
        table.integer('package_id').references('packages.id').onDelete('CASCADE');
        table.json('package').notNullable();
        table.text('notes');
        table.string('contact_phone', 255);
        table.string('contact_name', 255);
        table.integer('picture_number').notNullable();
        table.decimal('price', 8, 2).notNullable();
        table.integer('duration').notNullable();
        table.boolean('scheduled').notNullable().defaultTo(true);
        table.string('status', 10).notNullable().defaultTo('none');
        table.timestamp('creation_date').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
        table.timestamp('last_update').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));

    });
};

exports.down = async (knex) => {

    await knex.schema.dropTable('shoots');
};
