'use strict';

exports.up = async function (knex) {

    await knex.schema.alterTable('shoots', (table) => {
        // Add progress column
        // values can be 'pending', 'processing', 'completed', 'failed', 'needs_review'
        table.string('migration_status', 20).nullable().defaultTo('pending');
    });
};

exports.down = async function (knex) {

    await knex.schema.alterTable('shoots', (table) => {
        // Revert back to not null if needed
        table.dropColumn('migration_status');
    });
};
