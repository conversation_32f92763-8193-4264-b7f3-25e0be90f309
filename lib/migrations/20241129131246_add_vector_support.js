'use strict';

exports.up = async function (knex) {

    await knex.raw('CREATE EXTENSION IF NOT EXISTS vector');
    return knex.schema.alterTable('images', (table) => {

        table.specificType('embedding', 'vector(3072)');
    });
};

exports.down = async function (knex) {

    await knex.schema.alterTable('images', (table) => {

        table.dropColumn('embedding');
    });
    return knex.raw('DROP EXTENSION IF EXISTS vector');
};
