'use strict';

exports.up = async (knex) => {

    await knex.schema.dropTableIfExists('notification_settings').createTable('notification_settings', (table) => {

        table.increments('id').primary();
        table.integer('parent_id').references('notification_settings.id').onDelete('CASCADE');
        table.string('key').notNullable();
        table.string('description').notNullable();
        table.json('value').notNullable();
        table.string('user_type').notNullable();
    });
};

exports.down = async (knex) => {

    await knex.schema.dropTable('notification_settings');
};
