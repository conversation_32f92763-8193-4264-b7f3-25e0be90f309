'use strict';

exports.up = async (knex) => {

    await knex.schema.alterTable('orders', (table) => {

        table.dropColumn('billing_address');
    });

    await knex.schema.alterTable('orders', (table) => {

        table.json('billing_address');
    });

    await knex('orders')
        .whereNull('billing_address')
        .update({ billing_address: {
            city: 'city',
            country: 'country',
            line1: 'line1',
            line2: 'line2',
            postal_code: 'postal_code',
            state: 'state'
        } });

    await knex.schema.alterTable('orders', (table) => {

        table.json('billing_address').alter().notNullable();
    });
};

exports.down = async (knex) => {

    await knex.schema.alterTable('orders', (table) => {

        throw new Error('Rollback unsupported');
    });
};
