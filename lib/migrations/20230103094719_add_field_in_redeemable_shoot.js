'use strict';

const shootsTableName = 'shoots';

exports.up = async (knex) => {

    await knex.schema.alterTable(shootsTableName, (table) => {

        table.string('poc_email').defaultTo(null);
        table.string('poc_phone').defaultTo(null);
        table.string('poc_name').defaultTo(null);
    });

};

exports.down = async (knex) => {

    await knex.schema.alterTable(shootsTableName, (table) => {

        table.dropColumn('poc_email');
        table.dropColumn('poc_phone');
        table.dropColumn('poc_name');
    });
};
