'use strict';

exports.up = function (knex) {

    return knex.schema.table('shoots', (table) => {
        // Adding a new boolean column 'raws_preview' with default value as false
        table.boolean('raws_preview').defaultTo(false);
    });
};

exports.down = function (knex) {

    return knex.schema.table('shoots', (table) => {
        // Removing the 'raws_preview' column in the rollback
        table.dropColumn('raws_preview');
    });
};
