'use strict';

exports.up = function (knex) {

    return knex.schema.table('packages', async (table) => {
        // Alter the package_type column to set the new default value
        const hasPackageTypeColumn = await knex.schema.hasColumn('packages', 'package_type');
        if (hasPackageTypeColumn) {
            table.string('package_type').defaultTo('b2b').alter();
        }
    });
};

exports.down = function (knex) {

    return knex.schema.table('packages', async (table) => {

        const hasPackageTypeColumn = await knex.schema.hasColumn('packages', 'package_type');
        // Reset the default value of the package_type column
        // Assuming the original default was NULL or there was no default
        if (!hasPackageTypeColumn) {
            table.string('package_type').defaultTo(null).alter();
        }
    });
};
