'use strict';

exports.up = async function (knex) {

    await knex.schema.alterTable('videos', (table) => {
        // Add progress column
        // values can be 'pending', 'processing', 'completed', 'failed', 'needs_review'
        table.string('preview_url', 512).nullable();
        table.string('placeholder_url', 512).nullable();
    });
};

exports.down = async function (knex) {

    await knex.schema.alterTable('videos', (table) => {
        // Revert back to not null if needed
        table.dropColumn('preview_url');
        table.dropColumn('placeholder_url');
    });
};
