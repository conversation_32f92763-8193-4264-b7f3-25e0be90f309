'use strict';

exports.up = (knex) => {

    return knex.schema.createTable('client_usage_summaries', (table) => {

        table.increments('id').primary(); // PK
        table
            .integer('client_id')
            .unsigned()
            .notNullable()
            .references('id')
            .inTable('users')
            .onDelete('CASCADE'); // FK to users
        table.integer('total_members').notNullable().defaultTo(0);
        table.integer('total_storage_size').notNullable().defaultTo(0);
        table.timestamp('created_at').defaultTo(knex.fn.now());
        table.timestamp('updated_at').defaultTo(knex.fn.now());
    });
};

exports.down = (knex) => {

    return knex.schema.dropTable('billing');
};
