'use strict';

exports.up = async (knex) => {

    await knex.schema.dropTableIfExists('photographers_shoots').createTable('photographers_shoots', (table) => {

        table.integer('photographer_id').notNullable().references('photographers.id').onDelete('CASCADE');
        table.integer('shoot_id').notNullable().references('shoots.id').onDelete('CASCADE');
        table.primary(['photographer_id', 'shoot_id']);
    });
};

exports.down = async (knex) => {

    await knex.schema.dropTable('photographers_shoots');
};
