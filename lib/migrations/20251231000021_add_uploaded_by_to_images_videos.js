'use strict';

exports.up = async function (knex) {

    await knex.schema.alterTable('images', (table) => {

        table.string('uploaded_by').nullable();
    });
    await knex.schema.alterTable('videos', (table) => {

        table.string('uploaded_by').nullable();
    });
};

exports.down = async function (knex) {

    await knex.schema.alterTable('videos', (table) => {

        table.dropColumn('uploaded_by');
    });
    await knex.schema.alterTable('images', (table) => {

        table.dropColumn('uploaded_by');
    });
};
