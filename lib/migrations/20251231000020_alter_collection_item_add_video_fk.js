'use strict';

exports.up = (knex) => {

    return knex.schema.alterTable('collection_items', (table) => {

        table.integer('video_id').unsigned().nullable();
        table.integer('image_id').nullable().alter();
        // Add video_id column with foreign key reference to videos table
        table.foreign('video_id').references('id').inTable('videos').onDelete('SET NULL');
    });
};

exports.down = (knex) => {

    return knex.schema.alterTable('collection_items', (table) => {

        // Drop video_id column
        table.dropColumn('video_id');
        // Change image_id column to not nullable
        table.integer('image_id').notNullable().alter();
        // Drop foreign key constraint
        table.dropForeign('video_id', 'collection_items_video_id_foreign');
    });
};
