'use strict';

const photographersTableName = 'photographers';

exports.up = async (knex) => {

    await knex.schema.alterTable(photographersTableName, (table) => {

        table.boolean('is_internal').notNullable().defaultTo(false);
    });
};

exports.down = async (knex) => {

    await knex.schema.alterTable(photographersTableName, (table) => {

        table.dropColumn('is_internal');
    });
};
