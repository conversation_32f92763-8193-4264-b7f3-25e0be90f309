'use strict';

exports.up = async function (knex) {

    await knex.schema.alterTable('download_request_jobs', (table) => {
        // Drop the not null constraint from downloadUrl
        table.string('downloadUrl', 512).nullable().alter();
    });
};

exports.down = async function (knex) {

    await knex.schema.alterTable('download_request_jobs', (table) => {
        // Revert back to not null if needed
        table.string('downloadUrl', 512).notNullable().alter();
    });
};
