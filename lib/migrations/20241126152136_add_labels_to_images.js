'use strict';

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {

    return knex.schema.alterTable('images', (table) => {
        // Add JSONB column for labels with default empty array
        table.jsonb('labels').defaultTo(knex.raw('?::jsonb', ['[]']));

        // Add GIN index for efficient querying within labels
        // This is optional but recommended if you plan to query the labels often
        return knex.raw(`
          CREATE INDEX IF NOT EXISTS images_labels_gin_idx 
          ON images USING GIN (labels jsonb_path_ops)
      `);
    });
};

/**
* @param { import("knex").Knex } knex
* @returns { Promise<void> }
*/
exports.down = function (knex) {

    return knex.schema.alterTable('images', (table) => {
        // Drop the index first
        knex.raw('DROP INDEX IF EXISTS images_labels_gin_idx');

        // Then drop the column
        table.dropColumn('labels');
    });
};
