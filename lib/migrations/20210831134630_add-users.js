'use strict';

exports.up = async (knex) => {

    await knex.schema.dropTableIfExists('users').createTable('users', (table) => {

        table.increments('id').primary();
        table.string('name').notNullable();
        table.string('email', 320).notNullable().unique();
        table.string('phone', 20).notNullable();
        table.integer('photographer_id').references('photographers.id').unique().onDelete('RESTRICT');
        table.integer('client_id').references('clients.id').unique().onDelete('RESTRICT');
        table.boolean('account_verified').notNullable().defaultTo(false);
        table.string('status', 10).notNullable().defaultTo('none');
        table.timestamp('creation_date').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
        table.timestamp('last_update').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    });

    // Check constraints
    await knex.schema.raw('ALTER TABLE users ADD CHECK (photographer_id IS NULL OR client_id IS NULL)');

};

exports.down = async (knex) => {

    await knex.schema.dropTable('users');
};
