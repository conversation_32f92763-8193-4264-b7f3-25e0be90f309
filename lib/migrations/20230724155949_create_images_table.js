'use strict';

exports.up = function (knex) {

    return knex.schema.createTable('images', (table) => {

        table.increments('id').primary();
        table.integer('shoot_id').unsigned().notNullable();
        table.string('url', 255).notNullable();
        table.string('status', 20).notNullable().defaultTo('pending'); // Set default value to 'pending'
        table.string('feedback').nullable();

        // Add foreign key constraint for shoot_id referencing shoots table
        table.foreign('shoot_id').references('id').inTable('shoots');
    });
};

exports.down = function (knex) {

    return knex.schema.dropTable('images');
};
