'use strict';

exports.up = function (knex) {

    return knex.schema
        .table('images', (table) => {

            table.specificType('small_embedding', 'vector(1536)');
            table.text('small_embedding_context');
        })
        .then(() => {
            // Add HNSW index without dim parameter
            return knex.raw(`
                CREATE INDEX ON images
                    USING hnsw (small_embedding vector_cosine_ops);
            `);
        });
};

exports.down = function (knex) {

    return knex.schema
        .raw('DROP INDEX IF EXISTS images_small_embedding_idx')
        .then(() => {

            return knex.schema.table('images', (table) => {

                table.dropColumn('small_embedding');
                table.dropColumn('small_embedding_context');
            });
        });
};
