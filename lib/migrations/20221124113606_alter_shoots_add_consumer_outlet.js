'use strict';

const shootsTableName = 'shoots';
const ordersTableName = 'orders';
const clientsTableName = 'clients';

exports.up = async (knex) => {

    await knex.schema.alterTable(shootsTableName, (table) => {

        table.string('outlet_code').defaultTo(null);
        table.string('outlet_name').defaultTo(null);
        table.integer('consumer_client_id').references(`${clientsTableName}.id`).onDelete('SET NULL').defaultTo(null);
    });

    const shootsToBeUpdated = await knex(shootsTableName).whereNull('consumer_client_id');

    for (const { id, order_id } of shootsToBeUpdated) {
        const { client_id } = await knex(ordersTableName)
            .where('id', order_id)
            .first();

        await knex(shootsTableName)
            .where('id', id)
            .update('consumer_client_id', client_id);
    }
};

exports.down = async (knex) => {

    await knex.schema.alterTable(shootsTableName, (table) => {

        table.dropColumn('outlet_code');
        table.dropColumn('outlet_name');
        table.dropColumn('consumer_client_id');
    });
};
