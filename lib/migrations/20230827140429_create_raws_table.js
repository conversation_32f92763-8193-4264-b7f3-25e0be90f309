'use strict';

exports.up = function (knex) {

    return knex.schema.createTable('raws', (table) => {

        table.increments('id').primary();
        table.integer('shoot_id').unsigned().notNullable();
        table.string('url', 255).notNullable();
        table.string('preview_url', 255).notNullable();
        table.string('placeholder_url', 255).notNullable();
        table.string('filename', 255).notNullable();
        table.string('status', 20).notNullable().defaultTo('pending');
        table.string('notes').nullable();

        table.foreign('shoot_id').references('id').inTable('shoots');
    });
};

exports.down = function (knex) {

    return knex.schema.dropTable('raws');
};
