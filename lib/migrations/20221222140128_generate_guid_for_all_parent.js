'use strict';

const { v4: uuidv4 } = require('uuid');

exports.up = async (knex) => {

    const clients = await knex('clients').whereNotNull('registration_guid');

    for (const client of clients) {
        await knex('clients')
            .where('id', client.id)
            .update({ registration_guid: uuidv4() });
    }

    //at this moment there are no child accounts yet, so I consider all the accounts present
    //in the system as parent accounts and for all I generate a guid

};

exports.down = async (knex) => {

};
