'use strict';

exports.up = async (knex) => {

    await knex.schema.dropTableIfExists('clients').createTable('clients', (table) => {

        table.increments('id').primary();
        table.string('company_name').notNullable();
        table.string('industry').notNullable();
        table.integer('size').notNullable();
        table.string('country', 2).notNullable();
        table.integer('payment_details_id').references('payment_details.id').notNullable().onDelete('CASCADE');
        table.string('status', 10).notNullable().defaultTo('none');
        table.timestamp('creation_date').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
        table.timestamp('last_update').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));

    });
};

exports.down = async (knex) => {

    await knex.schema.dropTable('clients');
};
