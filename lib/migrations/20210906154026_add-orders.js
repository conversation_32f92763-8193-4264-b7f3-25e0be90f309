'use strict';

exports.up = async (knex) => {

    await knex.schema.dropTableIfExists('orders').createTable('orders', (table) => {

        table.increments('id').primary();
        table.integer('client_id').references('clients.id').notNullable().onDelete('CASCADE');
        table.string('purchase_type', 255).notNullable();
        table.json('payment_details').notNullable();
        table.text('billing_address').notNullable();
        table.boolean('payment_confirmed').notNullable();
        table.timestamp('payment_date');
        table.string('status', 10).notNullable().defaultTo('none');
        table.timestamp('creation_date').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
        table.timestamp('last_update').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));

    });
};

exports.down = async (knex) => {

    await knex.schema.dropTable('orders');
};
