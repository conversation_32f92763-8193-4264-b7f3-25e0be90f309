'use strict';

exports.up = async (knex) => {

    await knex.schema.alterTable('clients', (table) => {
        // Modify company_name to be nullable (optional)
        table.string('company_name').alter().nullable();
        // Modify industry to be nullable (optional)
        table.string('industry').alter().nullable();
        // Modify contact fields to be nullable (optional)
        table.string('contact_name').alter().nullable();
        table.string('contact_email').alter().nullable();
        table.string('contact_phone').alter().nullable();
    });

    // Also make phone nullable in users table
    await knex.schema.alterTable('users', (table) => {
        // Modify phone to be nullable (optional)
        table.string('phone').alter().nullable();
    });
};

exports.down = async (knex) => {

    await knex.schema.alterTable('clients', (table) => {
        // Revert changes by making fields required again
        table.string('company_name').alter().notNullable();
        table.string('industry').alter().notNullable();
        table.string('contact_name').alter().notNullable();
        table.string('contact_email').alter().notNullable();
        table.string('contact_phone').alter().notNullable();
    });

    // Revert phone field in users table
    await knex.schema.alterTable('users', (table) => {
        // Make phone required again
        table.string('phone').alter().notNullable();
    });
};
