'use strict';

exports.up = (knex) => {

    return knex.schema.createTable('stacks', (table) => {

        table.increments('id').primary();
        table.text('description').notNullable();
        table.integer('user_id').unsigned().references('id').inTable('users').onDelete('CASCADE');
        table.timestamp('created_at').defaultTo(knex.fn.now());
        table.timestamp('updated_at').defaultTo(knex.fn.now());
    });
};

exports.down = (knex) => {

    return knex.schema.dropTableIfExists('stacks');
};
