'use strict';

exports.up = async (knex) => {

    await knex.schema.dropTableIfExists('photographers_services').createTable('photographers_services', (table) => {

        table.integer('photographer_id').notNullable().references('photographers.id').onDelete('CASCADE');
        table.integer('service_id').notNullable().references('services.id').onDelete('CASCADE');
        table.integer('rate').notNullable().defaultTo(1);
        table.primary(['photographer_id', 'service_id']);
    });
};

exports.down = async (knex) => {

    await knex.schema.dropTable('photographers_services');
};
