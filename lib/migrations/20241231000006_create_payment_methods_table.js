'use strict';

exports.up = (knex) => {

    return knex.schema.createTable('payment_methods', ( table ) => {

        table.increments('id').primary(); // PK
        table
            .integer('user_id')
            .unsigned()
            .notNullable()
            .references('id')
            .inTable('users')
            .onDelete('CASCADE'); // FK to users table
        table.string('last4_digit').notNullable();
        table
            .enum('status', ['deleted', 'active', 'inactive'])
            .notNullable()
            .defaultTo('active');
        table.string('type').notNullable(); // Assuming possible types
        table.string('provider').notNullable().defaultTo('stripe');
        table.string('reference').notNullable();
        table.string('full_name').notNullable();
        table.string('is_default').defaultTo(false);
        table.timestamp('created_at').defaultTo(knex.fn.now());
        table.timestamp('updated_at').defaultTo(knex.fn.now());
    });
};

exports.down =  (knex) => {

    return knex.schema.dropTable('payment_methods');
};
