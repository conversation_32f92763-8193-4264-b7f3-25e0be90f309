'use strict';

exports.up = async (knex) => {

    await knex.schema.alterTable('photographers', (table) => {

        table.string('main_location');
        table.boolean('is_pro').notNullable().defaultTo(false);

    });

    await knex('photographers')
        .whereNull('main_location')
        .update({ main_location: 'default_location' });

    await knex.schema.alterTable('photographers', (table) => {

        table.string('main_location').alter().notNullable();

    });
};

exports.down = async (knex) => {

    await knex.schema.alterTable('photographers', (table) => {

        table.dropColumn('main_location');
        table.dropColumn('is_pro');
    });
};
