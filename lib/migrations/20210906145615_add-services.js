'use strict';

exports.up = async (knex) => {

    await knex.schema.dropTableIfExists('services').createTable('services', (table) => {

        table.increments('id').primary();
        table.integer('purpose_id').references('purposes.id').notNullable().onDelete('CASCADE');
        table.string('name', 100).notNullable();
        table.string('status', 10).notNullable().defaultTo('none');
        table.timestamp('creation_date').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
        table.timestamp('last_update').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));

    });

};

exports.down = async (knex) => {

    await knex.schema.dropTable('services');
};
