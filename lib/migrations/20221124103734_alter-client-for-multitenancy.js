'use strict';

const { v4: uuidv4 } = require('uuid');

exports.up = async (knex) => {

    await knex.schema.alterTable('clients', (table) => {

        table.string('registration_guid');
        table.integer('parent_client_id').references('clients.id').onDelete('CASCADE');
    });

    //at this moment there are no child accounts yet, so I consider all the accounts present
    //in the system as parent accounts and for all I generate a guid
    await knex('clients')
        .whereNull('registration_guid')
        .update({ registration_guid: uuidv4() });
};

exports.down = async (knex) => {

    await knex.schema.alterTable('clients', (table) => {

        table.dropColumn('registration_guid');
        table.dropColumn('parent_client_id');
    });
};
