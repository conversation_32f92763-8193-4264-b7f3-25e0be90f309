'use strict';

exports.up = function (knex) {

    return knex.schema.table('shoots', (table) => {

        table.dropColumn('raws_selection');
        table.dropColumn('old');
        table.dropColumn('images_preview');
    });
};

exports.down = function (knex) {

    return knex.schema.table('shoots', (table) => {
    // When rolling back, you will typically want to add the columns back.
    // I'm going to assume they were all text columns for this example,
    // but you might need to adjust the column types based on your original schema.
        table.text('raws_selection');
        table.text('old');
        table.text('images_preview');
    });
};
