'use strict';

exports.up = function (knex) {

    return knex.schema.createTable('collections', (table) => {

        table.increments('id').primary();
        table.string('name', 255).notNullable();
        table.integer('user_id').unsigned().notNullable();
        table.timestamp('created_at').defaultTo(knex.fn.now());
        table.timestamp('updated_at').defaultTo(knex.fn.now());

        // Add foreign key constraint for user_id referencing users table with cascade delete
        table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');

        // Add unique constraint on combination of name and user_id
        table.unique(['name', 'user_id']);
    });
};

exports.down = function (knex) {

    return knex.schema.dropTable('collections');
};
