'use strict';

const editorsTableName = 'editors';

exports.up = async (knex) => {

    await knex.schema.dropTableIfExists(editorsTableName).createTable(editorsTableName, (table) => {

        table.increments('id').primary();
        table.string('status', 10).notNullable().defaultTo('none');
        table.timestamp('creation_date').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
        table.timestamp('last_update').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    });
};

exports.down = async (knex) => {

    await knex.schema.dropTable(editorsTableName);
};
