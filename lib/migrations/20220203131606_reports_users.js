'use strict';

exports.up = async (knex) => {

    await knex.schema.dropTableIfExists('reports_users').createTable('reports_users', (table) => {

        table.increments('id').primary();
        table.integer('report_id').notNullable().references('reports.id').onDelete('CASCADE');
        table.integer('shoot_id').notNullable().references('shoots.id').onDelete('CASCADE');
        table.integer('performer_user_id').notNullable().references('users.id').onDelete('CASCADE');
        table.integer('target_client_user_id').references('users.id').onDelete('CASCADE');
        table.integer('target_photographer_user_id').references('users.id').onDelete('CASCADE');
        table.string('starting_status').notNullable();
        table.string('target_status').notNullable();
        table.timestamp('creating_data').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    });

};

exports.down = async (knex) => {

    await knex.schema.dropTable('reports_users');
};
