'use strict';

exports.up = async (knex) => {

    await knex.schema.dropTableIfExists('photographers_specializations').createTable('photographers_specializations', (table) => {

        table.integer('photographer_id').notNullable().references('photographers.id').onDelete('CASCADE');
        table.integer('specialization_id').notNullable().references('specializations.id').onDelete('CASCADE');
        table.primary(['photographer_id', 'specialization_id']);
    });
};

exports.down = async (knex) => {

    await knex.schema.dropTable('photographers_specializations');
};
