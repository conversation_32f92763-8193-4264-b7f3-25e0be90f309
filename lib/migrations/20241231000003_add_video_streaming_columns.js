'use strict';

exports.up = function (knex) {

    return knex.schema.table('videos', (table) => {

        table.string('video_hls_url', 512).nullable();
        table.string('preview_video_url', 512).nullable();
        table.string('seek_video_url', 512).nullable();
        table.string('sprite_sheet_url', 512).nullable();
        table.string('transcript_srt_url', 512).nullable();
        table.string('transcript_vtt_url', 512).nullable();
    });
};

exports.down = function (knex) {

    return knex.schema.table('videos', (table) => {

        table.dropColumn('video_hls_url');
        table.dropColumn('preview_video_url');
        table.dropColumn('seek_video_url');
        table.dropColumn('sprite_sheet_url');
        table.dropColumn('transcript_srt_url');
        table.dropColumn('transcript_vtt_url');
    });
};
