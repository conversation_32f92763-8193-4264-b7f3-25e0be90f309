'use strict';

exports.up = async (knex) => {

    await knex.schema.alterTable('clients', (table) => {

        table.string('contact_name');
        table.string('contact_email', 320);
        table.string('contact_phone', 20);

    });
    await knex('clients')
        .whereNull('contact_name')
        .whereNull('contact_email')
        .whereNull('contact_phone')
        .update({ contact_name: 'test', contact_email: '<EMAIL>', contact_phone: '1234567890' });
    await knex.schema.alterTable('clients', (table) => {

        table.string('contact_name').alter().notNullable();
        table.string('contact_email', 320).alter().notNullable();
        table.string('contact_phone', 20).alter().notNullable();

    });
};

exports.down = async (knex) => {

    await knex.schema.alterTable('clients', (table) => {

        table.dropColumn('contact_name');
        table.dropColumn('contact_email');
        table.dropColumn('contact_phone');

    });
};
