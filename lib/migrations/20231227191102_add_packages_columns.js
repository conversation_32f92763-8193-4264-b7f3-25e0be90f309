'use strict';

exports.up = function (knex) {

    return knex.schema.table('packages', async (table) => {
        // Add back the 'price_tag' column with a default value of null
        const hasPriceTagColumn = await knex.schema.hasColumn('packages', 'price_tag');

        if (!hasPriceTagColumn) {
            table.string('price_tag').defaultTo(null);
        }

        const hasStripeProductIdColumn = await knex.schema.hasColumn('packages', 'stripe_product_id');
        if (!hasStripeProductIdColumn) {
            table.string('stripe_product_id').defaultTo(null);
        }

        const hasPackageTypeColumn = await knex.schema.hasColumn('packages', 'package_type');
        if (!hasPackageTypeColumn) {
            table.string('package_type').defaultTo('b2b');
        }
    });
};

exports.down = function (knex) {

    return knex.schema.table('packages', async (table) => {
        // Remove the 'price_tag' column
        const hasPriceTagColumn = await knex.schema.hasColumn('packages', 'price_tag');
        if (hasPriceTagColumn) {
            table.dropColumn('price_tag');
        }

        const hasStripeProductIdColumn = await knex.schema.hasColumn('packages', 'stripe_product_id');

        if (hasStripeProductIdColumn) {
            table.dropColumn('stripe_product_id');
        }

        const hasPackageTypeColumn = await knex.schema.hasColumn('packages', 'package_type');
        if (hasPackageTypeColumn) {
            table.dropColumn('package_type');
        }
    });
};
