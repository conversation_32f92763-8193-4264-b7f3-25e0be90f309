'use strict';

exports.up = async (knex) => {

    await knex.schema.dropTableIfExists('payment_details').createTable('payment_details', (table) => {

        table.increments('id').primary();
        table.string('type', 255).notNullable();
        table.json('details').notNullable();
        table.string('status', 10).notNullable().defaultTo('none');
        table.timestamp('creation_date').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
        table.timestamp('last_update').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));

    });
};

exports.down = async (knex) => {

    await knex.schema.dropTable('payment_details');
};
