'use strict';

exports.up = async (knex) => {

    await knex.schema.dropTableIfExists('shoots_services').createTable('shoots_services', (table) => {

        table.integer('shoot_id').notNullable().references('shoots.id').onDelete('CASCADE');
        table.integer('service_id').notNullable().references('services.id').onDelete('CASCADE');
        table.integer('picture_number').notNullable();
        table.json('package').notNullable();
        table.primary(['shoot_id', 'service_id']);
    });
};

exports.down = async (knex) => {

    await knex.schema.dropTable('shoots_services');
};
