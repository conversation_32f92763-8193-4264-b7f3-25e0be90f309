'use strict';

exports.up = async function (knex) {

    await knex.schema.createTable('download_request_jobs', (table) => {
        // Primary key
        table.increments('id').primary();

        // Foreign key
        table.integer('shoot_id')
            .notNullable()
            .references('id')
            .inTable('shoots')
            .onDelete('CASCADE');

        // Other columns
        table.string('message', 1024).notNullable();
        table.string('status', 20).notNullable().defaultTo('pending');
        table.string('downloadUrl', 512).notNullable();

        // Timestamps
        table.datetime('created_at').notNullable().defaultTo(knex.fn.now());
        table.datetime('updated_at').notNullable().defaultTo(knex.fn.now());

        // Indexes
        table.index('shoot_id');
        table.index('status');
    });
};

exports.down = async function (knex) {

    await knex.schema.dropTableIfExists('download_request_jobs');
};
