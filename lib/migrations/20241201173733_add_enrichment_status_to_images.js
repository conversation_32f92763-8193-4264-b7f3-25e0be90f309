'use strict';

// migrations/YYYYMMDDHHMMSS_add_enrichment_status_to_images.js
/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {

    return knex.schema.alterTable('images', (table) => {

        table.enum('enrichment_status', [
            'pending',      // Initial state, needs processing
            'queued',       // Sent to SQS queue
            'processing',   // Being processed by consumer
            'processed',    // Successfully processed
            'error'         // Processing failed
        ]).notNullable().defaultTo('pending');

        // Index for efficient querying
        table.index('enrichment_status');
    });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {

    return knex.schema.alterTable('images', (table) => {

        table.dropColumn('enrichment_status');
    });
};
