'use strict';

exports.up =  (knex) => {

    return knex.schema.createTable('package_plans', (table) => {

        table.increments('id').primary(); // PK
        table.string('label').notNullable();
        table.string('name').notNullable();
        table.string('stripe_reference'); // Stripe product/price ID
        table.enum('status', ['active', 'inactive']).notNullable().defaultTo('active');
        table.decimal('price', 10, 2); // Price in dollars
        table.integer('discount'); // Discount percentage
        table.decimal('origin_price', 10, 2); // Discounted price
        table.string('currency').notNullable().defaultTo('aed'); // Default currency
        table.string('provider').notNullable().defaultTo('stripe');
        table.string('interval').notNullable(); // e.g., 'monthly', 'yearly'
        table.integer('interval_count').notNullable(); // e.g., 'monthly', 'yearly'
        table.text('description').notNullable();
        table.boolean('is_popular').defaultTo(false); // Flag for popular plans
        table.jsonb('plans').notNullable(); // JSONB for additional plan details
        table.integer('weight').defaultTo(0); // Weight for sorting or prioritization
        table.timestamp('created_at').defaultTo(knex.fn.now());
        table.timestamp('updated_at').defaultTo(knex.fn.now());
    });
};

exports.down = (knex) => {

    return knex.schema.dropTable('package_plans');
};
