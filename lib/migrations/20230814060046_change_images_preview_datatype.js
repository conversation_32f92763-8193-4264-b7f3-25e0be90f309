'use strict';

exports.up = function (knex) {
    // Modify the existing 'images_preview' column to change its data type
    return knex.schema.alterTable('shoots', (table) => {

        table.boolean('images_preview').notNullable().defaultTo(false).alter();
    });
};

exports.down = function (knex) {
    // Revert the changes by modifying the 'images_preview' column back to string data type
    return knex.schema.alterTable('shoots', (table) => {

        table.string('images_preview').defaultTo(false).alter();
    });
};
