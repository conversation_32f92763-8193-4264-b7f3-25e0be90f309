'use strict';

exports.up = function (knex) {

    return knex.schema.table('packages', (table) => {
    // Remove the 'price_tag' column
        table.dropColumn('price_tag');
        // Remove the 'stripe_product_key' column
        table.dropColumn('stripe_product_id');
        // Remove the 'package_type' column
        table.dropColumn('package_type');
    });
};

exports.down = function (knex) {

    return knex.schema.table('packages', (table) => {
    // Add back the 'price_tag' column with its original definition
        table.string('price_tag').defaultTo('');
        // Add back the 'stripe_product_key' column with its original definition
        table.string('stripe_product_id');
        // Add back the 'package_type' column with its original definition
        table.string('package_type');
    });
};
