'use strict';

exports.up = async (knex) => {

    await knex.schema.dropTableIfExists('packages').createTable('packages', (table) => {

        table.increments('id').primary();
        table.integer('service_id').references('services.id').notNullable().onDelete('CASCADE');
        table.string('name').notNullable();
        table.text('description').notNullable();
        table.decimal('price', 8, 2).notNullable();
        table.string('currency', 3).notNullable();
        table.integer('duration').notNullable();
        table.integer('picture_number').notNullable();
        table.decimal('price_per_additional_photo', 8, 2).notNullable();
        table.string('status', 10).notNullable().defaultTo('none');
        table.timestamp('creation_date').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
        table.timestamp('last_update').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));

    });

};

exports.down = async (knex) => {

    await knex.schema.dropTable('packages');
};
