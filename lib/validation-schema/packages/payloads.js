'use strict';

const Joi = require('joi');

module.exports.search = Joi.object(
    {
        sorting: Joi.object({
            field: Joi.string().valid().required(),
            direction: Joi.string().valid('asc', 'desc').default('desc')
        }), // NOTE: we do not use it right now, but it's ready should we decide to
        filters: Joi.object({
            services: Joi.array().items(Joi.number().integer())
        }).default({})
    }
);

module.exports.create = Joi.object(
    {
        service_id: Joi.number().integer().required(),
        name: Joi.string().required(),
        price: Joi.number().precision(2).min(0).required(),
        photographer_revenue: Joi.number().precision(2).min(0).less(Joi.ref('price')).required(),
        duration: Joi.number().integer().min(0).required(),
        picture_number: Joi.number().integer().min(0).required(),
        description: Joi.string().required(),
        is_plus: Joi.boolean().required(),
        hide: Joi.boolean().required(),
        package_type: Joi.string().allow('').allow(null).default(''),
        price_tag: Joi.string().allow('').allow(null).default(''),
        stripe_product_id: Joi.string().allow('').allow(null).default('')
    }
);
