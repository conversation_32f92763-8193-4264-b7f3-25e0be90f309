'use strict';

const Joi = require('joi');

module.exports.pack =  Joi.object(
    {
        id: Joi.number().integer(),
        service_id: Joi.number().integer().required(),
        name: Joi.string().required(),
        description: Joi.string().required(),
        price: Joi.number().required(),
        photographer_revenue: Joi.number().precision(2).min(0).required(),
        currency: Joi.string().required(),
        duration: Joi.number().integer().required(),
        picture_number: Joi.number().integer().required(),
        is_plus: Joi.boolean().default(false),
        price_per_additional_photo: Joi.number().required(),
        status: Joi.string().max(10),
        creation_date: Joi.date().required(),
        last_update: Joi.date().required(),
        num_pictures: Joi.number(),
        price_tag: Joi.string().allow('').allow(null).default(''),
        package_type: Joi.string().allow('').allow(null).default(''),
        stripe_product_id: Joi.string().allow('').allow(null).default('')
    }
);
