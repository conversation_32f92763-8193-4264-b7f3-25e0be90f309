'use strict';

const Joi = require('joi');

const dailyReport = Joi.object().pattern(/\d{4}-(0?[1-9]|1[012])-(0?[1-9]|[12][0-9]|3[01])/,
    Joi.object().pattern(/^[a-zA-Z0-9_][a-zA-Z0-9_.]*/, Joi.object({
        total: Joi.number().integer(),
        delta: Joi.number().integer()
    })
    )
);

const userReport = Joi.object({
    user_id: Joi.number().integer().min(1).required(),
    user_name: Joi.string().required(),
    user_photographer_id: Joi.number().integer().min(1),
    user_client_id: Joi.number().integer().min(1),
    target_photographer_user_id: Joi.number().integer().min(1),
    target_client_user_id: Joi.number().integer().min(1),
    confirmed_count: Joi.number().integer().min(0),
    confirmed_revenue: Joi.number().min(0),
    declined_count: Joi.number().integer().min(0),
    declined_revenue: Joi.number().min(0),
    canceled_count: Joi.number().integer().min(0),
    canceled_revenue: Joi.number().min(0),
    uploaded_count: Joi.number().integer().min(0),
    uploaded_revenue: Joi.number().min(0),
    completed_count: Joi.number().integer().min(0),
    completed_revenue: Joi.number().min(0),
    created_count: Joi.number().integer().min(0),
    created_revenue: Joi.number().min(0),
    subclients: Joi.array().optional()
}).xor('user_photographer_id', 'user_client_id').xor('target_photographer_user_id', 'target_client_user_id');

module.exports = {
    dailyReport,
    userReport
};
