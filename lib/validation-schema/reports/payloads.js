'use strict';

const Joi = require('joi');

const search = Joi.object({
    filters: Joi.object({
        time: Joi.object({
            from: Joi.date(),
            to: Joi.date().when('from', { is: Joi.exist(), then: Joi.date().min(Joi.ref('from')) })
        }).default({})
    })
});

const searchUsers = Joi.object({
    filters: Joi.object({
        time: Joi.object({
            from: Joi.date().required(),
            to: Joi.date().min(Joi.ref('from')).required()
        }).required(),
        userType: Joi.string().valid('client', 'photographer').default('client').required(),
        name: Joi.string(),
        userId: Joi.number().integer().min(1),
        photographerId: Joi.number().integer().min(1),
        clientId: Joi.number().integer().min(1)
    }),
    pagination: Joi.object({
        limit: Joi.number().integer().min(0).max(100),
        offset: Joi.number().integer().min(0)
    }).default({}),
    sorting: Joi.object({
        field: Joi.string(), // NOTE: this is validated internally by the report user service
        direction: Joi.string().valid('asc', 'desc').default('desc')
    }).default({})
});

module.exports = {
    search,
    searchUsers
};
