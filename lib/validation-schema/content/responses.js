'use strict';

const Joi = require('joi');

// Define the content item response schema
const contentItem = Joi.object({
    id: Joi.number().integer().required(),
    shoot_id: Joi.number().allow(null),
    user_id: Joi.number().required(),
    url: Joi.string().required(),
    status: Joi.string().valid('pending', 'approved', 'resubmitted').required(),
    filename: Joi.string().required(),
    folder: Joi.string().required(),
    filepath: Joi.string().required(),
    aspect_ratio: Joi.number().required(),
    file_size: Joi.number().allow(null),
    width: Joi.number().allow(null),
    height: Joi.number().allow(null),
    placeholder_url: Joi.string().allow(null),
    preview_url: Joi.string().allow(null),
    created_at: Joi.date().iso().required(),
    updated_at: Joi.date().iso().required(),
    // Video-specific properties
    duration: Joi.number().allow(null),
    video_url: Joi.string().allow(null),
    preview_video_url: Joi.string().allow(null),
    seek_video_url: Joi.string().allow(null),
    sprite_sheet_url: Joi.string().allow(null),
    transcript_srt_url: Joi.string().allow(null),
    transcript_vtt_url: Joi.string().allow(null)
});

// Export the content item schema
module.exports.contentItem = contentItem;

// Export a schema for a list of content items
module.exports.contentList = Joi.array().items(contentItem);
