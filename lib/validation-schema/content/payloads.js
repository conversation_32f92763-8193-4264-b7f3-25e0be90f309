'use strict';

const Joi = require('joi');

// Define base properties that all content types share
const contentSchema = Joi.object({
    id: Joi.number().optional(),
    shoot_id: Joi.number().optional().allow(null),
    user_id: Joi.number().optional().allow(null),
    stack_id: Joi.number().optional().allow(null),
    url: Joi.string().optional().allow(null),
    preview_url: Joi.string().optional().allow(null),
    placeholder_url: Joi.string().optional().allow(null),
    optimized_url: Joi.string().optional().allow(null),
    status: Joi.string().required(),
    feedback: Joi.string().optional().allow(null),
    filename: Joi.string().optional().allow(null),
    folder: Joi.string().optional().allow(null),
    filepath: Joi.string().optional().allow(null),
    aspect_ratio: Joi.number().optional().allow(null),
    file_size: Joi.number().optional().allow(null),
    width: Joi.number().optional().allow(null),
    height: Joi.number().optional().allow(null),
    type: Joi.string().valid('image', 'video').optional(),
    // Allow video-specific fields on all content types with null values for images
    duration: Joi.number().optional().allow(null),
    codec: Joi.string().optional().allow(null),
    framerate: Joi.number().integer().optional().allow(null),
    bitrate: Joi.number().integer().optional().allow(null),
    video_hls_url: Joi.string().optional().allow(null),
    preview_video_url: Joi.string().optional().allow(null),
    seek_video_url: Joi.string().optional().allow(null),
    sprite_sheet_url: Joi.string().optional().allow(null),
    transcript_srt_url: Joi.string().optional().allow(null),
    transcript_vtt_url: Joi.string().optional().allow(null),
    shoot_date: Joi.date().optional().allow(null),
    customer_name: Joi.string().optional().allow(null),
    service_id: Joi.number().optional().allow(null),
    error: Joi.string().optional().allow(null),
    name: Joi.string().optional().allow(null),
    labels: Joi.array().items(Joi.string()).optional(),
    created_at: Joi.date().optional().allow(null),
    processing_status: Joi.string().optional().allow(null),
    uploaded_by: Joi.string().max(255).optional().allow(null),
    // Comments associated with this image
    comments: Joi.array().items(Joi.object({
        id: Joi.number(),
        content: Joi.string(),
        user_id: Joi.number(),
        user_name: Joi.string(),
        image_id: Joi.number(),
        created_at: Joi.date().iso(),
        updated_at: Joi.date().iso()
    })).optional().description('List of comments associated with this image')
});

// Full payload schema for batch operations
module.exports.batchContent = Joi.object({
    content: Joi.array().items(contentSchema).required()
});

// Export individual schemas for reuse in other contexts
module.exports.content = contentSchema;

// Search payload schema
module.exports.search = Joi.object({
    filters: Joi.object({
        date: Joi.object({
            from: Joi.date(),
            to: Joi.date()
        }).optional(),
        services: Joi.array().items(Joi.number()).optional(),
        aspectRatios: Joi.array().items(
            Joi.string().valid('square', 'landscape', 'portrait')
        ).default([]).optional(),
        metadataSearchQuery: Joi.string().allow('', null).optional(),
        visualSearchQuery: Joi.string().allow('', null).optional(),
        searchQuery: Joi.string().allow('', null).optional(),
        contentType: Joi.string().valid('image', 'video').optional(),
        contentTypes: Joi.array().optional().default(['image', 'video']),
        clientId: Joi.number().allow(null).optional(),
        collectionId: Joi.number().allow(null).optional(),
        status: Joi.string().optional(),
        shootId: Joi.string().optional()
    }).default({}),
    pagination: Joi.object({
        limit: Joi.number().integer().min(1).default(20),
        cursor: Joi.string().allow(null).default(null)
    }).default({
        limit: 20,
        cursor: null
    })
});
