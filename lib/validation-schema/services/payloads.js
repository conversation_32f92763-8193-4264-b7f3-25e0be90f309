'use strict';

const Joi = require('joi');

module.exports.search = Joi.object({
    sorting: Joi.object({
        field: Joi.string().valid().required(),
        direction: Joi.string().valid('asc', 'desc').default('desc')
    }), // NOTE: we do not use it right now, but it's ready should we decide to
    filters: Joi.object({
        purposes: Joi.array().items(Joi.number().integer()),
        names: Joi.array().items(Joi.string()) // Adding names as an optional array of strings
    }).default({})
});

module.exports.create = Joi.object(
    {
        purpose_id: Joi.number().integer().required(),
        name: Joi.string().max(100).required()
    }
);

module.exports.reassign = Joi.object(
    {
        from_id: Joi.string().required(),
        to_id: Joi.string().required()
    }
);
