'use strict';

const Joi = require('joi');

const user =  Joi.object(
    {
        id: Joi.number().integer(),
        name: Joi.string().required(),
        email: Joi.string().max(320).required(),
        phone: Joi.string().max(20).required().allow('', null),
        photographer_id: Joi.number().integer().required().allow(null),
        editor_id: Joi.number().integer().required().allow(null),
        client_id: Joi.number().integer().required().allow(null),
        role: Joi.string().required(),
        subClientRole: Joi.string(),
        status: Joi.string().max(10).required(),
        account_verified: Joi.boolean(),
        creation_date: Joi.date(),
        last_update: Joi.date(),
        auth_provider_reference: Joi.string().max(200).optional().allow(null)
    }
);
const additionalInformations = (position) => {

    return {
        company_name: Joi.string().allow(null)
            .when(Joi.ref(position), { is: Joi.valid('client'), then: Joi.optional(), otherwise: Joi.forbidden() }),
        industry: Joi.string().allow(null)
            .when(Joi.ref(position), { is: Joi.valid('client'), then: Joi.optional(), otherwise: Joi.forbidden() }),
        contact_name: Joi.string().allow('', null)
            .when(Joi.ref(position), { is: Joi.valid('client'), then: Joi.optional(), otherwise: Joi.forbidden() }),
        contact_email: Joi.string().max(320).allow('', null)
            .when(Joi.ref(position), { is: Joi.valid('client'), then: Joi.optional(), otherwise: Joi.forbidden() }),
        contact_phone: Joi.string().max(20).allow('', null)
            .when(Joi.ref(position), { is: Joi.valid('client'), then: Joi.optional(), otherwise: Joi.forbidden() }),
        main_location: Joi.string().required().allow(null)
            .when(Joi.ref(position), { is: Joi.valid('admin'), then: Joi.forbidden(), otherwise: Joi.required() }),
        country: Joi.string().max(2).allow(null)
            .when(Joi.ref(position), { is: Joi.valid('client'), then: Joi.optional(), otherwise: Joi.forbidden() }),
        birthdate: Joi.date()
            .when(Joi.ref(position), { is: Joi.valid('photographer'), then: Joi.optional(), otherwise: Joi.forbidden() }),
        english_level: Joi.string().max(100)
            .when(Joi.ref(position), { is: Joi.valid('photographer'), then: Joi.required(), otherwise: Joi.forbidden() }),
        arabic_level: Joi.string().max(100)
            .when(Joi.ref(position), { is: Joi.valid('photographer'), then: Joi.required(), otherwise: Joi.forbidden() }),
        details: Joi.object()
            .when(Joi.ref(position), { is: Joi.valid('photographer'), then: Joi.required(), otherwise: Joi.forbidden() }),
        is_internal: Joi.boolean()
            .when(Joi.ref(position), { is: Joi.valid('photographer'), then: Joi.required(), otherwise: Joi.forbidden() }),
        registration_guid: Joi.string().allow(null)
            .when(Joi.ref(position), { is: Joi.valid('client'), then: Joi.required(), otherwise: Joi.forbidden() }),
        parent_client_id: Joi.number().integer().allow(null)
            .when(Joi.ref(position), { is: Joi.valid('client'), then: Joi.required(), otherwise: Joi.forbidden() }),
        has_company_name: Joi.boolean().optional().allow(null),
        auth_provider_reference: Joi.string().max(200).optional().allow(null),
        plan: Joi.object().optional().allow(null),
        billing: Joi.object().optional().allow(null),
        sub_client_role: Joi.string().optional().allow(null)
    };
};

const softUser = user.keys({
    ...({ ...additionalInformations('$payload.requestedUserRole') })
});

const softClient = user.keys({
    company_name: Joi.string().optional().allow(null),
    industry: Joi.string().optional().allow(null),
    contact_name: Joi.string().optional().allow(null),
    contact_email: Joi.string().optional().allow(null).max(320),
    contact_phone: Joi.string().optional().allow(null).max(20),
    main_location: Joi.string().optional().allow(null),
    country: Joi.string().optional().allow(null).max(2),
    registration_guid: Joi.string().optional().allow(null),
    parent_client_id: Joi.number().integer().allow(null),
    has_company_name: Joi.boolean().optional().allow(null),
    auth_provider_reference: Joi.string().max(200).optional().allow(null)
});

const userDetail = user.keys({
    ...({ ...additionalInformations('$payload.filters.role') }),
    is_pro: Joi.boolean()
        .when(Joi.ref('$payload.filters.role'), { is: Joi.valid('photographer'), then: Joi.required(), otherwise: Joi.forbidden() }),
    is_internal: Joi.boolean()
        .when(Joi.ref('$payload.filters.role'), { is: Joi.valid('photographer'), then: Joi.required(), otherwise: Joi.forbidden() }),
    services: Joi.array().items(
        Joi.object({
            service_id: Joi.number().integer().required(),
            rate: Joi.number().integer().min(0).required()
        })).when(Joi.ref('$payload.filters.role'), { is: Joi.valid('photographer'), then: Joi.required(), otherwise: Joi.forbidden() }),
    shoot_number: Joi.number().integer().min(0).required()
        .when(Joi.ref('$payload.filters.role'), { is: Joi.valid('admin'), then: Joi.forbidden(), otherwise: Joi.required() }),
    shoot_total_price_or_revenue: Joi.number().precision(2).min(0).required()
        .when(Joi.ref('$payload.filters.role'), { is: Joi.valid('admin'), then: Joi.forbidden(), otherwise: Joi.required() }),
    subclients: Joi.array().optional()
});

const search = Joi.object({
    users: Joi.array().items(userDetail).required(),
    total: Joi.number().integer().required(),
    max_shoot_number: Joi.number().integer(),
    max_cost_or_revenue: Joi.number().precision(2)
});

const checker = Joi.boolean().required();

module.exports = {
    user,
    userDetail,
    search,
    softUser,
    checker,
    softClient
};
