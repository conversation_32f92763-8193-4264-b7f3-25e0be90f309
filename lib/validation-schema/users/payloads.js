'use strict';

const Joi = require('joi');

const search = Joi.object({
    filters: Joi.object({
        role: Joi.string()
            .valid('client', 'subClient')
            .when(Joi.ref('$auth.credentials.tokenPayload.role'), {
                is: Joi.valid('admin'),
                then: Joi.valid('client', 'photographer')
            }),
        services: Joi.when(Joi.ref('role'), {
            is: Joi.valid('photographer'),
            then: Joi.array().items(Joi.number().integer()),
            otherwise: Joi.forbidden()
        }),
        statuses: Joi.array().items(Joi.string()),
        locations: Joi.array().items(Joi.string()),
        contentType: Joi.array().items(Joi.string()),
        searchQuery: Joi.string()
            .regex(/^[a-zA-Z0-9 ]*$/)
            .allow('')
            .optional(),
        revenueRange: Joi.object({
            min: Joi.number().integer().min(0),
            max: Joi.number().integer().min(Joi.ref('min'))
        }).optional()
    }),
    fetchAll: Joi.boolean().default(false), // Added fetchAll as an optional boolean
    pagination: Joi.object({
        offset: Joi.number().integer().min(0),
        size: Joi.number().integer().min(0)
    })
        .when('fetchAll', {
            is: true,
            then: Joi.forbidden() // Disable pagination if fetchAll is true
        })
});

const user = Joi.object({
    name: Joi.string().required(),
    email: Joi.string().max(320).required(),
    phone: Joi.string().max(20).optional().allow('')
}).required();

const additionalInformations = (position) => {

    const additionalInformations = Joi.object({
        company_name: Joi.string().allow(null)
            .when(Joi.ref(position), { is: Joi.valid('client'), then: Joi.optional().allow(null), otherwise: Joi.forbidden() }),
        industry: Joi.string().optional().allow(null)
            .when(Joi.ref(position), { is: Joi.valid('client'), then: Joi.optional().allow(null), otherwise: Joi.forbidden() }),
        contact_name: Joi.string().allow('', null)
            .when(Joi.ref(position), { is: Joi.valid('client'), then: Joi.optional().allow(null), otherwise: Joi.forbidden() }),
        contact_email: Joi.string().optional().max(320).allow('', null)
            .when(Joi.ref(position), { is: Joi.valid('client'), then: Joi.optional().allow(null), otherwise: Joi.forbidden() }),
        contact_phone: Joi.string().optional().max(20).allow('', null)
            .when(Joi.ref(position), { is: Joi.valid('client'), then: Joi.optional().allow(null), otherwise: Joi.forbidden() }),
        main_location: Joi.string().optional().default('N/A'),
        country: Joi.string().max(2).default('SA')
            .when(Joi.ref(position), { is: Joi.valid('client'), then: Joi.optional(), otherwise: Joi.forbidden() }),
        birthdate: Joi.date()
            .when(Joi.ref(position), { is: Joi.valid('photographer'), then: Joi.optional(), otherwise: Joi.forbidden() }),
        english_level: Joi.string().max(100)
            .when(Joi.ref(position), { is: Joi.valid('photographer'), then: Joi.optional(), otherwise: Joi.forbidden() }),
        arabic_level: Joi.string().max(100)
            .when(Joi.ref(position), { is: Joi.valid('photographer'), then: Joi.optional(), otherwise: Joi.forbidden() }),
        details: Joi.object()
            .when(Joi.ref(position), { is: Joi.valid('photographer'), then: Joi.optional(), otherwise: Joi.forbidden() })
    }).required();
    return additionalInformations;
};

const createUser = Joi.object({
    password: Joi.string().required(),
    additionalInformations: additionalInformations('$params.role'),
    user,
    paymentDetail: Joi.object({
        type: Joi.string().max(255).required().valid('test').default('test'),
        details: Joi.object({}).default({})  //TODO remove the default, add correct fields
    }).optional()
});
const requireWhenRole = (originalScheme, affectedUserField, affectedUserRole) => {

    return originalScheme.when(
        Joi.ref(affectedUserField), {
            is: Joi.valid(affectedUserRole),
            then: Joi.when(
                Joi.ref('$auth.credentials.tokenPayload.role'), {
                    is: 'admin',
                    then: Joi.required(),
                    otherwise: Joi.forbidden()
                }
            )
        }
    );
};

const updateUser = Joi.object({
    role: Joi.string().valid('photographer', 'client').required(),
    additionalInformations: additionalInformations('...role').keys({
        is_pro: requireWhenRole(Joi.boolean(), '...role', 'photographer'),
        is_internal: requireWhenRole(Joi.boolean(), '...role', 'photographer')
    }).when(Joi.ref('role'), { is: Joi.valid('admin'), then: Joi.forbidden(), otherwise: Joi.required() }),
    status: Joi.string().max(10)
        .when(Joi.ref('$auth.credentials.tokenPayload.role'), { is: Joi.valid('photographer'), then: Joi.forbidden() }),
    services: requireWhenRole(Joi.array().allow(null).items(
        Joi.object({
            service_id: Joi.number().integer().required(),
            rate: Joi.number().integer().min(1).max(10)
        })
    ), 'role', 'photographer'),
    user: user.keys({
        name: Joi.optional(),
        email: Joi.forbidden()
    })
});
module.exports = {
    createUser,
    updateUser,
    search
};
