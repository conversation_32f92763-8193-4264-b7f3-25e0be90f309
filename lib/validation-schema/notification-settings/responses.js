'use strict';

const Joi = require('joi');

const setting = Joi.object({
    id: Joi.number().required(),
    parent_id: Joi.number().allow(null),
    key: Joi.string(),
    description: Joi.string(),
    value: Joi.boolean(),
    user_type: Joi.string(),
    children: Joi.array()
});

const settings = Joi.object({
    client: Joi.array().items( setting ),
    admin: Joi.array().items( setting ),
    photographer: Jo<PERSON>.array().items( setting )
});

module.exports = {
    settings
};
