'use strict';

const Joi = require('joi');

const search = Joi.object(
    {
        filters: Joi.object({
            date: Joi.object({
                from: Joi.date(),
                to: Joi.date().when('from', { is: Joi.exist(), then: Joi.date().min(Joi.ref('from')) })
            }),
            cities: Joi.array().items(Joi.string()),
            services: Joi.array().items(Joi.number().integer()),
            statuses: Joi.array().items(Joi.string()),
            searchQuery: Joi.string().regex(/^[a-zA-Z0-9 ]*$/).allow('').optional()
        }).default({}),
        pagination: Joi.object({
            offset: Joi.number().integer().min(0).default(0),
            size: Joi.number().integer().min(0).default(10)
        }).default({ offset: 0, size: 10 })
    }
);

const requireWhenRole = (role, originalScheme) => {

    return originalScheme.when(Joi.ref('$auth.credentials.tokenPayload.role'), {
        is: Joi.valid(role),
        then: Joi.required(),
        otherwise: Joi.forbidden()
    });
};

const customPackageScheme = Joi.object({
    service_id: Joi.number().integer().required(),
    picture_number: Joi.number().integer().min(0).required(),
    video_number: Joi.number().integer().min(0).required(),
    video_duration: Joi.number().integer().min(0).required()
});

const standardPackageScheme = Joi.object({
    package_id: Joi.number().integer().required()
});

const createBase = Joi.object({
    location: Joi.object({
        city: Joi.string().optional(),
        country: Joi.string(),
        line1: Joi.string(),
        postal_code: Joi.string(),
        state: Joi.string().required(),
        formatted_address: Joi.string().required(),
        utc_offset_minutes: Joi.number().integer()
    }),
    notes: Joi.string(),
    name: Joi.string().max(255).allow(null),
    outlet_code: Joi.string().allow(null),
    outlet_name: Joi.string().allow(null),
    poc_email: Joi.string().allow(null),
    poc_phone: Joi.string().allow(null),
    poc_name: Joi.string().allow(null),
    time: Joi.object({
        from: Joi.date().when(Joi.ref('$auth.credentials.tokenPayload.parent_client_id'), {
            is: Joi.any().valid(null, ''),
            then: Joi.optional(),
            otherwise: Joi.required()
        }),
        duration: Joi.number().integer().required()
    }).default({})
});

const updateFull =  createBase.keys(
    {
        packages: Joi.array().items(
            requireWhenRole('client', standardPackageScheme),
            requireWhenRole('admin', Joi.alternatives().try(customPackageScheme, standardPackageScheme))
        ).required(),
        total_price: requireWhenRole('admin', Joi.number().precision(2).min(0)
            .when('type', { is: Joi.valid('custom'), then: Joi.required(), otherwise: Joi.forbidden() })),
        total_revenue: requireWhenRole('admin', Joi.number().precision(2).min(0).less(Joi.ref('total_price'))
            .when('type', { is: Joi.valid('custom'), then: Joi.required(), otherwise: Joi.forbidden() })),
        photographer_id: requireWhenRole('admin', Joi.number().integer().required().allow(null)
            .when('time', { is: Joi.exist(), then: Joi.any(), otherwise: Joi.forbidden() })),
        content: Joi.allow(null)
            .when(Joi.ref('$auth.credentials.tokenPayload.role'), {
                is: Joi.valid('admin', 'client'), then: Joi.string().valid('photography', 'videography')
            })
    }
);

const createFull = updateFull.keys(
    {
        client_id: requireWhenRole('admin', Joi.number().integer().required()),
        photographer_id: requireWhenRole('admin', Joi.number().integer().required().allow(null)
            .when('time', { is: Joi.exist(), then: Joi.any(), otherwise: Joi.forbidden() })),
        is_payed: requireWhenRole('admin', Joi.boolean()),
        redeemable_total: Joi.allow(null)
            .when(Joi.ref('$auth.credentials.tokenPayload.role'), {
                is: Joi.valid('client'), then: Joi.number().integer().min(1), otherwise: Joi.number().integer().min(-1)
            }),
        contact_phone: Joi.string().max(255),
        contact_name: Joi.string().max(255),
        type: Joi.allow(null)
            .when(Joi.ref('$auth.credentials.tokenPayload.role'), {
                is: Joi.valid('admin'), then: Joi.string().valid('custom', 'express'), otherwise: Joi.string().valid('express')
            }),
        content: Joi.allow(null)
            .when(Joi.ref('$auth.credentials.tokenPayload.role'), {
                is: Joi.valid('admin', 'client'), then: Joi.string().valid('photography', 'videography')
            })
    }
);
const createFromRedeemable = createBase.keys({
    redeemable_shoot_id: Joi.number().integer().required()
});

const create = Joi.when(Joi.ref('$auth.credentials.tokenPayload.parent_client_id'), {
    is: Joi.any().valid(null, ''),
    then: Joi.alternatives().try(createFull, createFromRedeemable),
    otherwise: Joi.alternatives().try(createFromRedeemable)
});

module.exports = {
    updateFull,
    create,
    search
};

//TBD how to extend the method
module.exports.schedule = Joi.object(
    {
        datetime: Joi.date(),
        notes: Joi.string()
    }
);
