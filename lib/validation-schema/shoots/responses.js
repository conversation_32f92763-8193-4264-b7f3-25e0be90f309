'use strict';

const Joi = require('joi');

const image = Joi.object({
    id: Joi.number().integer(),
    shoot_id: Joi.number().integer().optional().allow(null),
    url: Joi.string().max(255).required(),
    name: Joi.string().max(2048).optional().allow(null),
    placeholder_url: Joi.string().max(255).required(),
    preview_url: Joi.string().max(255).required(),
    stack_id: Joi.number().integer().optional().allow(null),
    filename: Joi.string().max(255).required(),
    folder: Joi.string().max(255).required(),
    filepath: Joi.string().max(255).required(),
    status: Joi.string().max(20).required().default('pending'),
    feedback: Joi.string().allow(null).empty(),
    aspect_ratio: Joi.number().allow(null).empty()
});

const video = Joi.object({
    id: Joi.number().integer(),
    shoot_id: Joi.number().integer().optional().allow(null),
    url: Joi.string().max(255).required(),
    name: Joi.string().max(2048).optional().allow(null),
    placeholder_url: Joi.string().max(255).required(),
    preview_url: Joi.string().max(255).required(),
    filename: Joi.string().max(255).required(),
    folder: Joi.string().max(255).required(),
    filepath: Joi.string().max(255).required(),
    status: Joi.string().max(20).required().default('pending'),
    feedback: Joi.string().allow(null).empty(),
    aspect_ratio: Joi.number().allow(null).empty()
});

const rawImage = Joi.object({
    id: Joi.number().integer(),
    shoot_id: Joi.number().integer().optional().allow(null),
    url: Joi.string().max(255).required(),
    placeholder_url: Joi.string().max(255).required(),
    preview_url: Joi.string().max(255).required(),
    filename: Joi.string().max(255).required(),
    name: Joi.string().max(1024).optional().allow(null),
    folder: Joi.string().max(255).required(),
    filepath: Joi.string().max(255).required(),
    status: Joi.string().max(20).required().default('pending'),
    notes: Joi.string().allow(null).allow(''),
    aspect_ratio: Joi.number().allow(null).empty()
});

const shoot =  Joi.object(
    {
        id: Joi.number().integer(),
        photographer_id: Joi.number().integer().allow(null),
        order_id: Joi.number().integer().required(),
        datetime: Joi.date().allow(null),
        address: Joi.object().allow(null),
        latitude: Joi.number().precision(6).allow(null),
        longitude: Joi.number().precision(6).allow(null),
        notes: Joi.string().allow(null),
        contact_phone: Joi.string().max(255).allow(null),
        contact_name: Joi.string().max(255).allow(null),
        picture_number: Joi.number().integer().min(0).required(),
        video_number: Joi.number().integer().min(0).required().allow(null),
        video_duration: Joi.number().integer().min(0).required().allow(null),
        price: Joi.number().precision(2).min(0).required(),
        photographer_revenue: Joi.number().precision(2).min(0).required(),
        duration: Joi.number().integer().min(0).required(),
        scheduled: Joi.boolean().required(),
        // Changed something
        images_preview: Joi.boolean().default(false),
        raws_preview: Joi.boolean().default(false),
        old: Joi.boolean().default(false),
        photographer_name: Joi.string().allow(null).required(),
        customer_name: Joi.string().required(),
        services: Joi.array().items(
            Joi.object({
                service_id: Joi.number().integer().allow(null),
                picture_number: Joi.number().integer().min(0).required(),
                package: Joi.object({
                    name: Joi.string().required().default('custom'),
                    id: Joi.number().integer()
                }).required()
            })).required(),
        is_brief_uploaded: Joi.boolean().required(),
        status: Joi.string().max(10).required(),
        redeemable_counter: Joi.number().integer().allow(null),
        redeemable_total: Joi.number().integer().allow(null),
        redeemable_shoot_id: Joi.number().integer().allow(null),
        outlet_code: Joi.string().allow(null),
        outlet_name: Joi.string().allow(null),
        poc_email: Joi.string().allow(null),
        poc_phone: Joi.string().allow(null),
        poc_name: Joi.string().allow(null),
        consumer_client_id: Joi.number().integer().allow(null),
        last_edit_by: Joi.number().integer().allow(null),
        creation_date: Joi.date().required(),
        last_update: Joi.date().required(),
        name: Joi.string().max(255).allow(null),
        type: Joi.string().valid('custom', 'express').allow(null),
        content: Joi.string().valid('photography', 'videography').allow(null),
        client_id: Joi.number().integer().allow(null),
        service_id: Joi.number().integer().allow(null),
        migration_status: Joi.string().max(20).optional().default('pending')
    }
);

const shootDetail = shoot.keys({
    services: Joi.array().items(
        Joi.object({
            service_id: Joi.number().integer().allow(null),
            picture_number: Joi.number().integer().min(0).required(),
            package: Joi.object().required()
        })).required()
});

module.exports = {
    shoot,
    shootDetail,
    image,
    rawImage,
    video
};
