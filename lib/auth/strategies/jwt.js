'use strict';

const { generateRandString } = require('../../helpers/utils');

module.exports = (server, options) => ({
    scheme: 'jwt',
    options: {
        key: server.settings.app.auth.jwt.secret || generateRandString(32),
        urlKey: false,
        cookieKey: false,
        tokenType: 'Bearer',
        verifyOptions: {
            algorithms: [server.settings.app.auth.jwt.algorithm],
            issuer: server.settings.app.auth.jwt.issuer
        },
        validate: async (decoded, request, h) => {

            const { userService } = request.services();

            if (!decoded) {
                return { isValid: false };
            }

            const user = await userService.getUserByEmail({ email: decoded.email });

            if (!user || user.status !== 'active') {
                return { isValid: false };
            }

            if (user.role === 'client') {
                // const activeLogin = await authService.getSessionByUserId(user.id);
                //
                // if (activeLogin !== null && activeLogin !== decoded.sid) {
                //     return { isValid: false };
                // }

                const client = await userService.getFormattedUser({ id: user.id, role: user.role });
                user.company_name = client.company_name;
                user.parent_client_id = client.parent_client_id;
                // Check if the client is not a subclient and its company name does not indicate a B2C relationship
                if (!client.parent_client_id && (client.company_name && !client.company_name.endsWith('B2C'))) {
                    user.subClientRole = 'admin';
                }

                if (client.parent_client_id && (client.company_name && !client.company_name.endsWith('B2C'))) {
                    const authUser = await userService.getAuth0UserByEmail({ email: client.email });
                    user.subClientRole = authUser.user_metadata.subClientRole;
                }
            }
            else if (user.role === 'photographer') {
                const photographer = await userService.getFormattedUser({ id: user.id, role: user.role });
                user.is_internal = photographer.is_internal;
            }

            return {
                isValid: true,
                credentials: { tokenPayload: decoded, user }
            };
        }
    }
});
