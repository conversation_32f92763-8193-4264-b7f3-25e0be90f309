'use strict';

const Joi = require('joi');
const Hoek = require('@hapi/hoek');
const Bell = require('@hapi/bell');
const { generateRandString } = require('../../helpers/utils');

// Taken from internals.schema of index.js of hapi bell (https://github.com/hapijs/bell)
const settingsSchema = Joi.object({

    provider: Joi.object({
        name: Joi.string().optional().default('custom'),
        protocol: 'oauth2',
        auth: Joi.string().required(),
        token: Joi.string().required(),
        profile: Joi.func(),
        profileMethod: Joi.valid('get', 'post').default('get'),
        scope: Joi.array().items((Joi.string())),
        scopeSeparator: Joi.string(),
        useParamsAuth: Joi.boolean().default(false)
    }).required(),
    password: Joi.string().required().min(32),
    clientId: Joi.string().required(),
    clientSecret: Joi.string().required(),
    cookie: Joi.string().required(),
    isSameSite: Joi.valid('Strict', 'Lax').allow(false).default('Strict'),
    isSecure: Joi.boolean().optional().default(true),
    isHttpOnly: Joi.boolean().optional().default(true),
    ttl: Joi.number().min(300000).max(86400000), // min 5 minutes max 24 hours in ms
    name: Joi.string().required(),
    config: Joi.object(),
    forceHttps: Joi.boolean().optional().default(true),
    allowRuntimeProviderParams: Joi.boolean().default(true) // This lets arbitrary query params (such as __flashy_data) flow towards the auth provider
});

module.exports = {
    name: 'oauth2',
    scheme: (server, options) => {

        let settings = Hoek.clone(options, { shallow: 'provider' });

        // Setup cookie for managing temporary authorization state
        const cookieOptions = {
            encoding: 'iron',
            path: '/',
            isSameSite: options.isSameSite,
            password: generateRandString(32),
            ignoreErrors: true,
            clearInvalid: true,
            ...server.settings.app.cookies.options
        };

        settings.password = settings.password || generateRandString(32);

        settings.cookie = server.settings.app.cookies.login;

        server.state(server.settings.app.cookies.login, cookieOptions);
        server.state(server.settings.app.cookies.redirectUri, cookieOptions);

        settings.provider = server.settings.app.auth.provider.name;
        settings.clientId = server.settings.app.auth.provider.client_id;
        settings.clientSecret = server.settings.app.auth.provider.client_secret;
        settings.config = server.settings.app.auth.provider.config;
        settings.forceHttps = server.settings.app.auth.forceHttps;

        settings.name = settings.provider;
        settings.provider = Bell.providers[settings.provider].call(null, settings.config);
        const settingsValidation = settingsSchema.validate(settings);

        if (settingsValidation.error) {
            throw new Error(settingsValidation.error);
        }

        settings = settingsValidation.value;

        return {
            authenticate: async (request, h) => {

                if (settings.name === 'auth0') {
                    // Install a custom profile callback for the auth0 provider (to fetch also metadata)
                    settings.provider.profile = async function (credentials, params, get) {

                        const { userService, jwtService } = request.services();
                        const token = await jwtService.decodeToken({ token: params.id_token });
                        const profile = await userService.getAuth0UserById({ id: token.decoded.payload.sub });

                        credentials.profile = {
                            id: profile.sub,
                            email: profile.email,
                            displayName: profile.displayName,
                            email_verified: profile.email_verified
                        };
                        credentials.app_metadata = profile.app_metadata;
                        credentials.user_metadata = profile.user_metadata;
                    };
                }

                if (request.query.__redirect_uri) {
                    h.state(server.settings.app.cookies.redirectUri, request.query.__redirect_uri);
                }

                return await server.plugins.bell.oauth.v2(settings)(request, h);
            }
        };
    }
};
