'use strict';

const { UnauthenticatedError } = require('../../helpers/errors');
const { createHash } = require('crypto');

module.exports = {
    scheme: (server, options) => {

        const { admin } = server.settings.app.auth.apiKeys;

        const validAdminAPIKeys = new Set(admin);
        const adminAPIKeyHashes = Object.fromEntries(admin.map((apiKey) => [
            apiKey,
            createHash('sha256').update(apiKey).digest('base64')
        ]));

        return {
            authenticate: (request, h) => {

                const apiKey = request.headers.authorization && request.headers.authorization.replace('Bearer ', '');
                if (apiKey && validAdminAPIKeys.has(apiKey)) {
                    return  h.authenticated({
                        credentials: {
                            tokenPayload: {
                                role: 'admin'
                            },
                            // TODO: replace with a proper user fetched from the db, once we upgrade the
                            // API KEY management system to properly support it
                            user: {},
                            apiKeyHash: adminAPIKeyHashes[apiKey]
                        }
                    });
                }

                return h.unauthenticated(new UnauthenticatedError('Invalid API KEY'));
            }
        };
    }
};
