/* eslint-disable @hapi/scope-start */
'use strict';

const { ChatOpenAI } = require('@langchain/openai');
const { OpenAIEmbeddings } = require('@langchain/openai');
const { PineconeStore } = require('@langchain/pinecone');
const { Pinecone } = require('@pinecone-database/pinecone');
const { StringOutputParser } = require('@langchain/core/output_parsers');
const { ChatPromptTemplate, MessagesPlaceholder } = require('@langchain/core/prompts');
const { RunnableSequence, RunnablePassthrough } = require('@langchain/core/runnables');
const Schmervice = require('schmervice');
const { HumanMessage, AIMessage, SystemMessage } = require('@langchain/core/messages');

/**
 * @class AssistantService
 * @extends Schmervice.Service
 * @description Service for handling AI assistant functionalities
 */
module.exports = class AssistantService extends Schmervice.Service {
    /**
     * @constructor
     * @param {Object} server - The server instance
     * @param {Object} options - Configuration options
     */
    constructor(server, options) {
        super(server, options);

        // Initialize Pinecone client
        this.pinecone = new Pinecone({
            apiKey: this.server.settings.app.pinecone.api_key
        });

        // Initialize OpenAI chat model
        this.openai = new ChatOpenAI({
            temperature: 0.25,
            modelName: 'gpt-4-turbo-preview',
            apiKey: this.server.settings.app.openai.api_key
        });

        // Initialize OpenAI embeddings
        this.embeddings = new OpenAIEmbeddings({
            openAIApiKey: this.server.settings.app.openai.api_key,
            modelName: 'text-embedding-3-small'
        });
    }

    /**
     * @async
     * @method initialize
     * @description Initializes the AssistantService, setting up the vector store and chain
     */
    async initialize() {
        try {
            const { conversationsService } = this.server.services();
            this.conversationsService = conversationsService;

            // Step 1: Set up Pinecone vector store
            const pineconeIndex = this.pinecone.Index('invoices-store');
            this.vectorStore = await PineconeStore.fromExistingIndex(
                this.embeddings,
                { pineconeIndex }
            );

            // Step 3: Set up question contextualization
            const contextualizeQSystemPrompt = `Given a chat history and the latest user question
            which might reference context in the chat history, formulate a standalone question
            which can be understood without the chat history. Do NOT answer the question,
            just reformulate it if needed and otherwise return it as is.`;

            const contextualizeQPrompt = ChatPromptTemplate.fromMessages([
                ['system', contextualizeQSystemPrompt],
                new MessagesPlaceholder('chat_history'),
                ['human', '{question}']
            ]);

            const contextualizeQChain = contextualizeQPrompt
                .pipe(this.openai)
                .pipe(new StringOutputParser());

            // Step 4: Set up QA prompt
            const qaSystemPrompt = `You are an expert assistant for answering questions about invoices, rates, and services.
            Use the following pieces of retrieved context to answer the question accurately and concisely.
            If you don't have enough information to answer confidently, say so.
            Always provide specific details from the context when available, such as exact rates, service descriptions, and dates.
            
            Context: {context}
            
            Remember to use only the provided context to inform your answer. Do not make up any information.`;

            const qaPrompt = ChatPromptTemplate.fromMessages([
                ['system', qaSystemPrompt],
                new MessagesPlaceholder('chat_history'),
                ['human', '{question}']
            ]);

            // Step 5: Define question contextualization function
            const contextualizedQuestion = async (input) => {
                if (input.chat_history && input.chat_history.length > 0) {
                    return await contextualizeQChain.invoke(input);
                }

                return input.question;
            };

            // In the initialize method, modify the chain setup:
            this.chain = RunnableSequence.from([
                RunnablePassthrough.assign({
                    chat_history: async (input) => {
                        if (!input.conversationId || !input.userId) {
                            throw new Error('Both conversationId and userId must be provided');
                        }

                        const history = await this.conversationsService.getConversationHistory(input.conversationId, input.userId);
                        return history.map((msg) => {
                            switch (msg.sender) {
                                case 'human':
                                    return new HumanMessage(msg.content);
                                case 'assistant':
                                    return new AIMessage(msg.content);
                                default:
                                    return new SystemMessage(msg.content);
                            }
                        });
                    },
                    context: async (input) => {
                        try {
                            const query = await contextualizedQuestion(input);
                            const docs = await this.vectorStore.asRetriever({ k: 20 }).invoke(query);

                            // Separate full_text for LLM processing and keep all metadata
                            const context = docs.map((doc) => doc.metadata.full_text).join('\n\n--- Next Document ---\n\n');
                            const fullContext = docs.map((doc) => doc.metadata);

                            return { context, fullContext };
                        }
                        catch (error) {
                            return { context: '', fullContext: [] };
                        }
                    }
                }),
                RunnablePassthrough.assign({
                    answer: ({ question, chat_history, context }) =>
                        qaPrompt.pipe(this.openai).pipe(new StringOutputParser())
                            .invoke({ question, chat_history, context: context.context })
                }),
                ({ answer, context }) => ({ answer, context: context.fullContext })
            ]);

        }
        catch (error) {
            throw new Error(error);
        }
    }

    async query(question, conversationId, userId) {
        if (!this.chain) {
            throw new Error('AssistantService not initialized. Call initialize() first.');
        }

        try {
            // Step 1: Add the user's question to the conversation history
            await this.conversationsService.addMessage(conversationId, question, 'human');

            // Step 2: Set up the chain with listeners for debugging
            const chainWithContext = this.chain.withListeners({});

            // Step 3: Invoke the chain to get the streaming response
            const stream = await chainWithContext.stream({
                question,
                conversationId,
                userId
            });

            let fullAnswer = '';
            let context = null;
            let lastUpdateTime = Date.now();
            const updateInterval = 2000; // Update every 2 seconds

            // Create an initial empty message for the assistant's response
            const initialMessage = await this.conversationsService.addMessage(conversationId, '', 'assistant');

            // Step 4: Set up an async generator to yield chunks and collect the full answer and context
            const streamGenerator = async function* () {
                for await (const chunk of stream) {
                    if (chunk.answer) {
                        fullAnswer += chunk.answer;
                        yield { type: 'answer', data: chunk.answer };

                        // Update the message with the current full answer periodically
                        if (Date.now() - lastUpdateTime > updateInterval) {
                            await this.conversationsService.updateMessage(conversationId, initialMessage.id, fullAnswer);
                            lastUpdateTime = Date.now();
                        }
                    }

                    if (chunk.context) {
                        // eslint-disable-next-line prefer-destructuring
                        context = chunk.context;
                        yield { type: 'context', data: context };
                    }
                }
            }.bind(this);

            // Step 5: Process the stream and ensure the final answer is added to the conversation history
            const { stream: resultStream, answer, context: finalContext } = await this.processStream(streamGenerator());

            // Update the message with the final answer
            await this.conversationsService.updateMessage(conversationId, initialMessage.id, answer);

            // Step 6: Return the stream, full answer, conversation ID, and context
            return {
                stream: resultStream,
                answer,
                conversationId,
                context: finalContext
            };
        }
        catch (error) {
            throw new Error(error);
        }
    }

    // Helper method to process the stream
    async processStream(generator) {
        let fullAnswer = '';
        let context = null;
        const chunks = [];

        for await (const chunk of generator) {
            chunks.push(chunk);
            if (chunk.type === 'answer') {
                fullAnswer += chunk.data;
            }
            else if (chunk.type === 'context') {
                context = chunk.data;
            }
        }

        return {
            stream: (async function* () {
                yield* chunks;
            })(),
            answer: fullAnswer,
            context
        };
    }
};
