/* eslint-disable @hapi/scope-start */
'use strict';

const Schmervice = require('schmervice');

/**
 * ConversationsService class for handling conversation-related operations.
 * @extends Schmervice.Service
 */
module.exports = class ConversationsService extends Schmervice.Service {
    /**
     * Create a ConversationsService.
     * @param {Object} server - The server instance.
     * @param {Object} options - Configuration options.
     */
    constructor(server, options) {
        super(server, options);
    }

    /**
     * Create a new conversation for a user.
     * @param {number} userId - The ID of the user creating the conversation.
     * @param {Object} [metadata={}] - Additional metadata for the conversation.
     * @returns {Promise<Object>} The created conversation object.
     */
    async createConversation(userId, metadata = {}) {
        const { Conversation } = this.server.models();
        return await Conversation.query().insert({
            user_id: userId,
            metadata
        });
    }

    /**
     * Delete a conversation.
     * @param {number} conversationId - The ID of the conversation to delete.
     * @param {number} userId - The ID of the user who owns the conversation.
     * @returns {Promise<Object>} An object indicating success or failure.
     * @throws {Error} If the conversation is not found or the user is not authorized.
     */
    async deleteConversation(conversationId, userId) {
        const { Conversation } = this.server.models();
        const deletedCount = await Conversation.query()
            .delete()
            .where('id', conversationId)
            .where('user_id', userId);
        if (deletedCount === 0) {
            throw new Error('Conversation not found or user not authorized');
        }

        return { success: true, message: 'Conversation deleted successfully' };
    }

    /**
     * Add a message to a conversation.
     * @param {number} conversationId - The ID of the conversation.
     * @param {string} content - The content of the message.
     * @param {string} sender - The sender of the message.
     * @returns {Promise<Object>} The created message object.
     */
    async addMessage(conversationId, content, sender) {
        const { Message } = this.server.models();
        return await Message.query().insert({
            conversation_id: conversationId,
            content,
            sender
        });
    }

    /**
     * Get the conversation history for a specific conversation.
     * @param {number} conversationId - The ID of the conversation.
     * @param {number} userId - The ID of the user requesting the history.
     * @param {number} [limit=20] - The maximum number of messages to retrieve.
     * @returns {Promise<Array>} An array of message objects.
     * @throws {Error} If the conversation is not found or the user is not authorized.
     */
    async getConversationHistory(conversationId, userId, limit = 100) {
        const { Message, Conversation } = this.server.models();

        if (conversationId === undefined || userId === undefined) {
            throw new Error('conversationId and userId must be provided');
        }

        const conversation = await Conversation.query()
            .where('id', conversationId)
            .where('user_id', userId)
            .first();
        if (!conversation) {
            throw new Error('Conversation not found or user not authorized');
        }

        return await Message.query()
            .where('conversation_id', conversationId)
            .orderBy('created_at', 'desc')
            .limit(limit)
            .then((messages) => messages.reverse());
    }

    /**
     * Get all conversations for a user.
     * @param {number} userId - The ID of the user.
     * @returns {Promise<Array>} An array of conversation objects with titles.
     */
    async getUserConversations(userId) {
        const { Conversation, Message } = this.server.models();

        if (!Conversation || !Message) {
            throw new Error('Required models not available');
        }

        const conversations = await Conversation.query()
            .where('user_id', userId)
            .orderBy('updated_at', 'desc');

        const conversationsWithTitle = await Promise.all(conversations.map(async (conv) => {
            const firstMessage = await Message.query()
                .where('conversation_id', conv.id)
                .orderBy('created_at', 'asc')
                .first();

            return {
                id: conv.id,
                title: conv.metadata.title ?  conv.metadata.title : firstMessage.content.substring(0, 50),
                created_at: conv.created_at,
                updated_at: conv.updated_at
            };
        }));

        return conversationsWithTitle;
    }

    /**
 * Update an existing message in a conversation.
 * @param {number} conversationId - The ID of the conversation.
 * @param {number} messageId - The ID of the message to update.
 * @param {string} content - The new content of the message.
 * @returns {Promise<Object>} The updated message object.
 */
    async updateMessage(conversationId, messageId, content) {
        const { Message } = this.server.models();
        return await Message.query()
            .patch({ content })
            .where('id', messageId)
            .where('conversation_id', conversationId)
            .returning('*')
            .first();
    }
};
