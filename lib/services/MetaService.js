/* eslint-disable @hapi/scope-start */
'use strict';

const Schmervice = require('schmervice');
const Axios = require('axios');
const { ValidationError } = require('../helpers/errors');

/**
 * Service for Meta Business Platform API interactions
 * @class MetaService
 * @extends Schmervice.Service
 */
module.exports = class MetaService extends Schmervice.Service {
    constructor(server, options) {

        super(server, options);

        this.clientId = this.server.settings.app.facebook.client_id;
        this.clientSecret = this.server.settings.app.facebook.client_secret;
        this.graphApiVersion = 'v21.0';
        this.instagramGraphApiVersion = 'v22.0';
        this.baseUrl = 'https://graph.facebook.com';
        this.requiredPermissions = [
            'instagram_basic',
            'instagram_content_publish',
            'pages_show_list',
            'pages_read_engagement',
            'pages_manage_posts',
            'business_management',
            'public_profile'
        ];
    }

    /**
   * Get app access token for API calls that require app authentication
   * @private
   * @returns {Promise<string>} App access token
   */
    async _getAppAccessToken() {

        try {
            const response = await Axios.get(`${this.baseUrl}/oauth/access_token`, {
                params: {
                    client_id: this.clientId,
                    client_secret: this.clientSecret,
                    grant_type: 'client_credentials'
                }
            });

            return response.data.access_token;
        }
        catch (error) {
            this.server.logger.error('Failed to get app access token:', error);
            throw new ValidationError('Failed to get app authentication');
        }
    }

    /**
 * Get access token for a specific page
 * @private
 * @param {string} pageId - ID of the Facebook page
 * @param {string} userAccessToken - User access token with required permissions
 * @returns {Promise<string>} Page access token
 */
    async _getPageAccessToken(pageId, userAccessToken) {

        try {
            const response = await Axios.get(
                `${this.baseUrl}/${this.graphApiVersion}/${pageId}`,
                {
                    params: {
                        access_token: userAccessToken,
                        fields: 'access_token'
                    }
                }
            );

            if (!response.data.access_token) {
                throw new ValidationError(`No access token found for page ${pageId}`);
            }

            return response.data.access_token;
        }
        catch (error) {
            this.server.logger.error(`Failed to get access token for page ${pageId}:`, error);
            throw new ValidationError(`Failed to get page access token for page ${pageId}`);
        }
    }

    /**
     * Exchange authorization code for access token
     * @param {string} code - Authorization code from Meta Business Login
     * @returns {Promise<Object>} Token data with expiration
     */
    async exchangeCodeForToken(code) {

        try {
            const response = await Axios.get(`${this.baseUrl}/${this.graphApiVersion}/oauth/access_token`, {
                params: {
                    client_id: this.clientId,
                    client_secret: this.clientSecret,
                    code
                }
            });

            return {
                accessToken: response.data.access_token,
                expiresIn: response.data.expires_in
            };
        }
        catch (error) {
            this.server.logger.error('Meta token exchange failed:', error);
            throw new ValidationError('Failed to exchange code for token');
        }
    }

    /**
* Verify Meta business access token and check permissions
* @param {string} accessToken - Access token to verify
* @returns {Promise<boolean>} True if token is valid and has required permissions
*/
    async verifyToken(accessToken) {

        try {
            const appAccessToken = await this._getAppAccessToken();

            const response = await Axios.get(`${this.baseUrl}/debug_token`, {
                params: {
                    input_token: accessToken,
                    access_token: appAccessToken
                }
            });

            const { data } = response.data;

            if (!data.is_valid) {
                throw new ValidationError('Invalid Meta token');
            }

            const missingPermissions = this.requiredPermissions.filter(
                (p) => !data.scopes.includes(p)
            );

            if (missingPermissions.length > 0) {
                throw new ValidationError(`Missing Meta permissions: ${missingPermissions.join(', ')}`);
            }

            return true;
        }
        catch (error) {
            this.server.logger.error('Meta token verification failed:', error);
            throw error;
        }
    }

    async _getBusinessDetails(pageId, accessToken) {
        try {
            const response = await Axios.get(
                `${this.baseUrl}/${this.graphApiVersion}/${pageId}`,
                {
                    params: {
                        access_token: accessToken,
                        fields: 'business'
                    }
                }
            );

            if (!response.data.business) {
                throw new ValidationError(`No business found for page ${pageId}`);
            }

            return {
                businessId: response.data.business.id,
                businessName: response.data.business.name
            };
        }
        catch (error) {
            this.server.logger.error(`Failed to get business details for page ${pageId}:`, error);
            throw new ValidationError('Failed to get business details');
        }
    }

    async getBusinessAssets(accessToken) {
        try {
            const pages = await this._fetchFacebookPages(accessToken);

            // Get business details from the first page
            const businessDetails = await this._getBusinessDetails(pages[0].id, pages[0].accessToken);
            const instagramAccounts = await this._fetchInstagramAccounts(pages);

            return {
                businessId: businessDetails.businessId,
                businessName: businessDetails.businessName,
                pages,
                instagramAccounts
            };
        }
        catch (error) {
            this.server.logger.error('Failed to get business assets:', {
                message: error.message,
                status: error.response?.status,
                data: error.response?.data,
                stack: error.stack
            });
            throw new ValidationError('Failed to retrieve Meta business assets');
        }
    }

    /**
     * Fetch Facebook pages for the user
     * @private
     * @param {string} accessToken - User access token
     * @returns {Promise<Array>} Array of page objects with access tokens
     */
    async _fetchFacebookPages(accessToken) {
        const accountsResponse = await Axios.get(
            `${this.baseUrl}/${this.graphApiVersion}/me/accounts`,
            {
                params: {
                    access_token: accessToken,
                    fields: [
                        'name',
                        'id',
                        'category',
                        'category_list',
                        'tasks',
                        'verification_status',
                        'followers_count',
                        'is_published',
                        'instagram_business_account'
                    ].join(',')
                }
            }
        );

        const pages = [];

        for (const page of accountsResponse.data.data) {
            try {
                this.server.logger.info(`Processing page: ${page.id} - ${page.name}`);
                const pageAccessToken = await this._getPageAccessToken(page.id, accessToken);

                pages.push({
                    id: page.id,
                    name: page.name,
                    accessToken: pageAccessToken,
                    category: page.category,
                    categoryList: page.category_list,
                    tasks: page.tasks,
                    verificationStatus: page.verification_status,
                    followersCount: page.followers_count,
                    isPublished: page.is_published,
                    instagramBusinessId: page?.instagram_business_account?.id
                });
            }
            catch (error) {
                this.server.logger.error(`Error processing page ${page.id}:`, {
                    message: error.message,
                    status: error.response?.status,
                    data: error.response?.data,
                    stack: error.stack
                });
            }
        }

        return pages;
    }

    /**
     * Fetch Instagram accounts for all pages
     * @private
     * @param {Array} pages - Array of Facebook pages
     * @returns {Promise<Array>} Array of Instagram account objects
     */
    async _fetchInstagramAccounts(pages) {
        const instagramAccounts = [];

        for (const page of pages) {
            if (!page.instagramBusinessId) {
                continue;
            }

            try {
                const accountsDetails = await this._getInstagramAccountDetail(page.instagramBusinessId, page);
                instagramAccounts.push(...accountsDetails);
            }
            catch (error) {
                this.server.logger.error(`Error processing Instagram accounts for page ${page.id}:`, {
                    message: error.message,
                    stack: error.stack
                });
            }
        }

        return instagramAccounts;
    }

    /**
     * Get linked Instagram accounts for a Facebook page
     * @private
     * @param {Object} page - Facebook page object
     * @returns {Promise<Array>} Array of linked Instagram account IDs
     */
    async _getLinkedInstagramAccounts(page) {
        this.server.logger.info(`Getting Instagram accounts for page ${page.id}`);

        const pageResponse = await Axios.get(
            `${this.baseUrl}/${this.graphApiVersion}/${page.id}/page_backed_instagram_accounts`,
            {
                params: {
                    access_token: page.accessToken
                }
            }
        );

        return pageResponse.data.data || [];
    }

    /**
     * Get detailed information for Instagram account
     * @private
     * @param linkedAccount - Array of linked Instagram account IDs
     * @param {Object} page - Facebook page object
     * @returns {Promise<Array>} Array of Instagram account details
     */
    async _getInstagramAccountDetail(linkedAccount, page) {
        const accountDetails = [];

        try {
            const igResponse = await Axios.get(
                `${this.baseUrl}/${this.instagramGraphApiVersion}/${linkedAccount}`,
                {
                    params: {
                        access_token: page.accessToken,
                        fields: [
                            'id',
                            'username',
                            'media_count',
                            'followers_count',
                            'follows_count',
                            'profile_picture_url',
                            'is_published'
                        ].join(',')
                    }
                }
            );

            accountDetails.push({
                id: igResponse.data.id,
                username: igResponse.data.username,
                followCount: igResponse.data.follows_count,
                followedByCount: igResponse.data.followers_count,
                hasProfilePicture: !!igResponse.data?.profile_picture_url,
                isPrivate: !igResponse.data.is_published,
                isPublished: igResponse.data.is_published,
                mediaCount: igResponse.data.media_count,
                profilePic: igResponse.data?.profile_picture_url,
                connectedPageId: page.id,
                connectedPageName: page.name,
                instagramBusinessId: page.instagramBusinessId
            });
        }
        catch (error) {
            this.server.logger.error(`Error processing Instagram account ${linkedAccount}:`, {
                message: error.message,
                status: error.response?.status,
                data: error.response?.data,
                stack: error.stack
            });
        }

        return accountDetails;
    }

    /**
     * Get detailed information for Instagram accounts
     * @private
     * @param {Array} linkedAccounts - Array of linked Instagram account IDs
     * @param {Object} page - Facebook page object
     * @returns {Promise<Array>} Array of Instagram account details
     */
    async _getInstagramAccountsDetails(linkedAccounts, page) {
        const accountDetails = [];

        for (const igAccount of linkedAccounts) {
            try {
                this.server.logger.info(`Getting Instagram details for account ${igAccount.id}`);

                const igResponse = await Axios.get(
                    `${this.baseUrl}/${this.instagramGraphApiVersion}/${igAccount.id}`,
                    {
                        params: {
                            access_token: page.accessToken,
                            fields: [
                                'id',
                                'follow_count',
                                'followed_by_count',
                                'has_profile_picture',
                                'is_private',
                                'is_published',
                                'media_count',
                                'profile_pic',
                                'username'
                            ].join(',')
                        }
                    }
                );

                this.server.logger.info(`Instagram account details:`, {
                    data: JSON.stringify(igResponse.data, null, 2)
                });

                accountDetails.push({
                    id: igResponse.data.id,
                    username: igResponse.data.username,
                    followCount: igResponse.data.follow_count,
                    followedByCount: igResponse.data.followed_by_count,
                    hasProfilePicture: igResponse.data.has_profile_picture,
                    isPrivate: igResponse.data.is_private,
                    isPublished: igResponse.data.is_published,
                    mediaCount: igResponse.data.media_count,
                    profilePic: igResponse.data.profile_pic,
                    connectedPageId: page.id,
                    connectedPageName: page.name,
                    instagramBusinessId: page.instagramBusinessId
                });
            }
            catch (error) {
                this.server.logger.error(`Error processing Instagram account ${igAccount.id}:`, {
                    message: error.message,
                    status: error.response?.status,
                    data: error.response?.data,
                    stack: error.stack
                });
            }
        }

        return accountDetails;
    }

    async storeIntegration(userId, integrationData) {
        const { businessId, businessName, accessToken, pages, instagramAccounts } = integrationData;

        try {
            const { MetaIntegration } = this.server.models();

            // Check if integration already exists
            const existingIntegration = await MetaIntegration.query()
                .where('fb_business_id', businessId)
                .first();

            if (existingIntegration) {
                throw new ValidationError('This Facebook Business is already connected to another account');
            }

            // Calculate token expiration
            const payload = {
                user_id: userId,
                fb_business_id: businessId,
                business_name: businessName,
                access_token: accessToken.accessToken,
                facebook_pages: pages,
                instagram_accounts: instagramAccounts
            };

            if (accessToken.expiresIn) {
                const tokenExpiresAt = new Date();
                tokenExpiresAt.setSeconds(tokenExpiresAt.getSeconds() + accessToken.expiresIn);
                payload.token_expires_at = tokenExpiresAt;
            }

            const payloadString = JSON.stringify(payload);
            this.server.logger.info(`Meta integration: ${payloadString}`);

            // Create new integration
            const result = await MetaIntegration.query().insert(payload);

            this.server.logger.info('Meta integration stored successfully:', {
                businessId,
                userId
            });

            return result;
        }
        catch (error) {
            this.server.logger.error('Failed to store meta integration:', {
                error: error.message,
                stack: error.stack,
                businessId,
                userId
            });

            if (error instanceof ValidationError) {
                throw error;
            }

            throw new ValidationError('Failed to store Meta integration');
        }
    }

    async deleteIntegration(userId) {
        try {
            const { MetaIntegration } = this.server.models();

            // Find and delete the integration
            const deleted = await MetaIntegration.query()
                .where('user_id', userId)
                .del();

            if (!deleted) {
                throw new ValidationError('No Meta integration found for this user');
            }

            this.server.logger.info('Meta integration deleted successfully:', {
                userId
            });

            return true;
        }
        catch (error) {
            this.server.logger.error('Failed to delete Meta integration:', {
                error: error.message,
                stack: error.stack,
                userId
            });

            if (error instanceof ValidationError) {
                throw error;
            }

            throw new ValidationError('Failed to delete Meta integration');
        }
    }

    async getIntegrationStatus(userId, instagramBusinessId = null) {
        try {
            const { MetaIntegration } = this.server.models();
            const query = MetaIntegration.query()
                .where('user_id', userId);

            if (instagramBusinessId) {
                query.andWhereRaw(
                    `EXISTS (
                    SELECT 1 FROM jsonb_array_elements(instagram_accounts) AS account
                    WHERE (account->>'instagramBusinessId')::text = ?
                )`,
                    [instagramBusinessId]
                );
            }

            const integration = await query.first();

            if (!integration) {
                return {
                    connected: false,
                    reason: 'NO_INTEGRATION'
                };
            }

            if (integration.token_expires_at) {
                // Check if token is expired
                const isExpired = integration.token_expires_at < new Date();
                if (isExpired) {
                    return {
                        connected: false,
                        reason: 'TOKEN_EXPIRED',
                        businessName: integration.business_name
                    };
                }
            }

            // Return all integration data
            return {
                connected: true,
                integration // Return the complete integration object
            };
        }
        catch (error) {
            this.server.logger.error('Failed to get Meta integration status:', {
                error: error.message,
                stack: error.stack,
                userId
            });
            throw new ValidationError('Failed to check Meta integration status');
        }
    }
    /**
 * Create a Facebook post with image and text
 * @param {string} pageId - ID of the Facebook page
 * @param {string} accessToken - Page access token
 * @param {Object} postData - Post data
 * @param {string} postData.imageUrl - URL of the image to post
 * @param {string} postData.message - Text message for the post
 * @returns {Promise<Object>} Object containing post_id and photo_id
 */
    async createPostWithImage(pageId, accessToken, postData) {
        try {
            // Validate required parameters
            if (!pageId || !accessToken || !postData.imageUrl || !postData.message) {
                throw new ValidationError('Missing required parameters for creating post');
            }

            const response = await Axios.post(
                `${this.baseUrl}/${this.graphApiVersion}/${pageId}/photos`,
                null,
                {
                    params: {
                        url: postData.imageUrl,
                        message: postData.message,
                        access_token: accessToken
                    }
                }
            );

            this.server.logger.info('Successfully created Facebook post with image:', {
                pageId,
                postId: response.data.post_id,
                photoId: response.data.id
            });

            return {
                postId: response.data.post_id,
                photoId: response.data.id
            };
        }
        catch (error) {
            this.server.logger.error('Failed to create Facebook post with image:', {
                error: error.message,
                status: error.response?.status,
                data: error.response?.data,
                requestUrl: `${this.baseUrl}/${this.graphApiVersion}/${pageId}/photos`,
                stack: error.stack,
                pageId
            });

            // Specific error handling for different cases
            if (error.response?.data?.error) {
                const fbError = error.response.data.error;
                throw new ValidationError(`Facebook API Error: ${fbError.message} (Code: ${fbError.code})`);
            }

            throw new ValidationError('Failed to create Facebook post with image');
        }
    }

};
