/* eslint-disable max-len */
'use strict';

const Schmervice = require('schmervice');
const { ManagementClient } = require('auth0');
const { NotFoundError, ValidationError } = require('../helpers/errors');
const { filterMaxMin, caseInsensitiveFiltering, groupBy } = require('../helpers/utils');
const { raw, ref } = require('objection');
const { v4: uuidv4 } = require('uuid');
const Axios = require('axios');
const AWS = require('aws-sdk');

module.exports = class UserService extends Schmervice.Service {

    /**
     * This function initializes the auth0 client.
     *
     */
    initialize() {

        this.auth0 = new ManagementClient({
            domain: this.server.settings.app.auth.auth0.original_domain,
            clientId: this.server.settings.app.auth.provider.client_id,
            clientSecret: this.server.settings.app.auth.provider.client_secret,
            scope: 'read:users update:users create:users create:user_tickets'
        });

        this.sqs = new AWS.SQS({
            accessKeyId: this.server.settings.app.amazon.aws_id,
            secretAccessKey: this.server.settings.app.amazon.aws_secret,
            region: this.server.settings.app.amazon.aws_region
        });

        this.enrichmentQueueUrl = this.server.settings.app.amazon.enrichment_sqs_queue_url;
    }

    /**
     * Get a user (from the db) given its email.
     *
     * @param {string} email
     *  The email of the user.
     *
     * @return {Object} The db user data.
     */
    async getUserByEmail({ email }) {

        const { User } = this.server.models();

        const user = await User.query().where(raw(`LOWER(email)`), email.toLowerCase()).first();

        if (user) {
            return user;
        }

        return null;

    }
    async findUserByClientId({ clientId }) {

        const { User } = this.server.models();

        const user = await User.query().findOne({ client_id: clientId });

        if (!user) {
            throw new NotFoundError('User not found');
        }

        return { ...user, role: user.role };
    }

    /**
     * Get a user from auth0 given its internal auth0 id.
     *
     * @param {string} id
     *  The auth0 internal id of the user.
     *
     * @return {Object} The auth0 user data.
     */
    async getAuth0UserById({ id }) {

        return await this.auth0.getUser({ id });
    }

    /**
     * Get a user from auth0 given its email.
     *
     * @param {string} email
     *  The email of the user.
     *
     * @return {Object} The auth0 user data.
     */
    async getAuth0UserByEmail({ email }) {

        const users = await this.auth0.getUsers({
            q: `(email: "${email}")`
        });

        if (users.length === 0) {
            throw new NotFoundError('User not found');
        }

        if (users.length > 1) {
            this.server.logger.error({ email, users }, 'More than one user share the same email');
        }

        return users[0];
    }
    /**
 * Get multiple users from Auth0 given their emails.
 *
 * @param {Array<string>} emails
 *  The emails of the users to fetch.
 *
 * @return {Array<Object>} The Auth0 user data for the given emails.
 */
    async getAuth0UsersByEmail(emails) {

        if (!emails || emails.length === 0) {
            return [];
        }

        // Constructing the query to search for multiple emails.
        // Note: The exact syntax for querying multiple values might vary based on the Auth0 API and its version.
        // This is a general approach and might need adjustments.
        const query = emails.map((email) => `email:"${email}"`).join(' OR ');

        let users = [];
        try {
            users = await this.auth0.getUsers({ q: query });
        }
        catch (error) {
            this.server.logger.error({ error, query }, 'Failed to fetch users from Auth0');
            throw error; // Or handle it as you see fit
        }

        // Optionally log if the response users length doesn't match the requested emails length
        if (users.length !== emails.length) {
            this.server.logger.warn({
                requestedEmails: emails.length,
                returnedUsers: users.length
            }, 'Mismatch between requested emails and returned users');
        }

        return users;
    }
    /**
 * Update a user's metadata in Auth0.
 *
 * @param {string} userId The Auth0 user ID.
 * @param {Object} metadata The new metadata to set for the user.
 * @return {Promise<Object>} The updated Auth0 user data.
 */
    async updateAuth0UserMetadata(email, subClientRole) {

        try {

            const user = await this.getAuth0UserByEmail({ email });
            const params = { id: user.user_id }; // Object with 'id' to match 'ObjectWithId' type
            const updatedUser = this.auth0.updateUserMetadata(params, { ...user.user_metadata, subClientRole });
            return updatedUser;
        }
        catch (error) {
            throw new Error(`Failed to update user metadata: ${error.message || 'Unknown error'}`);
        }
    }

    /**
     * Generate an Auth0 password-reset link ( ticket ).
     *
     * @param {string} email
     *  The email of the user.
     *
     */
    async generatePasswordResetLink({ email }) {

        const params = {
            // Dev
            // connection_id: 'con_hcxi5Ru8Af06UCYQ',

            // Staging
            // connection_id: 'con_ofM3WbDevU8pyM6l',

            // Production
            // connection_id: 'con_LAEeTw6ozcIs8GC9',
            connection_id: this.server.settings.app.auth.auth0.connection_id,
            client_id: this.server.settings.app.auth.provider.client_id,
            email,
            result_url: this.server.settings.app.auth.auth0.result_url
        };

        try {
            const response = await this.auth0.createPasswordChangeTicket(params);
            return response;
        }
        catch (err) {
            console.error('Error in generatePasswordResetLink:', err);
            throw new Error(`Auth0 error || 'Unknown error'`);
        }
    }

    /**
     * Initiates a password reset using Auth0's dbconnections/change_password endpoint
     *
     * @param {Object} options
     * @param {string} options.email - The email address of the user
     * @returns {Promise<Object>} - Response from Auth0
     */
    async requestPasswordReset({ email }) {

        try {
            const auth0Domain = this.server.settings.app.auth.auth0.original_domain;
            const clientId = this.server.settings.app.auth.provider.client_id;
            const host = process.env.CURRENT_HOST || this.server.settings.app.auth.auth0.host;

            // First verify the email exists in Auth0
            try {
                await this.getAuth0UserByEmail({ email });
            }
            catch (error) {
                if (error instanceof NotFoundError) {
                    this.server.logger.warn({ email }, 'Password reset requested for non-existent user');
                    // For security, don't reveal if email exists
                    return { message: 'If your email exists in our system, you will receive password reset instructions.', success: false };
                }

                throw error;
            }

            console.log('host', host);
            // Send the password reset request to Auth0
            await Axios.post(`https://${auth0Domain}/dbconnections/change_password`, {
                client_id: clientId,
                email,
                connection: 'Username-Password-Authentication',
                result_url: host
            });

            this.server.logger.info({ email }, 'Password reset email sent successfully');
            return {
                message: 'If your email exists in our system, you will receive password reset instructions.',
                success: true
            };
        }
        catch (error) {
            this.server.logger.error({ error, email }, 'Failed to send password reset email');

            // Don't expose internal errors to the client
            return {
                message: 'If your email exists in our system, you will receive password reset instructions.',
                success: false
            };
        }
    }

    /**
     * Create a user in Auth0.
     *
     * @param {Object} user
     *  The user object that contains all the informations required for user registration
     *
     * @param {string} password
     *  The password chosen for the user
     *
     * @param role
     * @param subClientRole
     * @param isVerified
     * @return {Object} The Auth0 user saved.
     */
    async createAuth0User({ user, password, role, subClientRole = null, isVerified = true }) {

        const savedUser = await this.auth0.createUser({
            email: user.email,
            password,
            name: user.name,
            connection: 'Username-Password-Authentication',
            email_verified: isVerified,
            user_metadata: {
                role,
                subClientRole
            }
        });

        return savedUser;
    }

    /**
     * Verify the user in Auth0.
     * @param user
     * @returns {Promise<void>}
     */
    async verifyAuth0User({ user }) {

        if (!user) {
            throw new ValidationError('User not found');
        }

        try {
            await this.auth0.sendEmailVerification({
                user_id: user.auth_provider_reference
            });
        }
        catch (error) {
            this.server.logger.error({ error }, 'Error in verifyAuth0User');
            throw new ValidationError('Failed to send verification email');
        }
    }
    /**
     * Initiate the passwordless authentication process.
     *
     * @param {string} phone_number
     *  The phone number of the client
     */
    async startPasswordlessAuth({ phone_number }) {

        try {
            const auth0Domain = this.server.settings.app.auth.provider.config.domain;
            const clientId = this.server.settings.app.auth.provider.client_id;
            const clientSecret = this.server.settings.app.auth.provider.client_secret;

            const response = await Axios.post(`https://${auth0Domain}/passwordless/start`, {
                client_id: clientId,
                client_secret: clientSecret,
                connection: 'sms',
                phone_number,
                send: 'code',
                authParams: {
                    scope: 'openid'
                }
            });

            // If Auth0 returns a data object with an error property, throw an error
            if (response.data && response.data.error) {
                throw new Error(`Auth0 error: ${response.data.error_description || 'Unknown error'}`);
            }

        }
        catch (err) {
            if (err && err.response && err.response.data) {
                // Log detailed error information from Auth0
                const detailedErrorMessage = `Failed to initiate passwordless authentication for phone: ${phone_number}. 
                        Error: ${err.message}. 
                        Auth0 Detailed Error: ${err.response.data.error_description || 'Unknown error'}. 
                        Full Error Details: ${JSON.stringify(err.response.data)}`;

                throw new Error(detailedErrorMessage);
            }
            else {
                // If there's no detailed error from Auth0, just throw the error as is
                throw new Error(`Failed to start passwordless authentication for phone: ${phone_number}. Error: ${err.message}`);
            }
        }
    }

    async getAuth0Token({ username, password, grant_type }) {

        try {
            const auth0Domain = this.server.settings.app.auth.auth0.original_domain;
            const clientId = this.server.settings.app.auth.provider.client_id;
            const clientSecret = this.server.settings.app.auth.provider.client_secret;

            const response = await Axios.post(`https://${auth0Domain}/oauth/token`, {
                grant_type,
                username,
                password,
                client_id: clientId,
                client_secret: clientSecret,
                scope: 'openid profile email'
            });

            return response.data;
        }
        catch (error) {
            console.error('Error in getAuth0Token:', error);
            if (error && error.response && error.response.data) {
                const detailedErrorMessage = `Failed to get access token for user: ${username}. 
                    Error: ${error.message}. 
                    Auth0 Detailed Error: ${error.response.data.error_description || 'Unknown error'}. 
                    Full Error Details: ${JSON.stringify(error.response.data)}`;
                throw new Error(detailedErrorMessage);
            }
            else {
                throw new Error(`Failed to get access token for user: ${username}. Error: ${error.message}`);
            }
        }
    }

    async getSocialAuth0Token({ code }) {

        try {
            const auth0Domain = this.server.settings.app.auth.auth0.original_domain;
            const clientId = this.server.settings.app.auth.provider.client_id;
            const clientSecret = this.server.settings.app.auth.provider.client_secret;
            const redirectUri = `${process.env.CURRENT_HOST}/login`;

            // Exchange the authorization code for tokens with Auth0
            const response = await Axios.post(`https://${auth0Domain}/oauth/token`, {
                grant_type: 'authorization_code',
                client_id: clientId,
                client_secret: clientSecret,
                code,
                redirect_uri: redirectUri
            });

            if (response.data.error) {
                throw new Error(`Auth0 error: ${response.data || 'Unknown error'}`);
            }

            return response.data;
        }
        catch (error) {
            this.server.logger.error('Error in getGoogleToken:', error);

            if (error.response && error.response.data) {
                const detailedError = `Failed to get Google tokens. Error: ${error.message}. Auth0 details: ${JSON.stringify(error.response.data)}`;
                throw new Error(detailedError);
            }

            throw new Error(`Failed to get Google tokens. Error: ${error.message}`);
        }
    }

    /**
     * Verify the OTP received by the user via SMS.
     *
     * @param {string} phone_number The phone number of the user.
     * @param {string} otp The OTP code received by the user.
     */
    async verifyOtp({ phone_number, otp }) {

        try {
            const noVerifiedPhones = this.server.settings.app.auth.auth0.no_verified_phones;

            if (noVerifiedPhones && noVerifiedPhones.includes(phone_number)) {
                return {};
            }

            const auth0Domain = this.server.settings.app.auth.provider.config.domain;
            const clientId = this.server.settings.app.auth.provider.client_id;
            const clientSecret = this.server.settings.app.auth.provider.client_secret;

            const response = await Axios.post(`https://${auth0Domain}/oauth/token`, {
                grant_type: 'http://auth0.com/oauth/grant-type/passwordless/otp',
                client_id: clientId,
                client_secret: clientSecret,
                username: phone_number,
                otp,
                realm: 'sms',
                scope: 'openid profile'
            });

            // Check response for errors
            if (response.data && response.data.error) {
                throw new Error(`Auth0 error: ${response.data.error_description || 'Unknown error'}`);
            }

            return response.data;
        }
        catch (err) {
            if (err && err.response && err.response.data) {
                const detailedErrorMessage = `Failed to verify OTP for phone: ${phone_number}. 
                    Error: ${err.message}. 
                    Auth0 Detailed Error: ${err.response.data.error_description || 'Unknown error'}. 
                    Full Error Details: ${JSON.stringify(err.response.data)}`;
                throw new Error(detailedErrorMessage);
            }
            else {
                throw new Error(`Failed to verify OTP for phone: ${phone_number}. Error: ${err.message}`);
            }
        }
    }

    /**
     * Delete a user from Auth0 starting from email.
     *
     * @param {string} email
     *  The email of the user.
     *
     * @return {Object} The Auth0 user data.
     */
    async deleteAuth0User({ email }) {

        const userToBeDeleted = await this.getAuth0UserByEmail({ email });
        await this.auth0.deleteUser({ id: userToBeDeleted.user_id });

        return userToBeDeleted;
    }

    /**
     * Update the user in DB and Auth0.
     * @param user
     * @returns {Promise<void>}
     */
    async updateAccountVerified({ user }) {

        if (!user) {
            throw new ValidationError('User not found');
        }

        if (user.account_verified) {
            throw new ValidationError('User already verified');
        }

        const { User } = this.server.models();
        const userToBeUpdated = await this.getAuth0UserByEmail({ email: user.email });

        if (!userToBeUpdated) {
            throw new NotFoundError('User not found');
        }

        const trx = await User.startTransaction();

        try {
            await User.query(trx).patchAndFetchById(user.id, {
                account_verified: true
            });
            await trx.commit();
        }
        catch (error) {
            await trx.rollback();
            this.server.logger.error({ error }, 'Error updating user');
            throw new ValidationError('Failed to update user');
        }
    }

    /**
     * Create a user in DB inserting records in users, payment_details and photographers.
     * In case of transaction aborted, the system will delete Auth0 user.
     *
     * @param {Object} user
     *  The user object
     *
     * @param {string} password
     *  The password chosen for the user
     *
     * @param {Object} paymentDetail
     *  The payment detail object
     *
     * @param {Object} additionalInformations
     *  The object that contains all the information that will characterize different roles
     * @param {string} role
     *  The role of the user
     * @param {string} parentGuid
     *  The guid used to create a sub account related with parent
     *
     * @param subClientRole
     * @param isVerified
     * @param syncAuth0
     * @return {Object} The user data.
     */
    async createUser({ user, password, paymentDetail, additionalInformations, role, parentGuid,  subClientRole, loggedInUser = null, isVerified = true, syncAuth0 = true }) {

        const { User, Photographer, PaymentDetail, Client } = this.server.models();

        const { logger } = this.server;
        const trx = await PaymentDetail.startTransaction();

        let parent = parentGuid ? await Client.query().where('registration_guid', parentGuid).first() : null;

        if (parent === null && subClientRole) {
            parent = await loggedInUser.client;
        }

        if (parent === undefined) {
            throw new NotFoundError(`Not found parent with specified guid: ${parentGuid}`);
        }

        try {
            let auth0User;
            // Create auth0 user
            if (syncAuth0) {
                auth0User = await this.createAuth0User({ user, password, role, subClientRole: subClientRole ? subClientRole : null, isVerified  });
            }

            // Store user data in db
            const savedPaymentDetail = await PaymentDetail.query(trx).insertAndFetch(paymentDetail);
            const args = (parent) ? { parent_client_id: parent.id } : { registration_guid: uuidv4() };

            const savedPhotographer = (role === 'photographer') && await Photographer.query(trx)
                .insertAndFetch({ ...additionalInformations, payment_details_id: savedPaymentDetail.id });
            const savedClient = (role === 'client') && await Client.query(trx)
                .insertAndFetch({ ...additionalInformations, payment_details_id: savedPaymentDetail.id, ...args });

            user.email = user.email.toLowerCase();

            // Account approval
            let status = 'waiting';
            if (role === 'photographer') {
                status = 'waiting';
            }
            else if (role === 'editor') {
                status = 'waiting';
            }
            else if (role === 'admin') {
                status = 'active';
            }
            else if (parentGuid) {
                status = 'active';
            }
            else if (role === 'client') {
                status = 'active';
            }
            else {
                status = 'waiting';
            }

            const userPayload = {
                ...user,
                client_id: savedClient?.id || null,
                photographer_id: savedPhotographer?.id || null,
                editor_id: null,
                status
            };

            if (auth0User) {
                userPayload.auth_provider_reference = auth0User.user_id;
            }

            const savedUser = await User.query(trx).insertAndFetch(userPayload);
            await trx.commit();

            return savedUser;
        }
        catch (err) {
            await trx.rollback();
            if (err.statusCode !== 409 && syncAuth0) {
                await this.deleteAuth0User({ email: user.email }).catch((e) => {

                    logger.error(e, 'Error in auth0 user deletion');
                });
            }

            throw err;
        }
    }

    generateStrongPassword(length = 10) {

        const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+~`|}{[]:;?><,./-=';
        let password = '';
        // eslint-disable-next-line @hapi/for-loop
        for (let i = 0, n = charset.length; i < length; ++i) {
            password += charset.charAt(Math.floor(Math.random() * n));
        }

        return password;
    }

    /**
 * Create a B2C client in the database. This will create a passwordless user in Auth0
 * and store relevant client information in your database.
 *
 * @param {Object} user The user object containing basic user information.
 * @param {Object} paymentDetail Payment details for the client.
 * @param {Object} additionalInformations Additional information for the client.
 * @return {Object} The created client data.
 */
    async createClient({ user, paymentDetail, additionalInformations, syncAuth0 = true }) {

        const { User, PaymentDetail, Client } = this.server.models();

        const { logger } = this.server;
        const trx = await PaymentDetail.startTransaction();

        try {
            // Create auth0 user ( auth0 DB )
            const password = this.generateStrongPassword(12);
            let authUser = null;

            if (syncAuth0) {
                authUser = await this.createAuth0User({ user, password, role: 'client' });
            }

            // Create Payment Detail data entry
            const savedPaymentDetail = await PaymentDetail.query(trx).insertAndFetch(paymentDetail);
            // Create a Client data entry
            const savedClient =  await Client.query(trx)
                .insertAndFetch({ ...additionalInformations, payment_details_id: savedPaymentDetail.id });

            user.email = user.email.toLowerCase();
            const userPayload = {
                ...user,
                client_id: savedClient.id,
                photographer_id: null,
                editor_id: null,
                status: 'active'
            };

            if (authUser) {
                userPayload.auth_provider_reference = authUser.user_id;
            }

            // Create a User data entry ( Flashy DB )
            const savedUser = await User.query(trx).insertAndFetch(userPayload);
            await trx.commit();
            return { savedPaymentDetail, savedClient, savedUser, authUser };
        }
        catch (err) {

            await trx.rollback();
            if (err.statusCode !== 409) {
                await this.deleteAuth0User({ email: user.email }).catch((e) => {

                    logger.error(e, 'Error in auth0 user deletion');
                });
            }

            throw err;
        }
    }

    async getAllUniqueLocations(targetRole) {

        const { User } = this.server.models();
        let query;
        if (targetRole === 'client') {
            query = User.query()
                .distinct('c.country')  // Changed from 'c.main_location' to 'c.country'
                .join('clients as c', 'users.client_id', 'c.id')
                .whereNotNull('c.country')  // Changed from 'c.main_location' to 'c.country'
                .orderBy('c.country');  // Changed from 'c.main_location' to 'c.country'
        }
        else if (targetRole === 'photographer') {
            query = User.query()
                .distinct('p.main_location')
                .join('photographers as p', 'users.photographer_id', 'p.id')
                .whereNotNull('p.main_location')
                .orderBy('p.main_location');
        }
        else {
            throw new Error('Invalid target role specified');
        }

        const locations = await query;
        return locations.map((l) => l.country || l.main_location);  // Changed to handle both cases
    }

    /**
 * Fetches photographers with their details, total shoots, and shoots per service.
 * Implements pagination with limit and offset, and returns the total count of photographers.
 *
 * @param {Object} params - Parameters for the query.
 * @param {number} params.limit - The maximum number of records to return.
 * @param {number} params.offset - The number of records to skip.
 * @param {string} params.searchQuery - text search query
 * @param {array} params.services - Array of integers to filter by services
 * @param {array} params.locations - Array of strings to filter by city
 * @param {array} params.statuses - Array of strings to filter by status
 * @param {array} params.contentType - Array of strings to filter by content type
 * @returns {Object} - An object containing photographers array and total count.
 */
    async getPhotographers({ size, offset, searchQuery, services, locations, statuses, contentType }) {

        const { User } = this.server.models();

        // Define the subqueries (unchanged)
        const subqueries = `
            LEFT JOIN (
                SELECT photographer_id, COUNT(*) AS total_shoots
                FROM shoots
                WHERE status IN ('ready', 'completed')
                GROUP BY photographer_id
            ) sc ON p.id = sc.photographer_id
            LEFT JOIN (
                SELECT
                    s.photographer_id,
                    json_agg(
                        json_build_object(
                            'service_name', svc.name,
                            'service_id', s.service_id,
                            'count', s.service_count
                        )
                    ) AS shoots_per_service
                FROM (
                    SELECT 
                        s.photographer_id, 
                        ss.service_id, 
                        COUNT(DISTINCT s.id) as service_count
                    FROM shoots s
                    JOIN shoots_services ss ON s.id = ss.shoot_id
                    WHERE s.status IN ('ready', 'completed')
                    GROUP BY s.photographer_id, ss.service_id
                ) s
                JOIN services svc ON s.service_id = svc.id
                GROUP BY s.photographer_id
            ) asp ON p.id = asp.photographer_id
        `;

        // Build the main query
        let query = User.query()
            .select(
                'users.name',
                'users.email',
                'users.phone',
                'users.status',
                'p.id AS id',
                'p.birthdate',
                'p.english_level',
                'p.arabic_level',
                'p.main_location',
                'p.is_pro',
                'p.is_internal',
                raw('p.details::jsonb - \'bank\'').as('details'),
                raw('COALESCE(sc.total_shoots, 0)').as('total_shoots'),
                raw('COALESCE(asp.shoots_per_service, \'[]\'::json)').as('shoots_per_service'),
                raw('(SELECT json_agg(json_build_object(\'service_id\', service_id, \'rate\', rate)) FROM photographers_services WHERE photographer_id = p.id)').as('services'),
                raw('COUNT(*) OVER()').as('total')
            )
            .from(raw(`users JOIN photographers p ON users.photographer_id = p.id ${subqueries}`))
            .whereNotNull('users.photographer_id');

        // Apply search query if provided
        if (searchQuery) {
            query = query.where('users.name', 'ILIKE', `%${searchQuery}%`);
        }

        // Apply service filter if provided
        if (services && services.length > 0) {
            query = query.whereRaw(`
                EXISTS (
                    SELECT 1
                    FROM shoots s
                    JOIN shoots_services ss ON s.id = ss.shoot_id
                    WHERE s.photographer_id = p.id
                    AND s.status IN ('ready', 'completed')
                    AND ss.service_id = ANY(?)
                )
            `, [services]);
        }

        // Apply location filter if provided
        if (locations && locations.length > 0) {
            query = query.whereRaw('p.main_location = ANY(?)', [locations]);
        }

        if (statuses && statuses.length > 0) {
            query = query.whereRaw('users.status = ANY(?)', [statuses]);
        }

        if (contentType && contentType.length > 0) {
            query = query.whereRaw(`
                EXISTS (
                    SELECT 1
                    FROM shoots s
                    WHERE s.photographer_id = p.id
                    AND s.status IN ('ready', 'completed')
                    AND s.content = ANY(?)
                )
            `, [contentType]);
        }

        // Apply ordering, limit, and offset
        query = query.orderBy('total_shoots', 'DESC');

        if (size !== undefined) {
            query = query.limit(size);
        }

        if (offset !== undefined) {
            query = query.offset(offset);
        }

        const photographers = await query;

        // Process the results
        const total = photographers.length > 0 ? Number(photographers[0].total) : 0;
        const phs = photographers.map(({ total: _, ...photographer }) => photographer);

        return { photographers: phs, total };
    }

    /**
 * @param {Object} params - Parameters for the query.
 * @param {string} [params.searchQuery] - Optional search query for filtering results.
 * @param {string[]} [params.locations] - Optional array of locations to filter by.
 * @param {string[]} [params.statuses] - Optional array of statuses to filter by.
 * @param {Object} [params.revenueRange] - Optional revenue range to filter by.
 * @param {number} [params.revenueRange.min] - Minimum revenue.
 * @param {number} [params.revenueRange.max] - Maximum revenue.
 * @param {boolean} [params.fetchAll=false] - Whether to bypass pagination and fetch all results.
 * @returns {Object} - An object containing customers array and total count.
 */
    async getCustomers({ searchQuery, locations, statuses, revenueRange, fetchAll = false, offset = 0, limit = 50 }) {

        const { User } = this.server.models();

        // Build the base query with all filters applied.
        let baseQuery = User.query()
            .select(
                'users.id',
                'users.name',
                'users.email',
                'users.phone',
                'users.status',
                'users.photographer_id',
                'users.client_id',
                'users.editor_id',
                'c.company_name',
                'c.industry',
                'c.country',
                'c.contact_name',
                'c.contact_email',
                'c.contact_phone',
                'c.main_location',
                'c.registration_guid',
                'c.parent_client_id',
                // Using raw queries for aggregated fields
                raw('COALESCE((SELECT COUNT(*) FROM shoots WHERE consumer_client_id = c.id), 0) AS shoot_number'),
                raw('COALESCE((SELECT SUM(price) FROM shoots WHERE consumer_client_id = c.id), 0) AS shoot_total_price_or_revenue'),
                raw(`(
                SELECT json_agg(json_build_object(
                    'id', sc.id,
                    'name', su.name,
                    'email', su.email,
                    'phone', su.phone,
                    'status', su.status,
                    'photographer_id', su.photographer_id,
                    'client_id', su.client_id,
                    'editor_id', su.editor_id,
                    'company_name', sc.company_name,
                    'industry', sc.industry,
                    'country', sc.country,
                    'contact_name', sc.contact_name,
                    'contact_email', sc.contact_email,
                    'contact_phone', sc.contact_phone,
                    'main_location', sc.main_location,
                    'registration_guid', sc.registration_guid,
                    'parent_client_id', sc.parent_client_id,
                    'shoot_number', COALESCE((SELECT COUNT(*) FROM shoots WHERE consumer_client_id = sc.id), 0),
                    'shoot_total_price_or_revenue', COALESCE((SELECT SUM(price) FROM shoots WHERE consumer_client_id = sc.id), 0),
                    'role', 'client'
                ))
                FROM clients sc
                JOIN users su ON sc.id = su.client_id
                WHERE sc.parent_client_id = c.id
            ) AS subclients`)
            )
            .from('users')
            .join('clients AS c', 'users.client_id', 'c.id')
            .whereNull('c.parent_client_id');

        // Apply search filtering.
        if (searchQuery) {
            const lowerSearchQuery = searchQuery.toLowerCase();
            baseQuery = baseQuery.where((builder) => {

                builder.whereRaw('LOWER(c.company_name) LIKE ?', [`%${lowerSearchQuery}%`])
                    .orWhereRaw('LOWER(users.name) LIKE ?', [`%${lowerSearchQuery}%`])
                    .orWhereRaw('CAST(users.client_id AS TEXT) LIKE ?', [`%${searchQuery}%`]);
            });
        }

        // Apply location filtering.
        if (locations && locations.length > 0) {
            baseQuery = baseQuery.whereIn('c.country', locations);
        }

        // Apply status filtering.
        if (statuses && statuses.length > 0) {
            baseQuery = baseQuery.whereIn('users.status', statuses);
        }

        // Apply revenue filtering.
        if (revenueRange && (revenueRange.min !== undefined || revenueRange.max !== undefined)) {

            baseQuery = baseQuery.where((builder) => {

                const revenueSubquery = 'COALESCE((SELECT SUM(price) FROM shoots WHERE consumer_client_id = c.id), 0)';
                if (revenueRange.min !== undefined) {
                    builder.whereRaw(`${revenueSubquery} >= ?`, [revenueRange.min]);
                }

                if (revenueRange.max !== undefined) {
                    builder.whereRaw(`${revenueSubquery} <= ?`, [revenueRange.max]);
                }
            });
        }

        baseQuery = baseQuery.orderBy('users.id', 'DESC');

        // Clone the base query to compute the total before applying pagination.
        const countQuery = baseQuery.clone().clearSelect().clearOrder().countDistinct('users.id as total');

        let customers;
        if (fetchAll) {
            customers = await baseQuery;
        }
        else {
            customers = await baseQuery.limit(limit).offset(offset);
        }

        // Get the total count from the cloned query.
        let total = 0;
        if (fetchAll) {
            total = customers.length;
        }
        else {
            const countResult = await countQuery;
            // countResult is an array like [{ total: "123" }] so make sure to convert it if needed.
            total = parseInt(countResult[0].total, 10);
        }

        // Process the results if needed.
        const processedCustomers = customers.map((customer) => ({
            ...customer,
            role: 'client',
            subclients: customer.subclients || []
        }));

        return { customers: processedCustomers, total };
    }

    async getClientRevenueRange() {

        const { Shoot } = this.server.models();

        const result = await Shoot.query()
            .select(
                Shoot.knex().raw('COALESCE(MIN(client_revenue), 0) as low'),
                Shoot.knex().raw('COALESCE(MAX(client_revenue), 0) as high')
            )
            .from(
                Shoot.query()
                    .select('consumer_client_id')
                    .sum('price as client_revenue')
                    .whereNotNull('consumer_client_id')
                    .groupBy('consumer_client_id')
                    .as('client_revenues')
            );

        return {
            low: Math.ceil(result[0]?.low) || 0,
            high: Math.ceil(result[0]?.high) || 0
        };
    }

    /**
     * Get users (from the db) given filters.
     *
     * @param {string} id
     *  The id of the user.
     *
     * @param {string} role
     *  The role of users
     *
     * @param {string} name
     *  The name of users
     *
     * @param {string} main_location
     *  The main location
     *
     * @param {array} statuses
     *  The array of statuses
     *
     * @param {Object} shoot_total_price_or_revenue
     *  The object containing the minimum and the maximum price or revenue
     *
     * @param {array} services
     *  The array of services
     *
     * @param {Object} shoot_number
     *  The object containing the minimum and the maximum number of shoot
     *
     * @param {number} limit
     *  The number of users.
     *
     * @param {number} offset
     *  The offset clause.
     *
     * @param {Object} searcher
     * The user that do the search
     *
     * @param {string} searcher_role
     * The role of the user that do the search
     *
     * @return {Object} The db users data.
     *
     * @return {number} total
     *  The amount of records in db not taking into account pagination.
     *
     */
    async getUsers({ id, role, name, main_location, statuses, shoot_total_price_or_revenue, services, shoot_number, limit, offset,
        searcher = null, searcher_role = null }) {

        const { User, Shoot, PhotographerService, Client } = this.server.models();
        const query = User.query()
            .leftJoinRelated('[photographer, client]');

        if (role === 'client' && searcher_role === 'admin') {
            query
                .whereNull('client.parent_client_id');
        }

        //filter on id
        if (id) {
            if (role === 'admin') {
                query.where('users.id', id);
            }
            else if (role === 'client') {
                query.where('client.id', id);
            }
            else if (role === 'photographer') {
                query.where('photographer.id', id);
            }
        }

        //if the searcher is a client, he will be able to view only the users who have his id as client_parent_id
        if (searcher_role === 'client') {
            query.where('client.parent_client_id', searcher.client_id);
        }

        //filters on name and main location can be applied to each role
        if (name) {
            //filter case insensitive with like operator
            if (role !== 'client') {
                query.where(caseInsensitiveFiltering({ fieldName: 'users.name', value: name }));
            }
            else {
                query.where(function () {

                    this
                        .where(caseInsensitiveFiltering({ fieldName: 'users.name', value: name }))
                        .orWhere(caseInsensitiveFiltering({ fieldName: 'client.company_name', value: name }));
                });
            }
        }

        if (main_location) {
            //filter case insensitive with like operator
            query.where(caseInsensitiveFiltering({
                fieldName: (role === 'photographer') ? 'photographer.main_location' : 'client.main_location',
                value: main_location
            }));
        }

        //filter on statuses
        if (statuses) {
            query.whereIn('users.status', statuses);
        }

        let filteredStatuses = [Shoot.statuses.completed];

        let photographerToServicesNotFilteredDict;
        let shootsPerPhotographerDict;
        let shootsPerClientDict;
        let max_cost_or_revenue;
        let max_shoot_number;
        let total;
        if (role === 'photographer') {

            const photographerServicesQuery = PhotographerService.query();

            //dictionary that will be used used to filter photographers that have more than the required services
            photographerToServicesNotFilteredDict = (await photographerServicesQuery).reduce((acc, photographerService) => ({
                ...acc,
                [photographerService.photographer_id]: [...(acc[photographerService.photographer_id] || []), photographerService]
            }), {});

            //check for services related to users
            services = services && new Set(services);
            if (services) {
                photographerServicesQuery.whereIn('service_id', [...services]);
            }

            if (services) {
                const photographersIds = Object.entries(photographerToServicesNotFilteredDict)
                    .filter(([_, photographerServices]) =>

                        photographerServices.filter(({ service_id }) => services.has(service_id)).length >= services.size)
                    .map(([id, _]) => id);
                query.whereIn('users.photographer_id', photographersIds);
            }

            //this query is useful to collect photographer's completed shoots
            //(shoot where photographer task is completed) and count revenues and number of shoots
            let shootsPerPhotographer = await Shoot.query()
                .where('shoots.status', Shoot.statuses.photosUploaded)
                .groupBy('photographer_id')
                .count('shoots.id as shoot_number')
                .sum('photographer_revenue as shoot_total_price_or_revenue')
                .select('photographer_id');

            const { max_shoot_number_value = 0 } = await Shoot.query()
                .where('shoots.status', Shoot.statuses.photosUploaded)
                .groupBy('photographer_id')
                .count('shoots.id as max_shoot_number_value')
                .orderBy('max_shoot_number_value', 'DESC')
                .first() || {};

            max_shoot_number = max_shoot_number_value;

            const { max_cost_or_revenue_value = 0 } = await Shoot.query()
                .where('shoots.status', Shoot.statuses.photosUploaded)
                .groupBy('photographer_id')
                .count('shoots.id as max_shoot_number_value')
                .orderBy('max_shoot_number_value', 'DESC')
                .first() || {};

            max_cost_or_revenue = max_cost_or_revenue_value;
            //it is possible to filter on a interval of number of shoots or revenues
            shootsPerPhotographer = filterMaxMin({ array: shootsPerPhotographer, field: 'shoot_number', interval: shoot_number });
            shootsPerPhotographer = filterMaxMin({
                array: shootsPerPhotographer,
                field: 'shoot_total_price_or_revenue',
                interval: shoot_total_price_or_revenue
            });
            //only photographer that satisfy this filter are selected
            if (shoot_total_price_or_revenue || shoot_number) {
                query.whereIn('photographer_id', shootsPerPhotographer.map(({ photographer_id }) => photographer_id));
            }

            //format the data structure to be easily linked to each photographer
            shootsPerPhotographerDict = shootsPerPhotographer.reduce((acc, { photographer_id, shoot_number, shoot_total_price_or_revenue }) => ({
                ...acc,
                [photographer_id]: { ...(acc[photographer_id] || []), shoot_number, shoot_total_price_or_revenue }
            }), {});
            //last step on picking only photographer ordered by account_verified and count them, 'total' is fundamental for pagination
            query
                .whereNull('client_id')
                .whereNotNull('users.photographer_id');
            total = await query.clone().count('*').first();
            query
                .select(
                    'birthdate as birthdate',
                    'english_level as english_level',
                    'arabic_level as arabic_level',
                    'details as details',
                    'photographer.main_location as main_location',
                    'is_pro as is_pro',
                    'is_internal as is_internal'
                )
                .orderBy('users.status', 'DESC');
        }

        else if (role === 'client') {

            let subclientIds;
            if (searcher_role === 'client') {
                filteredStatuses = [
                    Shoot.statuses.scheduled,
                    Shoot.statuses.photographerAssigned,
                    Shoot.statuses.confirmed,
                    Shoot.statuses.photosUploaded,
                    Shoot.statuses.photosReady,
                    Shoot.statuses.completed
                ];
                subclientIds = await this.getSubClientIdsByClient({ user: searcher, selectId: false });
            }

            //this query is useful to collect client's completed shoots
            //(shoot where clients receive photos) and count cost and number of shoots
            const shootsPerClientQuery = Shoot.query()
                //.whereIn('shoots.status', filteredStatuses)
                .count({
                    shoot_number: raw(`CASE WHEN shoots.status IN (${filteredStatuses.map((_) => '?').join(',')}) THEN 1 END`, [...filteredStatuses])
                })
                .sum({
                    shoot_total_price_or_revenue: raw(`CASE WHEN shoots.status IN (${filteredStatuses.map((_) => '?').join(',')}) 
                        THEN shoots.price 
                        ELSE 0 END`, [...filteredStatuses]
                    )
                });

            //max maxCostOrRevenue can assume two different values. If the searcher is a client (subclientIds not null) the value of the filter is
            //calculated on his own customers, while if the searcher is an admin, the selection must be done
            //on order_id because our intent is to retrieve the value only on maincustomer

            subclientIds ?
                shootsPerClientQuery
                    .whereIn('consumer_client_id', subclientIds)
                    .groupBy('consumer_client_id')
                    .select('consumer_client_id as client_id') :
                shootsPerClientQuery
                    .leftJoinRelated('order')
                    .groupBy('order.client_id')
                    .select('order.client_id as client_id');
            let shootsPerClient = await shootsPerClientQuery;

            const maxShootNumberValueQuery = Shoot.query()
                .whereIn('shoots.status', filteredStatuses)
                .count('shoots.id as max_shoot_number_value')
                .orderBy('max_shoot_number_value', 'DESC')
                .first();

            //max maxShootNumberValue can assume two different values. If the searcher is a client (subclientIds not null) the value of the filter is
            //calculated on his own customers, while if the searcher is an admin, the selection must be done
            //on order_id because our intent is to retrieve the value only on maincustomer
            subclientIds ?
                maxShootNumberValueQuery
                    .whereIn('consumer_client_id', subclientIds)
                    .groupBy('consumer_client_id')
                    .select('consumer_client_id as client_id') :
                maxShootNumberValueQuery
                    .leftJoinRelated('order')
                    .groupBy('order.client_id')
                    .select('order.client_id as client_id');
            const { max_shoot_number_value = 0 } = (await maxShootNumberValueQuery || {});
            max_shoot_number = max_shoot_number_value;

            const maxCostOrRevenueQuery = Shoot.query()
                .whereIn('shoots.status', filteredStatuses)
                .sum('shoots.price as max_cost_or_revenue_value')
                .orderBy('max_cost_or_revenue_value', 'DESC')
                .first();

            //max maxCostOrRevenue can assume two different values. If the searcher is a client (subclientIds not null) the value of the filter is
            //calculated on his own customers, while if the searcher is an admin, the selection must be done
            //on order_id because our intent is to retrieve the value only on maincustomer
            subclientIds ?
                maxCostOrRevenueQuery
                    .whereIn('consumer_client_id', subclientIds)
                    .groupBy('consumer_client_id')
                    .select('consumer_client_id as client_id') :
                maxCostOrRevenueQuery
                    .leftJoinRelated('order')
                    .groupBy('order.client_id')
                    .select('order.client_id as client_id');
            const { max_cost_or_revenue_value = 0 } = (await maxCostOrRevenueQuery || {});
            max_cost_or_revenue = max_cost_or_revenue_value;

            //it is possible to filter on a interval of number of shoots or total cost
            shootsPerClient = filterMaxMin({ array: shootsPerClient, field: 'shoot_number', interval: shoot_number });
            shootsPerClient = filterMaxMin({
                array: shootsPerClient,
                field: 'shoot_total_price_or_revenue',
                interval: shoot_total_price_or_revenue
            });

            const selectedClientIds = shootsPerClient.map(({ client_id }) => client_id);

            //only client that satisfy this filter are selected
            if (shoot_total_price_or_revenue || shoot_number) {
                query.whereIn('client_id', selectedClientIds);
            }

            //format the data structure to be easily linked to each client

            shootsPerClientDict = shootsPerClient.reduce((acc, { client_id, shoot_number, shoot_total_price_or_revenue }) => ({
                ...acc,
                [client_id]: { ...(acc[client_id] || []), shoot_number, shoot_total_price_or_revenue }
            }), {});

            //last step on picking only clients and count them, 'total' is fundamental for pagination
            query
                .whereNull('photographer_id')
                .whereNotNull('client_id');
            total = await query.clone().count('*').first();

            query
                .select(
                    'company_name as company_name',
                    'industry as industry',
                    'country as country',
                    'contact_name as contact_name',
                    'contact_email as contact_email',
                    'contact_phone as contact_phone',
                    'client.main_location as main_location',
                    'client.registration_guid as registration_guid',
                    'client.parent_client_id as parent_client_id'
                )
                .orderBy('users.status', 'DESC');
        }
        else if (role === 'admin') {
            //query for admin has only role filter and total for pagination
            query
                .whereNull('photographer_id')
                .whereNull('client_id');
            total = await query.clone().count('*').first();
        }

        query
            .select('users.id',
                'users.name',
                'users.email',
                'users.phone',
                'users.photographer_id',
                'users.client_id',
                'users.editor_id',
                'users.status'
            )
            .orderBy('users.status', 'DESC');

        //limit and offset are related to pagination that now is optional, TBD: align for defaults
        if (limit && offset) {
            query
                .limit(limit)
                .offset(offset);
        }

        const selectedClientIds = (await query.clone()).map(({ client_id }) => client_id);

        let subClients;
        let subClientsDict;
        if (role === 'client' && searcher_role === 'admin') {

            subClients = await Shoot.query()
                .leftJoinRelated('[consumer, order]')
                .count({
                    shoot_number: raw(`CASE WHEN shoots.status IN (${filteredStatuses.map((_) => '?').join(',')}) THEN 1 END`, [...filteredStatuses])
                })
                .sum({
                    shoot_total_price_or_revenue: raw(`CASE WHEN shoots.status IN (${filteredStatuses.map((_) => '?').join(',')}) 
                    THEN shoots.price 
                    ELSE 0 END`, [...filteredStatuses])
                })
                .whereIn('order.client_id', selectedClientIds)
                .whereNotIn('consumer_client_id', selectedClientIds)
                .groupBy('consumer_client_id')
                .select('consumer_client_id')
                .select(Client.query()
                    .where('id', ref('consumer_client_id'))
                    .select('parent_client_id')
                    .as('parent_client_id')
                );

            const subClientsDetails = await User
                .query()
                .leftJoinRelated('client')
                .whereIn('client.parent_client_id', selectedClientIds)
                .select(
                    'company_name as company_name',
                    'industry as industry',
                    'country as country',
                    'contact_name as contact_name',
                    'contact_email as contact_email',
                    'contact_phone as contact_phone',
                    'client.main_location as main_location',
                    'client.registration_guid as registration_guid',
                    'client.parent_client_id as parent_client_id'
                )
                .select('users.id',
                    'users.name',
                    'users.email',
                    'users.phone',
                    'users.photographer_id',
                    'users.client_id',
                    'users.editor_id',
                    'users.status'
                );

            const result = subClientsDetails
                .map((subClientList) => ({ ...subClientList, ...subClients.find((sc) => sc.consumer_client_id === subClientList.client_id) }))
                .map(({ consumer_client_id, shoot_number, shoot_total_price_or_revenue, ...rest }) => ({
                    ...rest,
                    shoot_number: shoot_number || '0',
                    shoot_total_price_or_revenue: shoot_total_price_or_revenue || '0'
                }));
            subClientsDict = groupBy(result, 'parent_client_id');
        }

        //users are formatted with all the informations collected
        const users = (await query).map((user) => ({
            ...user,
            ...(role === 'photographer' && {
                services: (photographerToServicesNotFilteredDict[user.photographer_id] || []).map(({ service_id, rate }) => ({
                    service_id,
                    rate
                }))
            } || {}),
            ...(role === 'photographer' && (shootsPerPhotographerDict[user.photographer_id] || { shoot_number: 0, shoot_total_price_or_revenue: 0 })),
            ...(role === 'client' && (shootsPerClientDict[user.client_id] || { shoot_number: 0, shoot_total_price_or_revenue: 0 })),
            ...(role === 'client' && searcher_role === 'admin' && ({ subclients: subClientsDict[user.client_id] || [] })),
            role
        }));

        return { users, total: total.count, max_cost_or_revenue, max_shoot_number };

    }

    /**
     * Get a user (from the db) given its photographer id.
     *
     * @param {string} photographer_id
     *  The photographer_id of the user.
     *
     * @return {Object} The db user data.
     */
    async getPhotographerById({ photographer_id }) {

        const { User } = this.server.models();

        if (!photographer_id) {
            throw new ValidationError(`Photographer id is invalid (${photographer_id})`);
        }

        const user = await User.query().whereNull('client_id').where('photographer_id', photographer_id).first();

        if (!user) {
            throw new NotFoundError('Photographer not found');
        }

        return { user };
    }

    /**
     * Get a user (from the db) given its client id.
     *
     * @param {string} client_id
     *  The client_id of the user.
     *
     * @return {Object} The db user data.
     */
    async getClientById({ client_id }) {

        const { User } = this.server.models();

        const user = await User.query().whereNull('photographer_id').where('client_id', client_id).first();

        if (!user) {
            throw new NotFoundError('Client not found');
        }

        return { user };
    }

    /**
     * Get a user (from the db) given its id.
     *
     * @param {string} id
     *  The id of the user.
     *
     * @return {Object} The db user data.
     */
    async getUser({ id }) {

        const { User } = this.server.models();

        const user = await User.query().findById(id);

        if (!user) {
            throw new NotFoundError('User not found');
        }

        return { ...user, role: user.role };
    }

    async getUserByAffectedRole( { id, role } ) {

        switch (role) {
            case 'photographer':
                // eslint-disable-next-line no-case-declarations
                const photographer = await this.getPhotographerById({ photographer_id: id });
                return photographer.user;
            case 'client':
            default:
                return await this.getUser({ id });
        }
    }

    /**
     * Patch user (in the db) given id
     *
     * @param {Object} affectedUser
     *  The User db object to be updated
     *
     * @param {string} userToBeUpdatedRole
     *  The role of the user to be updated
     *
     * @param {Object} additionalInformations
     *  The additional informations to be updated
     *
     * @param {Object} user
     *  The user data to be updated
     *
     * @param {Object} status
     *  The status to be updated
     *
     * @param {Object} services
     *  The services to be updated
     *
     * @return {Object} user
     *  The updated user.
     */
    async updateUser({ affectedUser = {}, userToBeUpdatedRole, additionalInformations, user, status, services }) {

        const { User, Photographer, Client, PhotographerService } = this.server.models();

        const trx = await User.startTransaction();
        try {
            if (userToBeUpdatedRole === 'photographer' && affectedUser.photographer_id) {
                await Photographer.query(trx)
                    .patchAndFetchById(affectedUser.photographer_id, {
                        ...additionalInformations,
                        last_update: Date.now()
                    });
            }

            if (userToBeUpdatedRole === 'client' && affectedUser.client_id) {
                await Client.query(trx)
                    .patchAndFetchById(affectedUser.client_id, {
                        ...additionalInformations,
                        last_update: Date.now()
                    });
            }

            await User.query(trx).patchAndFetchById(affectedUser.id, {
                ...user,
                last_update: Date.now(),
                ...(
                    (userToBeUpdatedRole === 'photographer' ||
                        userToBeUpdatedRole === 'client' ||
                        userToBeUpdatedRole === 'editor') &&
                    status !== undefined ? { status } : {}
                )
            });

            if (services && services.length > 0 && affectedUser.photographer_id) {
                await PhotographerService.query(trx).where('photographer_id', affectedUser.photographer_id).delete();
                await PhotographerService.query(trx).insert(services.map((service) => ({
                    photographer_id: affectedUser.photographer_id,
                    ...service
                })));
            }

            await trx.commit();
            return await this.getUser({ id: affectedUser.id });
        }
        catch (err) {

            await trx.rollback();
            throw err;
        }
    }

    /**
     * Syncronize payment details for clients.
     *
     * @param {Object} user
     *  The object related to user
     *
     * @param {Object} billing_address
     *  The object related to the billing address
     *
     * @param {string} tax_id
     *  The tax id number
     *
     * @return {Object} paymentDetails
     *  The db payment details data.
     */
    async synchronizePaymentData({ user, billing_address, tax_id }) {

        const { User, Client, PaymentDetail } = this.server.models();
        const { paymentService } = this.server.services();
        const client = await Client.query().findById(user.client_id);

        let paymentDetails;
        paymentDetails = await User.query().findById(user.id)
            .leftJoinRelated('[client.paymentDetail]')
            .select('client:paymentDetail.*');

        const { country, company_name } = client;

        if (paymentDetails.type !== 'stripe') {
            const customer = await paymentService.createStripeCustomer({
                email: user.email,
                name: company_name
            });

            paymentDetails = await PaymentDetail.query().patchAndFetchById(
                paymentDetails.id,
                {
                    type: 'stripe',
                    details: {
                        stripe_customer_id: customer.id
                    }
                });
        }

        await paymentService.synchronizeStripeCustomer({
            stripe_customer_id: paymentDetails.details.stripe_customer_id,
            tax_id,
            country,
            billing_address,
            name: company_name
        });

        return { paymentDetails };
    }

    /**
     * Syncronize payment details for clients.
     *
     * @param {Object} user
     *  The object related to user
     *
     * @param {Object} billing_address
     *  The object related to the billing address
     *
     * @param {string} tax_id
     *  The tax id number
     *
     * @return {Object} paymentDetails
     *  The db payment details data.
     */
    async getPaymentDetails({ user }) {

        const { User } = this.server.models();

        const paymentDetails = await User.query().findById(user.id)
            .leftJoinRelated('[client.paymentDetail]')
            .where('client:paymentDetail.type', 'stripe')
            .select('client:paymentDetail.*');

        if (!paymentDetails) {
            throw new NotFoundError('Photographer not found');
        }

        return paymentDetails;
    }

    /**
     * Get a formatted user (from the db) given its id.
     *
     * @param {string} id
     *  The id of the user.
     *
     * @return {Object} The db user data with client or photographer data.
     */
    async getFormattedUser({ id, role }) {

        const { User } = this.server.models();

        const query = User.query().findById(id);

        query
            .leftJoinRelated('[photographer, client]')
            .select('users.*');

        if (role === 'photographer') {
            query
                .whereNull('client_id')
                .whereNotNull('photographer_id')
                .select(
                    'birthdate as birthdate',
                    'english_level as english_level',
                    'arabic_level as arabic_level',
                    'details as details',
                    'photographer.main_location as main_location',
                    'is_internal as is_internal'
                );
        }
        else if (role === 'client') {
            query
                .whereNull('photographer_id')
                .whereNotNull('client_id')
                .select(
                    'company_name as company_name',
                    'industry as industry',
                    'country as country',
                    'contact_name as contact_name',
                    'contact_email as contact_email',
                    'contact_phone as contact_phone',
                    'client.main_location as main_location',
                    'client.parent_client_id as parent_client_id',
                    'client.registration_guid as registration_guid',
                    raw('CASE WHEN company_name IS NULL OR company_name = \'\' THEN false ELSE true END as has_company_name')
                );
        }
        else if (role === 'admin') {
            query
                .whereNull('photographer_id')
                .whereNull('client_id');
        }

        const user = await query;

        if (!user) {
            throw new NotFoundError('Unable to find the user');
        }

        return { ...user, role };
    }

    /**
     * Returns the existence of a user with a specified email
     *
     * @param {string} email
     *  The email to be found.
     *
     * @return {Boolean} email existence.
     */
    async emailChecker({ email }) {

        const { User } = this.server.models();

        const user = await User.query().where({ email });

        return user.length > 0;
    }

    /**
     * Returns the client by its guid
     *
     * @param {string} guid
     *  The guid of the user to be found.
     *
     * @return {Object} the formatted client.
     */
    async getClientByGuid({ guid }) {

        const { User } = this.server.models();

        const client = await User.query().leftJoinRelated('client').where('registration_guid', guid).first();

        if (!client) {
            throw new NotFoundError(`Unable to find the client with specified guid: ${guid}`);
        }

        return this.getFormattedUser({ id: client.id, role: 'client' });
    }

    /**
     * Returns the array of subclient user ids that belongs to a user
     *
     * @param {Object} user
     *  The object related to the user.
     * @param {Boolean} selectId
     *  True if ids are requested, otherwise client_id
     *
     * @return {Array} the array of subclient user ids.
     */
    async getSubClientIdsByClient({ user, selectId = true }) {

        const { User } = this.server.models();
        const subClients = await User.query()
            .leftJoinRelated('client')
            .where('client.parent_client_id', user.client_id);

        return selectId ? subClients.map(({ id }) => id) : subClients.map(({ client_id }) => client_id);

    }

    /**
 * Retrieves subclients associated with a main account, including their subClientRole from Auth0 and company name.
 *
 * @param {Object} options - Options for the function.
 * @param {number} options.parentId - The ID of the main account.
 * @return {Promise<Array>} - A promise that resolves to an array of subclients with their roles and company names.
 */
    async getSubClients({ parentId }) {

        const { User } = this.server.models();
        // Query to find all subclients associated with the main account
        const subClients = await User.query()
            .leftJoinRelated('client')
            .where('client.parent_client_id', parentId)
            .select('users.*',
                'client.company_name',
                'client.industry',
                'client.country',
                'client.contact_name',
                'client.contact_email',
                'client.contact_phone',
                'client.main_location'
            );

        if (!subClients || subClients.length === 0) {
            return [];
        }

        // Fetch Auth0 user data for each subclient
        const subClientsWithRoles = await Promise.all(subClients.map(async (subclient) => {

            try {
                const auth0User = await this.getAuth0UserByEmail({ email: subclient.email });
                return {
                    ...subclient,
                    subClientRole: auth0User.user_metadata?.subClientRole || null,
                    role: 'client'
                };
            }
            catch (error) {
                if (error instanceof NotFoundError) {
                    this.server.logger.warn({ userId: subclient.id, email: subclient.email }, 'Auth0 user not found for subclient');
                }
                else {
                    this.server.logger.error({ userId: subclient.id, email: subclient.email, error }, 'Failed to fetch Auth0 user data for subclient');
                }

                return {
                    ...subclient,
                    subClientRole: null,
                    role: 'client'
                };
            }
        }));

        return subClientsWithRoles;
    }

    /**
     * Deletes (status = deleted) subclients and assigns their shoot and report user to mainclient
     *
     * @param {Object} affectedUser
     *  The affected user
     *
     * @return {Boolean} true if action is successfully completed.
     */
    async deleteSubClients({ affectedUser }) {

        const { shootService, reportUserService } = this.server.services();
        const { User, Client } = this.server.models();

        //search for subusers
        const subUsers = await User.query()
            .joinRelated('client')
            .where('client.parent_client_id', affectedUser.client_id);

        if (subUsers.length) {
            try {
                const userIds = subUsers.map(({ id }) => id);
                const clientIds = subUsers.map(({ client_id }) => client_id);

                //assign all shoot consumed by subclients to main client
                await shootService.assignSubClientsShootsToMainClient({ affectedUser });

                //assign all reports generated by subclients action to main client
                await reportUserService.assignSubClientsReportUserToMainClient({ affectedUser, subclientsUserIds: userIds });

                await User.query().whereIn('id', userIds).delete();
                await Client.query().whereIn('id', clientIds).delete();

                for (const { email } of subUsers) {
                    this.deleteAuth0User({ email });
                }

                return true;
            }
            catch (error) {
                this.server.logger.warn({ context: 'userService.deleteSubClients', error, affectedUser });
                return false;
            }
        }
    }

    /**
 * Deletes (status = deleted) a subclient and assigns their shoots and report user to mainclient
 * based on subClientId or subUserId
 *
 * @param {Object} options
 *  - {number} subClientId: The ID of the subclient's client
 *  - {number} subUserId: The ID of the subclient's user
 *  - {Object} affectedUser: The affected user (main client)
 *
 * @return {Boolean} true if action is successfully completed.
 */
    async deleteSubClientById({ subUser,  affectedUser }) {

        const { shootService, reportUserService } = this.server.services();
        const { User, Client } = this.server.models();

        try {
            // Fetch the subclient's details

            if (!subUser ) {
                throw new Error('Sub user not found.');
            }

            // Assign all shoots consumed by the subclient to the main client
            await shootService.assignSubClientShootsToMainClient({ affectedUser, subUser });

            // Assign all reports generated by the subclient to the main client
            await reportUserService.assignSubClientsReportUserToMainClient({ affectedUser, subclientsUserIds: [subUser.id] });

            // Delete the subclient and their associated client
            await User.query().deleteById(subUser.id);
            await Client.query().deleteById(subUser.client_id);

            this.deleteAuth0User({ email: subUser.email });

            return true;
        }
        catch (error) {
            throw new Error(`Error from UserService.deleteSubClientById() :${error}`);
        }
    }

    /**
     *Return parent client id
     *
     * @param {String} id of user
     *
     * @return {Number} is child
     */
    async getParentClientId({ userId }) {

        const { User } = this.server.models();

        const client = await User.query().leftJoinRelated('client').select('*').where('users.id', '=', userId).first();

        return client?.parent_client_id;
    }

    /**
     * Send user to CustomerIO
     * @param userId
     * @returns {Promise<void>}
     */
    async sendUserToCustomerIO({ userId }) {

        try {
            const messageParams = {
                QueueUrl: this.enrichmentQueueUrl,
                MessageBody: JSON.stringify({
                    user_id: userId,
                    action: 'SYNC_USER_CUSTOMERIO',
                    timestamp: new Date().toISOString()
                }),
                MessageAttributes: {
                    user_id: {
                        DataType: 'String',
                        StringValue: userId.toString()
                    }
                }
            };

            await this.sqs.sendMessage(messageParams).promise();
        }
        catch (error) {
            throw new Error(`Error from UserService.sendUserToCustomerIO() :${error}`);
        }
    }
};
