/* eslint-disable @hapi/capitalize-modules */
/* eslint-disable @hapi/scope-start */
'use strict';

const Schmervice = require('schmervice');
const { NotFoundError, UnauthorizedError } = require('@hapi/boom');
const AWS = require('aws-sdk');
const { OpenAI } = require('openai');
const pgvector = require('pgvector');
const Knexfile = require('../../knexfile.js');
const knex = require('knex')(Knexfile);
const path = require('path');

const ASPECT_RATIO_THRESHOLD = 1.5;
const TALL_RATIO_THRESHOLD = 0.7;
const SUPPORTED_IMAGE_TYPES = ['.jpeg', '.jpg', '.png'];
const SUPPORTED_VIDEO_TYPES = ['.mp4', '.mov'];

/**
 * Service for managing content-related operations (images, videos, and raw images).
 * Handles batch operations, status updates, and approval workflows.
 */
module.exports = class ContentService extends Schmervice.Service {

    /**
     * Initialize the service.
     * Sets up required dependencies and configurations.
     */
    initialize() {
        this.Image = this.server.models().Image;
        this.Video = this.server.models().Video;
        this.RawImage = this.server.models().RawImage;
        this.Shoot = this.server.models().Shoot;
        this.notificationService = this.server.services().notificationService;
        this.processedBucket = this.server.settings.app.amazon.processed_photos_bucket_name;
        this.watermarksBucket = this.server.settings.app.amazon.watermarks_bucket_name;

        this.openai = new OpenAI({
            apiKey: this.server.settings.app.openai.api_key
        });

        this.sqs = new AWS.SQS({
            accessKeyId: this.server.settings.app.amazon.aws_id,
            secretAccessKey: this.server.settings.app.amazon.aws_secret,
            region: this.server.settings.app.amazon.aws_region
        });

        this.queueUrl = this.server.settings.app.amazon.sqs_download_queue_url;
        this.enrichmentQueueUrl = this.server.settings.app.amazon.enrichment_sqs_queue_url;
        this.contentProcessingQueueUrl = this.server.settings.app.amazon.content_processing_queue_url;
        this.cfDomain = this.server.settings.app.amazon.cloudfront_url;
    }

    async createEmbedding(text) {
        const response = await this.openai.embeddings.create({
            input: text,
            model: 'text-embedding-3-small'
        });

        return response.data[0].embedding;
    }

    /**
     * Batch creates or updates videos.
     *
     * @param {Array} videoDataArray - Array of video data objects containing url and other properties
     * @returns {Promise<Array>} Array of newly created video records
     * @throws {Error} If validation fails or database operation errors
     */
    /**
     * Unified content creation function that handles both images and videos.
     * Supports multiple file types: jpeg, png, mp4, mov.
     *
     * @param {Array} contentDataArray - Array of content data objects
     * @param {string} shootId - ID of the shoot
     * @param {number} userId - ID of the user creating the content
     * @returns {Promise<{images: Array, videos: Array}>} Newly created content records
     * @throws {Error} If validation fails or operation errors
     */
    async createContent(contentDataArray, shootId = null, userId = null, uploaded_by = null) {
        if (!Array.isArray(contentDataArray) || contentDataArray.length === 0) {
            throw new Error('No content provided for processing');
        }

        // Separate content by type
        const { images, videos } = this._separateContentByType(contentDataArray);

        // Process images and videos in parallel
        const [processedImages, processedVideos] = await Promise.all([
            images.length > 0 ? this.createImagesBatch(images, shootId, userId, uploaded_by) : [],
            videos.length > 0 ? this.createVideosBatch(videos, shootId, userId, uploaded_by) : []
        ]);

        return {
            images: processedImages,
            videos: processedVideos
        };
    }

    /**
     * Separates content array into images and videos based on file extension.
     * @private
     */
    _separateContentByType(contentDataArray) {
        const images = [];
        const videos = [];

        for (const content of contentDataArray) {
            const ext = path.extname(content.filename).toLowerCase();

            if (SUPPORTED_IMAGE_TYPES.includes(ext)) {
                images.push(content);
            }
            else if (SUPPORTED_VIDEO_TYPES.includes(ext)) {
                videos.push(content);
            }
            else {
                throw new Error(`Unsupported file type: ${ext}`);
            }
        }

        return { images, videos };
    }

    async createVideosBatch(videoDataArray, shootId, userId, uploaded_by = null) {

        if (!Array.isArray(videoDataArray) || videoDataArray.length === 0) {
            throw new Error('No videos provided for processing');
        }

        await this.refreshDownloadQueue(shootId);
        const result = await this.Video.transaction(async (trx) => {
            try {
                // Validate URLs
                const urls = this._validateUrls(videoDataArray);

                // Find existing videos
                const existingVideos = await this.Video.query(trx).whereIn('url', urls);

                // Enrich videoDataArray with shoot_id, user_id, and uploaded_by
                const enrichedVideoDataArray = videoDataArray.map((data) => ({
                    ...data,
                    shoot_id: shootId,
                    user_id: userId,
                    uploaded_by
                }));

                const { updatePromises, insertData } = this._processVideoData(
                    enrichedVideoDataArray, // Pass enriched data
                    existingVideos,
                    trx
                );

                if (updatePromises.length === 0 && insertData.length === 0) {
                    throw new Error('No updates or inserts to be made');
                }

                // Execute updates and inserts
                await Promise.all(updatePromises);
                const newVideos = insertData.length > 0
                    ? await this.Video.query(trx).insert(insertData)
                    : [];

                return newVideos;
            }
            catch (err) {
                throw new Error(`Failed to batch process videos: ${err.message}`);
            }
        });

        if (result.length > 0) {
            const messageParams = {
                QueueUrl: this.contentProcessingQueueUrl,
                MessageBody: JSON.stringify({
                    shootId: shootId ? shootId : null,
                    userId: userId ? userId : null,
                    action: 'PROCESS_VIDEO_CONTENT',
                    timestamp: new Date().toISOString(),
                    videoIds: result.map((video) => video.id)
                }),
                MessageAttributes: {
                    shootId: {
                        DataType: 'String',
                        StringValue: shootId ? shootId.toString() : `0`
                    }
                }
            };

            await this.sqs.sendMessage(messageParams).promise();
        }

        return result;
    }

    async refreshDownloadQueue(shootId) {

        const { DownloadRequestJob } = this.server.models();

        // Handle download request cleanup in a separate transaction
        try {
            const downloadTrx = await DownloadRequestJob.startTransaction();
            try {
                const { storageService, downloadContentService } = this.server.services();
                const bucket = this.server.settings.app.amazon.processed_photos_bucket_name;
                const filepaths = [`${shootId}/processed-content.zip`];

                // Delete processed content file
                await storageService.deleteObjects({
                    bucket,
                    filepaths
                });

                // Remove existing download job
                await downloadContentService._removeExistingJob({
                    shootId,
                    downloadTrx
                });

                await downloadTrx.commit();
            }
            catch (error) {
                await downloadTrx.rollback();
                throw error;
            }
        }
        catch (err) {
            throw new Error(`Failed to cleanup download artifacts: ${err.message}`);
        }
    }

    /**
     * Batch creates or updates images with notification support.
     *
     * @param {Array} imageDataArray - Array of image data objects containing url and other properties
     * @param {string} shootId - ID of the shoot these images belong to
     * @param userId
     * @param uploaded_by
     * @returns {Promise<Array>} Array of newly created image records
     * @throws {Error} If validation fails or database operation errors
     */
    async createImagesBatch(imageDataArray, shootId = null,
        userId = null, uploaded_by = null) {

        if (!Array.isArray(imageDataArray) || imageDataArray.length === 0) {
            throw new Error('No images provided for processing');
        }

        // Only refresh the download queue for shoot-related uploads
        if (shootId) {
            await this.refreshDownloadQueue(shootId);
        }

        const images = await this.Image.transaction(async (trx) => {
            try {
                // Validate URLs
                const urls = this._validateUrls(imageDataArray);

                // Find existing images
                const existingImages = await this.Image.query(trx).whereIn('url', urls);

                // Prepare data for processing
                const processData = imageDataArray.map((data) => ({
                    ...data,
                    shoot_id: shootId,
                    user_id: userId,
                    uploaded_by
                }));

                const { updatePromises, insertData } =
                    this._processImageData(processData, existingImages, trx);

                if (updatePromises.length === 0 && insertData.length === 0) {
                    throw new Error('No updates or inserts to be made');
                }

                // Execute updates and inserts
                await Promise.all(updatePromises);
                const newImages = insertData.length > 0
                    ? await this.Image.query(trx).insert(insertData)
                    : [];

                return newImages;
            }
            catch (err) {
                throw new Error(`Failed to batch process images: ${err.message}`);
            }
        });

        if (images.length > 0) {
            const decoratedImages = images.map((image) => ({
                id: image.id,
                url: image.url,
                preview_url: image.preview_url,
                placeholder_url: image.placeholder_url,
                filepath: shootId ? `${shootId}/${image.filepath}` : image.filepath
            }));
            await this.publishContentEnrichmentMessage(decoratedImages);
        }

        return images;
    }

    /**
     * Batch creates or updates raw images.
     *
     * @param {Array} rawsDataArray - Array of raw image data objects
     * @returns {Promise<Array>} Array of newly created raw image records
     * @throws {Error} If validation fails or database operation errors
     */
    async createRawsBatch(rawsDataArray) {

        if (!Array.isArray(rawsDataArray) || rawsDataArray.length === 0) {
            throw new Error('No raw images provided for processing');
        }

        const trx = await this.RawImage.startTransaction();

        try {
            const urls = this._validateUrls(rawsDataArray);
            const existingRawImages = await this.RawImage.query(trx).whereIn('url', urls);

            const { updatePromises, insertData } = this._processRawImageData(
                rawsDataArray,
                existingRawImages,
                trx
            );

            await Promise.all(updatePromises);
            const newRawImages = await this.RawImage.query(trx).insert(insertData);

            await trx.commit();
            return newRawImages;
        }
        catch (err) {
            await trx.rollback();
            throw new Error(`Failed to batch process raw images: ${err.message}`);
        }
    }

    /**
     * Updates the status and feedback for an image.
     *
     * @param {Object} params - Parameters object
     * @param {string} params.imageId - ID of the image to update
     * @param {string} params.newStatus - New status to set
     * @param {string} [params.feedback] - Optional feedback message
     * @returns {Promise<Object>} Updated image record
     * @throws {Error} If image not found or update fails
     */
    async updateImageStatus({ imageId, newStatus, feedback }) {

        const image = await this.Image.query().findById(imageId);

        if (!image) {
            throw new NotFoundError(`Image with ID ${imageId} not found`);
        }

        return await image.$query().patch({
            status: newStatus,
            feedback: feedback !== undefined ? feedback : image.feedback
        });
    }

    /**
     * Approves all remaining pending images for a shoot.
     *
     * @param {string} shootId - ID of the shoot
     * @param {Object} user - User performing the approval
     * @returns {Promise<Array>} Array of updated image records
     * @throws {UnauthorizedError} If user doesn't have required permissions
     * @throws {NotFoundError} If shoot or images not found
     */
    async approveRemainingImages(shootId, user) {

        await this._validateUserAndShoot(user, shootId);

        const pendingImages = await this.Image.query()
            .where('shoot_id', shootId)
            .andWhere('status', 'pending');

        if (pendingImages.length === 0) {
            throw new NotFoundError(`No pending images found for shoot ID ${shootId}`);
        }

        return await this._updateImagesStatus(pendingImages, 'approved');
    }

    /**
     * Bulk approves all images for a shoot.
     *
     * @param {string} shootId - ID of the shoot
     * @param {Object} user - User performing the approval
     * @returns {Promise<Array>} Array of updated image records
     * @throws {UnauthorizedError} If user doesn't have required permissions
     * @throws {NotFoundError} If shoot or images not found
     */
    async bulkApproveImages(shootId, user) {

        await this._validateUserAndShoot(user, shootId);

        const images = await this.Image.query()
            .where('shoot_id', shootId);

        if (images.length === 0) {
            throw new NotFoundError(`No images found for shoot ID ${shootId}`);
        }

        return await this._updateImagesStatus(images, 'approved');
    }

    /**
     * Updates the status and notes for a raw image.
     *
     * @param {Object} params - Parameters object
     * @param {string} params.imageId - ID of the raw image to update
     * @param {string} params.newStatus - New status to set
     * @param {string} [params.notes] - Optional notes
     * @returns {Promise<Object>} Updated raw image record
     * @throws {Error} If raw image not found or update fails
     */
    async updateRawImageStatus({ imageId, newStatus, notes }) {

        const rawImage = await this.RawImage.query().findById(imageId);

        if (!rawImage) {
            throw new NotFoundError(`Raw image with ID ${imageId} not found`);
        }

        return await rawImage.$query().patch({
            status: newStatus,
            notes: notes !== undefined ? notes : rawImage.notes
        });
    }

    // Private helper methods

    /**
     * Validates URLs in data array.
     * @private
     */
    _validateUrls(dataArray) {

        return dataArray.map((data) => {

            if (!data.url) {
                throw new Error('Some items have invalid URLs');
            }

            return data.url;
        });
    }

    /**
     * Processes video data for batch operations.
     * @private
     */
    _processVideoData(videoDataArray, existingVideos, trx) {

        const updatePromises = [];
        const insertData = [];

        for (const data of videoDataArray) {
            const existingVideo = existingVideos.find((v) => v.url === data.url);
            if (existingVideo && existingVideo.status === 'resubmitted') {
                updatePromises.push(
                    this.Video.query(trx)
                        .patch({ status: 'pending' })
                        .where('id', existingVideo.id)
                );
            }
            else if (!existingVideo) {
                insertData.push(data);
            }
        }

        return { updatePromises, insertData };
    }

    /**
     * Processes image data for batch operations.
     * @private
     */
    _processImageData(imageDataArray, existingImages, trx) {

        const updatePromises = [];
        const insertData = [];
        let notificationRequired = false;

        for (const data of imageDataArray) {
            // Remove duration property from image data
            const dataWithoutDuration = Object.assign({}, data);
            delete dataWithoutDuration.duration;

            const existingImage = existingImages.find((img) => img.url === dataWithoutDuration.url);
            if (existingImage && existingImage.status === 'resubmitted') {
                notificationRequired = true;
                updatePromises.push(
                    this.Image.query(trx)
                        .patch({ status: 'pending' })
                        .where('id', existingImage.id)
                );
            }
            else if (!existingImage) {
                insertData.push(dataWithoutDuration);
            }
        }

        return { updatePromises, insertData, notificationRequired };
    }

    /**
     * Processes raw image data for batch operations.
     * @private
     */
    _processRawImageData(rawsDataArray, existingRawImages, trx) {

        const updatePromises = [];
        const insertData = [];

        for (const data of rawsDataArray) {
            const existingRawImage = existingRawImages.find((img) => img.url === data.url);
            if (existingRawImage) {
                updatePromises.push(
                    this.RawImage.query(trx)
                        .patch({ status: 'pending' })
                        .where('id', existingRawImage.id)
                );
            }
            else {
                insertData.push({
                    shoot_id: data.shoot_id,
                    url: data.url,
                    placeholder_url: data.placeholder_url,
                    preview_url: data.preview_url,
                    filename: data.filename,
                    status: data.status,
                    folder: data.folder || '/',
                    filepath: data.filepath,
                    aspect_ratio: data.aspect_ratio,
                    notes: null
                });
            }
        }

        return { updatePromises, insertData };
    }

    /**
     * Sends reupload notification.
     * @private
     */
    async _sendReuploadNotification(user, shootId) {

        await this.notificationService.sendNotification({
            type: 'clientImageReuploaded',
            target: user,
            metaData: {
                message: 'Some images have been re-uploaded.',
                shoot_id: shootId
            }
        });
    }

    /**
     * Validates user permissions and shoot existence.
     * @private
     */
    async _validateUserAndShoot(user, shootId) {

        if (user.role !== 'client' && user.role !== 'admin') {
            throw new UnauthorizedError('Action is not allowed for this user');
        }

        const shoot = await this.Shoot.query().findById(shootId);
        if (!shoot) {
            throw new NotFoundError(`Shoot with ID ${shootId} not found`);
        }
    }

    /**
     * Updates status for multiple images.
     * @private
     */
    async _updateImagesStatus(images, status) {

        const updates = images.map((image) => ({
            ...image,
            id: image.id,
            status,
            feedback: null
        }));

        return await this.Image.query().upsertGraph(updates);
    }

    /**
 * Get content (images and videos) with semantic search and cursor-based pagination.
 * Combines traditional filtering with vector similarity search when search query is provided.
 *
 * @param {Object} params - Query parameters
 * @param {Object} params.user - User object containing role and client IDs
 * @param {('admin'|'client')} params.role - User role for access control
 * @param {number} [params.limit=20] - Number of items per page
 * @param {string} [params.cursor=null] - Base64 encoded pagination cursor
 * @param {Date} [params.starts_at] - Filter by shoot start date
 * @param {Date} [params.ends_at] - Filter by shoot end date
 * @param {number[]} [params.services] - Array of service IDs to filter by
 * @param {Array<'landscape'|'portrait'|'square'>} [params.aspectRatios] - Filter by aspect ratios
 * @param {string} [params.metadataSearchQuery] - Exact text search query for shoot metadata search
 * @param {string} [params.visualSearchQuery] - Natural language search query for semantic search
 * @param {number} [params.clientId] - Client ID to filter by (admin only)
 * @param {number} [params.collectionId] - Collection ID to filter by
 * @returns {Promise<{
 *   images: Array,
 *   videos: Array,
 *   pagination: {
 *     hasMore: boolean,
 *     nextCursor: string|null,
 *     total: number
 *   }
 * }>}
 */
    async getContent({
        user,
        role,
        limit = 40,
        cursor = null,
        starts_at,
        ends_at,
        services,
        aspectRatios = [],
        metadataSearchQuery = null,
        visualSearchQuery = null,
        clientId,
        contentTypes = ['image', 'video'],
        collectionId
    }) {

        const { Image, Video } = this.server.models();
        const knex = Image.knex();

        try {
            // Parse cursor if provided
            let cursorValues = null;
            if (cursor) {
                try {
                    cursorValues = JSON.parse(Buffer.from(cursor, 'base64').toString());
                }
                catch (error) {
                    throw new Error('Invalid cursor format');
                }
            }

            // Only proceed if at least one content type is requested
            if (contentTypes.length === 0) {
                return { content: [], pagination: { hasMore: false, nextCursor: null, total: 0 } };
            }

            // Determine how many items to fetch for each content type
            // For simplicity, split the limit evenly between content types
            const typesCount = contentTypes.length;
            const itemsPerType = Math.ceil(limit / typesCount);

            // Arrays to store the results
            let imageResults = [];
            let videoResults = [];

            // Get counts and content in parallel
            const [countPromise, contentPromise] = await Promise.all([
                // Get counts
                Promise.all([
                    contentTypes.includes('image')
                        ? this._buildCountQuery(Image, user, role, clientId, collectionId, starts_at,
                            ends_at, services, aspectRatios, metadataSearchQuery)
                            .then((result) => parseInt(result[0].count) || 0)
                        : Promise.resolve(0),
                    contentTypes.includes('video')
                        ? this._buildCountQuery(Video, user, role, clientId, collectionId, starts_at,
                            ends_at, services, aspectRatios, metadataSearchQuery)
                            .then((result) => parseInt(result[0].count) || 0)
                        : Promise.resolve(0)
                ]),

                // Get content
                Promise.all([
                    // Fetch images if requested
                    contentTypes.includes('image') ? (async () => {
                        const imagesQuery = this._buildBaseContentQuery(Image, user, role, clientId, collectionId);
                        this._applyContentFilters(imagesQuery, Image, starts_at, ends_at, services, aspectRatios, metadataSearchQuery);

                        // Add content_type identifier
                        const imageFields = this._getImageSelectFields(collectionId);
                        imagesQuery.select([
                            ...imageFields,
                            knex.raw('\'image\' as content_type')
                        ]);

                        // Apply visual search if requested
                        if (visualSearchQuery) {
                            const embedding = await this.createEmbedding(visualSearchQuery);
                            imagesQuery.select(knex.raw('small_embedding <-> ?::vector as distance', [pgvector.toSql(embedding)]));
                            imagesQuery.orderBy('distance');
                        }
                        else {
                            imagesQuery.orderBy('images.id', 'desc');
                        }

                        // Apply cursor-based pagination if available
                        if (cursorValues && cursorValues.lastImageId) {
                            imagesQuery.where('images.id', '<', cursorValues.lastImageId);
                        }

                        // Get items + 1 to check if there are more
                        imagesQuery.limit(itemsPerType + 1);
                        return await imagesQuery;
                    })() : Promise.resolve([]),

                    // Fetch videos if requested
                    contentTypes.includes('video') ? (async () => {
                        const videosQuery = this._buildBaseContentQuery(Video, user, role, clientId, collectionId);
                        this._applyContentFilters(videosQuery, Video, starts_at, ends_at, services, aspectRatios, metadataSearchQuery);

                        // Add content_type identifier
                        const videoFields = this._getVideoSelectFields(collectionId);
                        videosQuery.select([
                            ...videoFields,
                            knex.raw('\'video\' as content_type')
                        ]);

                        // For videos, we always sort by created_at
                        videosQuery.orderBy('videos.id', 'desc');

                        // If visual search is enabled, add a placeholder distance column
                        if (visualSearchQuery) {
                            videosQuery.select(knex.raw('NULL::FLOAT as distance'));
                        }

                        // Apply cursor-based pagination if available
                        if (cursorValues && cursorValues.lastVideoId) {
                            console.log('cursorValues.lastVideoId', cursorValues.lastVideoId);
                            videosQuery.where('videos.id', '<', cursorValues.lastVideoId);
                        }

                        // Get items + 1 to check if there are more
                        videosQuery.limit(itemsPerType + 1);

                        return await videosQuery;
                    })() : Promise.resolve([])
                ])
            ]);

            // Extract counts and results
            const [imageCount, videoCount] = countPromise;
            [imageResults, videoResults] = contentPromise;

            const imageVersions = await this.getVersionImageByImageIds(imageResults.map((image) => image.id));

            // Check if we have more items than the limit per type
            const hasMoreImages = imageResults.length > itemsPerType;
            const hasMoreVideos = videoResults.length > itemsPerType;

            // Trim results to requested limit per type
            if (hasMoreImages) {
                imageResults = imageResults.slice(0, itemsPerType);
            }

            if (hasMoreVideos) {
                videoResults = videoResults.slice(0, itemsPerType);
            }

            // Combine results
            let combinedResults = [];

            if (visualSearchQuery && contentTypes.includes('image')) {
                // For visual search, prioritize images sorted by similarity
                // then append videos sorted by creation date
                combinedResults = [...imageResults, ...videoResults];
            }
            else {
                // Combine and sort chronologically by created_at for standard browsing
                combinedResults = [...imageResults, ...videoResults].sort((a, b) => {
                    // Sort by created_at desc, then by id desc
                    const dateComparison = new Date(b.created_at) - new Date(a.created_at);
                    if (dateComparison !== 0) {
                        return dateComparison;
                    }

                    return b.id - a.id;
                });
            }

            // Limit the combined results to the original requested limit
            const hasMore = hasMoreImages || hasMoreVideos;
            combinedResults = combinedResults.slice(0, limit);

            // Find last items of each type for cursor generation
            const lastImageItem = imageResults.length > 0 ? imageResults[imageResults.length - 1] : null;
            const lastVideoItem = videoResults.length > 0 ? videoResults[videoResults.length - 1] : null;

            // Decorate the results
            const decoratedContent = await Promise.all(combinedResults.map(async (item) => {
                if (item.content_type === 'image') {
                    return await this._decorateImage(item, imageVersions);
                }

                return await this._decorateVideo(item);

            }));

            // Create a cursor for pagination
            let nextCursor = null;
            if (hasMore) {
                nextCursor = Buffer.from(JSON.stringify({
                    lastImageId: lastImageItem?.id,
                    lastImageCreatedAt: lastImageItem?.created_at,
                    lastVideoId: lastVideoItem?.id || 0,
                    lastVideoCreatedAt: lastVideoItem?.created_at
                })).toString('base64');
            }

            const total = imageCount + videoCount;

            return {
                content: decoratedContent,
                pagination: {
                    hasMore,
                    nextCursor,
                    total
                }
            };
        }
        catch (error) {
            console.error('Error in getContent:', error);
            throw new Error(`Failed to get content: ${error.message}`);
        }
    }

    // Helper methods to keep the main function clean

    _buildBaseContentQuery(model, user, role, clientId, collectionId) {
        // Initialize the base query
        const query = model.query()
            .leftJoin('shoots', 'shoots.id', '=', `${model.tableName}.shoot_id`)
            .leftJoin('orders', 'orders.id', '=', 'shoots.order_id')
            .leftJoin('clients', 'clients.id', '=', 'orders.client_id')
            .leftJoin('shoots_services', 'shoots_services.shoot_id', '=', 'shoots.id');

        if (model.tableName === 'images') {
            query.leftJoin('comments', 'comments.image_id', '=', 'images.id');
        }

        // Handle collection filtering
        if (collectionId) {
            if (model.tableName === 'images') {
                query.innerJoin('collection_items', function () {
                    this.on('collection_items.image_id', '=', 'images.id')
                        .andOnVal('collection_items.collection_id', '=', collectionId)
                        .andOnNull('collection_items.video_id');
                });
            }
            else {
                query.innerJoin('collection_items', function () {
                    this.on('collection_items.video_id', '=', 'videos.id')
                        .andOnVal('collection_items.collection_id', '=', collectionId)
                        .andOnNull('collection_items.image_id');
                });
            }
        }

        if (role !== 'admin') {
            // Apply base permission filters
            query.where(function () {
                this.whereIn('shoots.status', ['completed', 'ready'])
                    .orWhere(`${model.tableName}.user_id`, user.id);
            });
        }

        // Apply role-based filters
        if (role === 'client') {
            const clientIdVal = user.parent_client_id || user.client_id;
            query.where(function () {
                this.where('orders.client_id', clientIdVal)
                    .orWhere(`${model.tableName}.user_id`, user.id);
            });
        }

        if (role === 'admin' && clientId) {
            query.where(function () {
                this.where('orders.client_id', clientId)
                    .orWhere(`${model.tableName}.user_id`, user.id);
            });
        }

        return query;
    }

    _applyContentFilters(query, model, starts_at, ends_at, services, aspectRatios, metadataSearchQuery) {
        // Date range filters
        if (starts_at) {
            query.where('shoots.datetime', '>=', starts_at);
        }

        if (ends_at) {
            query.where('shoots.datetime', '<=', ends_at);
        }

        // Services filter
        if (services?.length) {
            if (services.length > 10) {
                query.whereExists(function () {
                    this.select(1)
                        .from('shoots_services as ss')
                        .whereRaw('ss.shoot_id = shoots.id')
                        .whereIn('ss.service_id', services);
                });
            }
            else {
                query.whereIn('shoots_services.service_id', services);
            }
        }

        // Aspect ratio filter
        if (aspectRatios?.length) {
            query.where(function () {

                aspectRatios.forEach((ratio, index) => {
                    const filterFn = index === 0 ? 'where' : 'orWhere';
                    this[filterFn](function () {
                        const ranges = {
                            landscape: { op: '>', value: 1.5 },
                            portrait: { op: '<', value: 0.7 },
                            square: { range: [0.7, 1.5] }
                        };
                        const range = ranges[ratio];
                        if (range.range) {
                            this.whereBetween(`${model.tableName}.aspect_ratio`, range.range);
                        }
                        else {
                            this.where(`${model.tableName}.aspect_ratio`, range.op, range.value);
                        }
                    });
                });
            });
        }

        // Metadata search filter
        if (!isNaN(parseFloat(metadataSearchQuery)) && isFinite(metadataSearchQuery)) {
            query.where('shoots.id', metadataSearchQuery);
        }
        else if (metadataSearchQuery) {
            query.leftJoin('shoots as shoot_details', 'shoot_details.id', `${model.tableName}.shoot_id`)
                .where(function () {

                    this.whereRaw('shoots.id::text ILIKE ?', [`%${metadataSearchQuery}%`])
                        .orWhereRaw('shoot_details.name ILIKE ?', [`%${metadataSearchQuery}%`])
                        .orWhereRaw('shoot_details.notes ILIKE ?', [`%${metadataSearchQuery}%`])
                        .orWhereRaw('shoot_details.outlet_name ILIKE ?', [`%${metadataSearchQuery}%`])
                        .orWhereRaw('shoot_details.outlet_code ILIKE ?', [`%${metadataSearchQuery}%`])
                        .orWhereRaw(`${model.tableName}.filename ILIKE ?`, [`%${metadataSearchQuery}%`]);
                });
        }

        if (model.tableName === 'images') {
            query
                .leftJoin('image_versions', 'images.id', '=', 'image_versions.origin_image_id')
                .whereNotExists(function () {
                    this.select(1)
                        .from('image_versions')
                        .whereRaw('image_versions.version_image_id = images.id');
                });
        }

        return query;
    }

    async getVersionImageByImageIds(imageIds) {

        if (!imageIds || imageIds.length === 0) {
            return {};
        }

        const { ImageVersion } = this.server.models();

        const imageVersions = await ImageVersion.query()
            .select('image_versions.origin_image_id', 'image_versions.version_image_id', 'images.url',
                'images.preview_url',
                'images.placeholder_url', 'images.aspect_ratio', 'images.width', 'images.height')
            .leftJoin('images', 'images.id', '=', 'image_versions.version_image_id')
            .whereIn('image_versions.origin_image_id', imageIds)
            .whereRaw('image_versions.is_default = \'true\'')
            .orderBy('image_versions.created_at', 'desc');

        const imageVersionMap = {};

        imageVersions.forEach((imageVersion) => {
            if (!imageVersionMap[imageVersion.origin_image_id]) {
                imageVersionMap[imageVersion.origin_image_id] = {};
            }

            imageVersionMap[imageVersion.origin_image_id] = imageVersion;
        });

        return imageVersionMap;
    }

    _getImageSelectFields(collectionId) {
        return [
            'images.id',
            'images.shoot_id',
            'images.user_id',
            'images.url',
            'images.preview_url',
            'images.placeholder_url',
            'images.status',
            'images.feedback',
            'images.filename',
            'images.name',
            'images.folder',
            'images.filepath',
            'images.uploaded_by',
            'images.aspect_ratio',
            'images.width',
            'images.height',
            'images.stack_id',
            'images.created_at',
            'shoots.datetime',
            'clients.company_name as customer_name',
            'shoots_services.service_id',
            knex.raw(`(
            SELECT array_agg(label->>'Name')
            FROM jsonb_array_elements(images.properties->'Labels') AS label
        ) AS labels`),
            knex.raw(`(
            SELECT COALESCE(json_agg(
                json_build_object(
                    'id', comments.id,
                    'content', comments.content,
                    'user_id', comments.user_id,
                    'user_name', comments.user_name,
                    'created_at', comments.created_at,
                    'updated_at', comments.updated_at
                ) ORDER BY comments.created_at DESC
            ), '[]'::json)::text
            FROM comments
            WHERE comments.image_id = images.id
        ) AS comments`),
            // Video-specific columns (NULL for images)
            ...(collectionId ? ['collection_items.added_at'] : []),
            knex.raw('\'image\' as content_type')
        ];
    }

    _getVideoSelectFields(collectionId) {
        return [
            'videos.id',
            'videos.shoot_id',
            'videos.user_id',
            'videos.url',
            'videos.preview_url',
            'videos.placeholder_url',
            'videos.status',
            'videos.feedback',
            'videos.filename',
            'videos.name',
            'videos.folder',
            'videos.filepath',
            'videos.uploaded_by',
            'videos.aspect_ratio',
            'videos.width',
            'videos.height',
            'videos.created_at',
            'shoots.datetime',
            'clients.company_name as customer_name',
            'shoots_services.service_id',
            'videos.duration',
            'videos.video_hls_url',
            'videos.processing_status',
            ...(collectionId ? ['collection_items.added_at'] : []),
            knex.raw('\'video\' as content_type')
        ];
    }

    _buildCountQuery(model, user, role, clientId, collectionId, starts_at, ends_at, services, aspectRatios, metadataSearchQuery) {
        const query = this._buildBaseContentQuery(model, user, role, clientId, collectionId);
        this._applyContentFilters(query, model, starts_at, ends_at, services, aspectRatios, metadataSearchQuery);
        return query.countDistinct(`${model.tableName}.id as count`);
    }

    async _decorateImage(image, imageVersions) {
        console.log('imageVersions', imageVersions);
        const chosenImageVersion = imageVersions[image.id] || image;

        try {
            const previewDimensions = this.getDimensionsByAspectRatio(chosenImageVersion, true);
            const placeholderDimensions = this.getDimensionsByAspectRatio(chosenImageVersion, false);
            const optimizedDimensions = this.getOptimizedDimensions(chosenImageVersion);
            const [previewUrl, placeholderUrl, optimizedUrl, s3SignedUrl] = await Promise.all([
                chosenImageVersion.url && this.getSignedUrl(chosenImageVersion.url, previewDimensions),
                chosenImageVersion.url && this.getSignedUrl(chosenImageVersion.url, placeholderDimensions),
                chosenImageVersion.url && this.getSignedUrl(chosenImageVersion.url, optimizedDimensions),
                chosenImageVersion.url && this.getS3SignedUrl(chosenImageVersion)
            ]);

            return {
                id: image.id,
                shoot_id: image.shoot_id,
                user_id: image.user_id,
                url: s3SignedUrl,
                optimized_url: optimizedUrl,
                preview_url: previewUrl,
                stack_id: image.stack_id,
                name: image.name,
                placeholder_url: placeholderUrl,
                aspect_ratio: chosenImageVersion.aspect_ratio,
                height: chosenImageVersion.height,
                width: chosenImageVersion.width,
                status: image.status,
                feedback: image.feedback,
                filename: image.filename,
                folder: image.folder,
                filepath: image.filepath,
                created_at: image.created_at ? new Date(image.created_at).toISOString().split('T')[0] : null,
                uploaded_by: image.uploaded_by ? image.uploaded_by : 'admin',
                labels: image.labels || [],
                comments: (() => {
                    try {
                        return image.comments ? JSON.parse(image.comments) : [];
                    }
                    catch (e) {
                        this.server.logger.error({ err: e }, 'Failed to parse comments JSON');
                        return [];
                    }
                })(),
                // Add type identifier
                type: 'image',
                // Add video-specific fields with default values
                duration: 0,
                codec: null,
                framerate: null,
                bitrate: null,
                video_hls_url: null,
                preview_video_url: null,
                seek_video_url: null,
                sprite_sheet_url: null,
                transcript_srt_url: null,
                transcript_vtt_url: null
            };
        }
        catch (error) {
            return {
                ...image,
                preview_url: null,
                url: null,
                placeholder_url: null,
                error: 'Failed to generate signed URLs',
                type: 'image'
            };
        }
    }

    async _decorateVideo(video) {
        try {
            const seekVideoBasePath = video.seek_video_url ? new URL(video.seek_video_url).pathname : null;
            const spriteSheetBasePath = video.sprite_sheet_url ? new URL(video.sprite_sheet_url).pathname : null;
            const transcriptSrtBasePath = video.transcript_srt_url ? new URL(video.transcript_srt_url).pathname : null;
            const transcriptVttBasePath = video.transcript_vtt_url ? new URL(video.transcript_vtt_url).pathname : null;

            const signedVideoUrl = video.url ? await this.getS3SignedUrl({ url: video.url }) : null;

            const signedSeekVideoUrl = seekVideoBasePath ?
                await this.getSignedUrl(seekVideoBasePath) : null;
            const signedSpriteSheetUrl = spriteSheetBasePath ?
                await this.getSignedUrl(spriteSheetBasePath) : null;
            const signedTranscriptSrtUrl = transcriptSrtBasePath ?
                await this.getSignedUrl(transcriptSrtBasePath) : null;
            const signedTranscriptVttUrl = transcriptVttBasePath ?
                await this.getSignedUrl(transcriptVttBasePath) : null;

            const previewDimensions = this.getDimensionsByAspectRatio(video.preview_url, true);
            const placeholderDimensions = this.getDimensionsByAspectRatio(video.placeholder_url, false);

            const signedPreviewUrl = video.preview_url ?
                await this.getSignedUrl(`https://${video.preview_url}`, previewDimensions) : null;
            const signedPlaceholderUrl = video.placeholder_url ?
                await this.getSignedUrl(`https://${video.placeholder_url}`, placeholderDimensions) : null;

            return {
                id: video.id,
                shoot_id: video.shoot_id,
                user_id: video.user_id,
                url: signedVideoUrl?.replace(/%20/g, ' '),
                preview_url: signedPreviewUrl,
                placeholder_url: signedPlaceholderUrl,
                status: video.status,
                feedback: video.feedback,
                filename: video.filename,
                name: video.name,
                folder: video.folder,
                filepath: video.filepath,
                processing_status: video.processing_status,
                created_at: video.created_at ? new Date(video.created_at).toISOString().split('T')[0] : null,
                uploaded_by: video.uploaded_by ? video.uploaded_by : 'admin',
                duration: video.duration || 0,
                aspect_ratio: video.aspect_ratio,
                codec: video.codec,
                framerate: video.framerate,
                labels: [],
                comments: [],
                bitrate: video.bitrate,
                video_hls_url: video.video_hls_url,
                preview_video_url: video.preview_video_url,
                seek_video_url: signedSeekVideoUrl,
                sprite_sheet_url: signedSpriteSheetUrl,
                transcript_srt_url: signedTranscriptSrtUrl,
                transcript_vtt_url: signedTranscriptVttUrl,
                // Add type identifier
                type: 'video'
            };
        }
        catch (error) {
            console.log(error);
            return {
                ...video,
                url: null,
                preview_url: null,
                placeholder_url: null,
                video_hls_url: null,
                preview_video_url: null,
                seek_video_url: null,
                sprite_sheet_url: null,
                transcript_srt_url: null,
                transcript_vtt_url: null,
                error: 'Failed to generate signed URLs',
                type: 'video'
            };
        }
    }

    /**
     * Authorize HLS video stream access with signed cookies
     * @param {string} videoPath - The video path pattern (e.g. '/videos/123/*')
     * @param {number} expires - Expiration time in seconds (default: 3600)
     * @returns {Object} Signed cookies object
     */
    authorizeHlsStreamAccess(videoPath, expires = 3600) {
        const { storageService } = this.server.services();

        // Generate signed cookies
        const signedCookies = storageService.generateSignedCookies(videoPath, expires);

        // Set cookie options
        const cookieOptions = {
            domain: this.server.settings.app.amazon.cookie_domain || '.flashy-staging.ae',

            path: '/',
            secure: true,
            httpOnly: true,
            sameSite: 'strict',
            maxAge: expires * 1000
        };

        return { signedCookies, cookieOptions };
    }

    async getRecentContent({ user, role, limit = 12 }) {
        const { Image, Video } = this.server.models();
        try {
            // Base query for images
            const imageBaseQuery = () => {
                return Image.query()
                    .leftJoin('shoots', 'shoots.id', '=', 'images.shoot_id')
                    .leftJoin('orders', 'orders.id', '=', 'shoots.order_id')
                    .leftJoin('clients', 'clients.id', '=', 'orders.client_id')
                    .where((builder) => {
                        builder.whereIn('shoots.status', ['completed', 'ready'])
                            .orWhere('images.user_id', user.id);
                    })
                    .select([
                        'images.id',
                        'images.shoot_id',
                        'images.url',
                        'images.aspect_ratio',
                        'images.height',
                        'images.width',
                        'images.filename',
                        'images.user_id',
                        'shoots.name as shoot_name',
                        'clients.company_name as customer_name'
                    ])
                    .limit(limit)
                    .orderBy('images.id', 'desc'); // Order by creation time
            };

            // Base query for videos
            const videoBaseQuery = () => {
                return Video.query()
                    .leftJoin('shoots', 'shoots.id', '=', 'videos.shoot_id')
                    .leftJoin('orders', 'orders.id', '=', 'shoots.order_id')
                    .leftJoin('clients', 'clients.id', '=', 'orders.client_id')
                    .where((builder) => {
                        builder.whereIn('shoots.status', ['completed', 'ready']);
                        // Add user_id check if videos have user association
                    })
                    .select([
                        'videos.id',
                        'videos.shoot_id',
                        'videos.url',
                        'videos.aspect_ratio',
                        'videos.height',
                        'videos.width',
                        'videos.filename',
                        'videos.preview_url', // Specific video preview
                        'videos.placeholder_url', // Specific video placeholder
                        'shoots.name as shoot_name',
                        'videos.created_at as created_at', // Use created_at for sorting
                        'clients.company_name as customer_name'
                    ])
                    .limit(limit)
                    .orderBy('videos.created_at', 'desc'); // Order by creation time
            };

            const applyFilters = (query, contentType) => {
                if (role === 'client') {
                    const clientId = user.parent_client_id || user.client_id;
                    query.where((builder) => {
                        builder.where('orders.client_id', clientId);
                        if (contentType === 'image') {
                            builder.orWhere('images.user_id', user.id);
                        }
                        // Add similar condition for videos if applicable
                    });
                }

                return query;
            };

            // Fetch recent images and videos in parallel
            const [rawImages, rawVideos] = await Promise.all([
                applyFilters(imageBaseQuery(), 'image'),
                applyFilters(videoBaseQuery(), 'video')
            ]);

            // Process images
            const processedImages = await Promise.all(rawImages.map(async (image) => {
                const previewDimensions = this.getDimensionsByAspectRatio(image, true);
                const placeholderDimensions = this.getDimensionsByAspectRatio(image, false);
                const optimizedDimensions = this.getOptimizedDimensions(image);
                const [previewUrl, placeholderUrl, optimizedUrl, s3SignedUrl] = await Promise.all([
                    image.url && this.getSignedUrl(image.url, previewDimensions),
                    image.url && this.getSignedUrl(image.url, placeholderDimensions),
                    image.url && this.getSignedUrl(image.url, optimizedDimensions),
                    image.url && this.getS3SignedUrl(image)
                ]);

                return {
                    type: 'image',
                    id: image.id,
                    shoot_id: image.shoot_id,
                    url: s3SignedUrl,
                    optimized_url: optimizedUrl,
                    preview_url: previewUrl,
                    placeholder_url: placeholderUrl,
                    aspect_ratio: image.aspect_ratio,
                    height: image.height,
                    width: image.width,
                    filename: image.filename,
                    shoot_name: image.shoot_name,
                    created_at: image.created_at ? new Date(image.created_at).toISOString().split('T')[0] : null,
                    relative_upload_time: this._timeAgo(image.created_at)
                };
            }));

            // Process videos
            const processedVideos = await Promise.all(rawVideos.map(async (video) => {
                // Assuming getS3SignedUrl works for videos too, adjust if needed
                const [s3SignedUrl, previewUrl, placeholderUrl] = await Promise.all([
                    video.url && this.getS3SignedUrl(video), // Need to adjust getS3SignedUrl if it's image-specific
                    video.preview_url && this.getSignedUrl(video.preview_url), // Use pre-generated or sign video preview URL
                    video.placeholder_url && this.getSignedUrl(video.placeholder_url) // Use pre-generated or sign video placeholder URL
                ]);

                return {
                    type: 'video',
                    id: video.id,
                    shoot_id: video.shoot_id,
                    url: s3SignedUrl, // Signed URL for the original video file
                    preview_url: previewUrl, // Signed URL for video thumbnail/preview
                    placeholder_url: placeholderUrl, // Signed URL for video placeholder
                    aspect_ratio: video.aspect_ratio,
                    height: video.height,
                    width: video.width,
                    filename: video.filename,
                    shoot_name: video.shoot_name,
                    created_at: video.created_at,
                    relative_upload_time: this._timeAgo(video.created_at)
                };
            }));

            // Combine, sort by creation date, and limit
            const combinedContent = [...processedImages, ...processedVideos]
                .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
                .slice(0, limit);

            return combinedContent;

        }
        catch (error) {
            this.server.logger.error({ err: error, user: user.id, role }, 'Failed to get recent content');
            throw new Error(`Failed to get recent content: ${error.message}`);
        }
    }

    // Helper method for signing URLs
    getSignedUrl(url, dimensions) {

        const { storageService } = this.server.services();
        if (!url) {
            return null;
        }

        const updatedUrl = this.getCloudfrontUrl(url, dimensions.width, dimensions.height);
        return storageService.signCloudFrontUrl({
            url: updatedUrl,
            expires: 86400
        });
    }

    getSignedCookies(url) {

        const { storageService } = this.server.services();

        if (!url) {
            return null;
        }

        return storageService.signCookies({
            url
        });
    }

    extractUrlPattern(url) {
        const urlParts = url.split('/');
        const bucket = urlParts[2];
        const folder = urlParts[3];

        return `https://${bucket}/${folder}/*`;
    }

    // Helper method for signing S3 URLs
    async getS3SignedUrl(image) {
        const { storageService } = this.server.services();
        if (!image.url) {
            return null;
        }

        const url = new URL(image.url);
        const bucket = url.hostname.split('.')[0];
        // Properly decode and encode the key with %20 for spaces
        const key = decodeURIComponent(url.pathname.substring(1))
            .replace(/\+/g, ' '); // Replace any + with spaces first

        return await storageService.signS3Url({
            bucket,
            key,
            expires: 86400
        });
    }
    /**
 * Format and resize images using CloudFront URLs with Thumbor filters.
 *
 * @param {Object} params - Parameters for formatting images
 * @param {number[]} params.imageIds - Array of image IDs to process
 * @param {Object} params.formatOptions - Image formatting options
 * @param {number} [params.formatOptions.width] - Desired width in pixels
 * @param {number} [params.formatOptions.height] - Desired height in pixels
 * @param {number} [params.formatOptions.minWidth] - Minimum width constraint in pixels
 * @param {string} [params.formatOptions.fit='fit-in'] - Fit mode: 'fit-in', 'adaptive', 'cover'
 * @param {string} [params.formatOptions.format='jpeg'] - Output format: 'jpeg', 'webp'
 * @param {number} [params.formatOptions.quality=85] - Image quality (1-100)
 * @param {boolean} [params.formatOptions.preserveAspectRatio=true] - Maintain aspect ratio during resize
 * @param {boolean} [params.formatOptions.smartCrop=true] - Use smart cropping for better subject focus
 * @param {Object} [params.formatOptions.watermark] - Watermark options
 * @param {string} params.formatOptions.watermark.image - Base64 watermark image string
 * @param {string} params.formatOptions.watermark.x - X position (e.g. -10p, center)
 * @param {string} params.formatOptions.watermark.y - Y position (e.g. 10p, center)
 * @param {number} params.formatOptions.watermark.alpha - Watermark opacity (0-100)
 * @param {string} params.formatOptions.watermark.w_ratio - Width as percentage of image
 * @param {string} params.formatOptions.watermark.h_ratio - Height ratio
 * @param {number} [params.expires=86400] - URL expiration time in seconds
 * @returns {Promise<Array<{id: number, url: string, error?: string}>>} Array of image IDs and their formatted URLs
 */
    async formatImages({ imageIds, formatOptions, expires = 86400 }) {
        if (!Array.isArray(imageIds) || imageIds.length === 0) {
            throw new Error('Image IDs array is required and cannot be empty');
        }

        // Handle watermark if provided
        let watermarkKey;
        if (formatOptions.watermark) {
            const { image, x, y, alpha } = formatOptions.watermark;

            // Validate parameters
            const validPosition = (pos) => {
                return typeof pos === 'string' && (
                    pos === 'center' ||
                    /^-?\d+p?$/.test(pos)
                );
            };

            if (!validPosition(x) || !validPosition(y)) {
                throw new Error('Invalid watermark position format. Use "center" or "<number>p"');
            }

            if (typeof alpha !== 'number' || alpha < 0 || alpha > 100) {
                throw new Error('Alpha value must be between 0 and 100');
            }

            try {
                watermarkKey = `${Date.now()}.png`;
                const imageBuffer = Buffer.from(image.split(',')[1], 'base64');

                // Upload watermark to S3
                await this.server.services().storageService.uploadObject({
                    bucket: this.watermarksBucket,
                    filepath: watermarkKey,
                    object: imageBuffer,
                    options: { ContentType: 'image/png' }
                });

                // Verify upload
                const exists = await this.server.services().storageService.objectExists({
                    bucket: this.watermarksBucket,
                    filepath: watermarkKey
                });

                if (!exists) {
                    throw new Error('Failed to verify watermark upload');
                }
            }
            catch (error) {
                throw new Error(`Failed to process watermark: ${error.message}`);
            }
        }

        try {
            const images = await this.Image.query()
                .whereIn('id', imageIds)
                .select('id', 'preview_url');

            if (images.length === 0) {
                return [];
            }

            const formattedImages = images.map(async (image) => {
                if (!image.preview_url) {
                    return { id: image.id, url: null };
                }

                const originalUrlParts = image.preview_url.split('/');
                const cdnDomain = this.cfDomain;
                const baseImagePath = originalUrlParts.includes('https:') ? originalUrlParts.slice(5).join('/') : originalUrlParts.slice(3).join('/');
                const [shootId, ...remains] = baseImagePath.split('/');
                const filename = remains.join('/');

                const newUrlParts = [];
                const filters = [];

                // Handle fit mode and dimensions
                const fitMode = formatOptions.fit || 'fit-in';
                if (formatOptions.width && formatOptions.height) {
                    const width = formatOptions.width || '';
                    const height = formatOptions.height || '';
                    newUrlParts.push(fitMode === 'cover' ?
                        `${width}x${height}` :
                        `${fitMode}/${width}x${height}`);
                }

                // Add basic filters
                filters.push(`filters:format(${formatOptions.format || 'jpeg'})`);

                if (formatOptions.quality < 100) {
                    filters.push(`filters:quality(${formatOptions.quality || 85})`);
                }

                if (formatOptions.smartCrop !== false && fitMode === 'cover') {
                    filters.push('smart');
                }

                if (formatOptions.preserveAspectRatio !== false && fitMode === 'adaptive') {
                    filters.push('preserve_aspect_ratio');
                }

                // Add watermark filter
                if (formatOptions.watermark && watermarkKey) {
                    const { x, y, alpha, w_ratio } = formatOptions.watermark;
                    // Create watermark filter with s3: prefix in the bucket parameter
                    const watermarkFilter = `filters:watermark(${this.watermarksBucket},${watermarkKey},${x},${y},${alpha},${w_ratio})`;

                    filters.push(watermarkFilter);
                }

                if (filters.length > 0) {
                    newUrlParts.push(`${filters.join('/')}`);
                }

                newUrlParts.push(`${shootId}/${filename}`);

                try {
                    const newUrl = `${cdnDomain}/${newUrlParts.join('/')}`.replace(/\/$/, '');
                    const signedUrl = await this.server.services().storageService.signCloudFrontUrl({
                        url: newUrl,
                        expires
                    });

                    return { id: image.id, url: signedUrl };
                }
                catch (error) {
                    return { id: image.id, url: null, error: 'Failed to sign URL' };
                }
            });

            return await Promise.all(formattedImages);
        }
        catch (error) {
            throw new Error(`Failed to format images: ${error.message}`);
        }
    }

    async publishContentEnrichmentMessage(images) {
        const messageBody = JSON.stringify({
            images,
            timestamp: new Date().toISOString(),
            batchId: `batch-${Date.now()}`
        });

        const params = {
            MessageBody: messageBody,
            QueueUrl: this.enrichmentQueueUrl,
            MessageAttributes: {
                'BatchSize': {
                    DataType: 'Number',
                    StringValue: images.length.toString()
                }
            }
        };

        await this.sqs.sendMessage(params).promise();
    }

    /**
     * Update image URL with new dimensions.
     * @param url
     * @param newWidth
     * @param newHeight
     * @returns {*}
     */
    getCloudfrontUrl(url, newWidth = null, newHeight = null) {

        // Extract path components from S3 URL
        const s3Url = new URL(url);
        const pathParts = s3Url.pathname.split('/').filter(Boolean); // Remove empty strings
        const formattedPathParts = pathParts.map((part) => {
            return part.includes('+') ? part.replace(/\+/g, ' ') : part;
        });

        if (newWidth && newHeight) {

            const dimensionPart = `fit-in/${newWidth}x${newHeight}`;

            // Combine all parts to form the final URL
            return `${this.cfDomain}/${dimensionPart}/${formattedPathParts.join('/')}`;
        }

        return `${this.cfDomain}/${formattedPathParts.join('/')}`;
    }

    /**
     * Get dimensions by aspect ratio.
     * @param image
     * @param isPreview
     * @returns {{width: number, height: number}|{width: number, height: number}}
     */
    getDimensionsByAspectRatio(image, isPreview = false) {
        if (image.aspect_ratio > ASPECT_RATIO_THRESHOLD) {
            return isPreview ? {
                width: 1280,
                height: 720
            } : {
                width: 160,
                height: 90
            };
        }

        if (image.aspect_ratio < TALL_RATIO_THRESHOLD) {
            return isPreview ? {
                width: 720,
                height: 1280
            } : {
                width: 90,
                height: 160
            };
        }

        return isPreview ? {
            width: 960,
            height: 960
        } : {
            width: 120,
            height: 120
        };
    }

    /**
     * Get dimensions for optimized 1440p version while maintaining aspect ratio.
     * @param {Object} image - The image object containing aspect ratio
     * @returns {Object} Dimensions object with width and height
     */
    getOptimizedDimensions(image) {
        if (image.aspect_ratio > ASPECT_RATIO_THRESHOLD) {
            // Landscape orientation (16:9)
            return {
                width: 2560,
                height: 1440
            };
        }

        if (image.aspect_ratio < TALL_RATIO_THRESHOLD) {
            // Portrait orientation (9:16)
            return {
                width: 1440,
                height: 2560
            };
        }

        // Square or near-square images
        return {
            width: 1440,
            height: 1440
        };
    }

    _timeAgo(timestamp) {
        if (!timestamp) {
            return null;
        }

        const now = new Date();
        const past = new Date(timestamp);
        const diffInSeconds = Math.floor((now - past) / 1000);

        const units = [
            { name: 'year', seconds: 31536000 },
            { name: 'month', seconds: 2592000 },
            { name: 'day', seconds: 86400 },
            { name: 'hour', seconds: 3600 },
            { name: 'minute', seconds: 60 },
            { name: 'second', seconds: 1 }
        ];

        for (const unit of units) {
            const interval = Math.floor(diffInSeconds / unit.seconds);
            if (interval >= 1) {
                return `${interval} ${unit.name}${interval > 1 ? 's' : ''} ago`;
            }
        }

        return 'just now';
    }

    async improveCaption(caption) {
        try {
            const response = await this.openai.chat.completions.create({
                model: 'gpt-4',
                messages: [
                    {
                        role: 'system',
                        // eslint-disable-next-line max-len
                        content: 'You are a social media expert specializing in creating engaging and effective captions. Your task is to improve the given caption while maintaining its core message and adding engaging elements like emojis where appropriate. The improved version should be more compelling and likely to generate better engagement while staying authentic and professional.'
                    },
                    {
                        role: 'user',
                        content: `Please improve this social media caption: ${caption}`
                    }
                ],
                temperature: 0.7,
                max_tokens: 500
            });

            const improvedCaption = response.choices[0].message.content.trim();
            return improvedCaption;
        }
        catch (error) {
            this.server.log(['error', 'content-service'], {
                message: 'OpenAI caption improvement failed',
                error: error.message,
                caption
            });
            throw error;
        }
    }

    async generateHashtags(caption) {
        try {
            const response = await this.openai.chat.completions.create({
                model: 'gpt-4',
                messages: [
                    {
                        role: 'system',
                        // eslint-disable-next-line max-len
                        content: 'You are a social media expert specializing in creating relevant, trending, and effective hashtags. Generate exactly 6 hashtags that are relevant to the given caption. The hashtags should be specific, engaging, and follow social media best practices.'
                    },
                    {
                        role: 'user',
                        // eslint-disable-next-line max-len
                        content: `Generate exactly 6 relevant hashtags for this caption: ${caption}. Return only the hashtags as a comma-separated list, without explanations.`
                    }
                ],
                temperature: 0.7,
                max_tokens: 150
            });

            const hashtagString = response.choices[0].message.content.trim();
            const hashtags = hashtagString
                .split(',')
                .map((tag) => tag.trim())
                .filter((tag) => tag.length > 0)
                .map((tag) => (tag.startsWith('#') ? tag : `#${tag}`))
                .slice(0, 6); // Ensure maximum of 6 hashtags

            return [...new Set(hashtags)]; // Remove duplicates
        }
        catch (error) {
            this.server.log(['error', 'content-service'], {
                message: 'OpenAI hashtag generation failed',
                error: error.message,
                caption
            });
            throw error;
        }
    }

    async extractContentFolders({ user }) {
        const { Image } = this.server.models();

        try {
            // Build query to find images related to the user
            const query = Image.query()
                .select('images.url')
                .where(function () {
                    // Match images directly uploaded by this user
                    this.where('images.user_id', user.id)
                        .andWhereRaw('images.shoot_id is NULL');
                })
                // Only include non-null URLs
                .whereNotNull('images.url');

            // Execute the query
            const images = await query;

            // Extract folder paths from URLs
            const folders = images.map((image) => {
                try {
                    const url = new URL(image.url);
                    const pathParts = url.pathname.split('/').filter(Boolean);

                    // Handle different folder path patterns
                    if (pathParts.length <= 1) {
                        return pathParts[0] || null;
                    }

                    // For URLs with filename at the end, remove the last part
                    const folderParts = pathParts.slice(0, -1);
                    return folderParts.join('/') || null;
                }
                catch (err) {
                    return null;
                }
            }).filter(Boolean); // Remove nulls

            // Get unique folders
            return [...new Set(folders)];
        }
        catch (error) {
            throw new Error(`Failed to extract content folders: ${error.message}`);
        }
    }

    async updateContentMetadata({ contentId, name, type }) {
        let content  = null;
        let schemaKeys = null;

        if (type === 'video') {
            const { video } = require('../validation-schema/shoots/responses.js');

            schemaKeys = Object.keys(video.describe().keys);
            content = this.server.models().Video.query();
        }
        else {
            const { image } = require('../validation-schema/shoots/responses.js');

            schemaKeys = Object.keys(image.describe().keys);
            content = this.server.models().Image.query();
        }

        if (!content) {
            throw new Error('Invalid content type');
        }

        try {
            // Update metadata
            const updatedContent = await content
                .patchAndFetchById(contentId, {
                    name
                });

            if (!updatedContent) {
                throw new Error('Content not found');
            }

            // Get only the fields from the schema

            // Create a filtered object with only the schema-defined keys
            const filteredContent = {};
            schemaKeys.forEach((key) => {
                if (updatedContent[key] !== undefined) {
                    filteredContent[key] = updatedContent[key];
                }
            });

            return filteredContent;
        }
        catch (error) {
            throw new Error(`Failed to update content metadata: ${error.message}`);
        }
    }

    async deleteImage({ imageId }) {
        const { Image } = this.server.models();
        const { storageService } = this.server.services();
        try {

            // Delete image from S3
            const image = await Image.query()
                .where('id', imageId)
                .first();

            if (!image) {
                throw new Error('Image not found');
            }

            if (!image.url) {
                throw new Error('Image URL not found');
            }

            const url = new URL(image.url);
            const bucket = url.hostname.split('.')[0];
            const key = decodeURIComponent(url.pathname.substring(1))
                .replace(/%20/g, ' ')
                .replace(/\+/g, ' '); // Replace any + with spaces first

            if (!key || key.endsWith('/')) {
                throw new Error('Invalid image key or key points to a folder instead of a file');
            }

            if (/^\d+$/.test(key)) {
                throw new Error('Cannot delete a potential folder path');
            }

            // Check if the key lacks a file extension (additional safety check)
            if (!/\.[a-zA-Z0-9]+$/.test(key)) {
                throw new Error('Cannot delete a path without a file extension');
            }

            await storageService.deleteObjects({
                bucket,
                filepaths: [key]
            });

            // Delete image from database
            await Image.query()
                .delete()
                .where('id', imageId);

            return { success: true };
        }
        catch (error) {
            throw new Error(`Failed to delete image: ${error.message}`);
        }
    }

    async deleteVideo({ videoId }) {
        const { Video } = this.server.models();
        const { storageService } = this.server.services();
        try {

            // Delete image from S3
            const video = await Video.query()
                .where('id', videoId)
                .first();

            if (!video) {
                throw new Error('Image not found');
            }

            if (!video.url) {
                throw new Error('Image URL not found');
            }

            const url = new URL(video.url);
            const bucket = url.hostname.split('.')[0];
            const key = decodeURIComponent(url.pathname.substring(1))
                .replace(/%20/g, ' ')
                .replace(/\+/g, ' '); // Replace any + with spaces first

            if (!key || key.endsWith('/')) {
                throw new Error('Invalid image key or key points to a folder instead of a file');
            }

            if (/^\d+$/.test(key)) {
                throw new Error('Cannot delete a potential folder path');
            }

            // Check if the key lacks a file extension (additional safety check)
            if (!/\.[a-zA-Z0-9]+$/.test(key)) {
                throw new Error('Cannot delete a path without a file extension');
            }

            await storageService.deleteObjects({
                bucket,
                filepaths: [key]
            });

            // Delete image from database
            await Video.query()
                .delete()
                .where('id', videoId);

            return { success: true };
        }
        catch (error) {
            throw new Error(`Failed to delete image: ${error.message}`);
        }
    }

    async checkUserImageAccess({ user, role, imageId }) {
        if (role === 'admin') {
            return true; // Admins have access to all images
        }

        const { Image } = this.server.models();

        try {
            // Check if the image exists and belongs to the user
            const image = await Image.query()
                .leftJoin('shoots', 'shoots.id', '=', 'images.shoot_id')
                .leftJoin('orders', 'orders.id', '=', 'shoots.order_id')
                .leftJoin('clients', 'clients.id', '=', 'orders.client_id')
                .leftJoin('users', 'users.client_id', '=', 'clients.id')
                .where('images.id', imageId)
                .andWhere(function () {
                    this.where('images.user_id', user.id)
                        .orWhere('users.id', user.id);
                })
                .first();

            return !!image;
        }
        catch (error) {
            throw new Error(`Failed to check image access: ${error.message}`);
        }
    }
    async addLabelToImage(imageId, label) {
        const { Image } = this.server.models();
        const image = await Image.query().findById(imageId);

        if (!image) {
            throw new NotFoundError('Image not found');
        }

        // Ensure properties is an object
        const properties = image.properties || {};
        properties.Labels = properties.Labels || [];

        const existingLabelNames = new Set(properties.Labels.map((label) => label.Name));

        if (label && !existingLabelNames.has(label)) {
            properties.Labels.push({ Name: label, Confidence: 100 });
        }

        // Update only the properties column in the database
        await Image.query().patchAndFetchById(imageId, { properties });
        // Return only the array of label names as strings
        return properties.Labels.map((l) => l.Name);
    }

    async getImagesByStackId(stackId) {
        const { ImageVersion, Stack, Image } = this.server.models();
        const stack = await Stack.query().findById(stackId);

        if (!stack) {
            throw new NotFoundError('Stack not found');
        }

        // Get all image versions for this stack
        const imageVersions = await ImageVersion.query()
            .select(
                'images.*',
                'image_versions.is_default',
                'image_versions.version_name',
                'image_versions.id as version_id',
                'image_versions.origin_image_id'
            )
            .join('images', 'images.id', '=', 'image_versions.version_image_id')
            .where('image_versions.stack_id', stackId)
            .orderBy('image_versions.created_at', 'desc');

        if (!imageVersions || imageVersions.length === 0) {
            return [];
        }

        // Get the original image and add necessary properties
        const originalImageId = imageVersions[0].origin_image_id;
        let originalImage = null;

        if (originalImageId) {
            originalImage = await Image.query().findById(originalImageId);
            if (originalImage) {
                // Explicitly add the properties needed for consistent structure
                originalImage.version_id = null;
                originalImage.is_default = false;
                originalImage.version_name = 'V0';
                originalImage.is_original = true;
            }
        }

        const isExistedDefault = imageVersions.some((image) => image.is_default === 'true');
        // Combine both sets of images
        const images = originalImage ? [...imageVersions, originalImage] : imageVersions;

        // Map all images with consistent structure
        return images.map((image) => {

            const { version_id, version_name, ...rest } = image;

            if (!isExistedDefault && !version_id) {
                // If no default version exists, set the original image as default
                image.is_default = true;
            }

            return {
                ...rest,
                version_id: version_id || null,
                version_name: version_name || 'V0',
                is_default: (image.is_default === true || image.is_default === 'true'),
                is_original: image.is_original || false
            };
        });
    }

    async addImageVersion(stackId, originImageId, versionImageId, versionName) {
        const { ImageVersion } = this.server.models();

        const existedVersion = await ImageVersion.query().where({
            origin_image_id: originImageId,
            version_image_id: versionImageId,
            stack_id: stackId
        }).first();

        if (existedVersion) {
            throw new Error('Version is existed');
        }

        // Use transaction to ensure data consistency
        await ImageVersion.transaction(async (trx) => {
            // First, set is_default to false for all other versions of this origin image
            await ImageVersion.query(trx)
                .where('origin_image_id', originImageId)
                .update({ is_default: false });

            // Then insert the new version with is_default=true
            await ImageVersion.query(trx).insert({
                origin_image_id: originImageId,
                version_image_id: versionImageId,
                version_name: versionName,
                stack_id: stackId,
                is_default: true,
                created_at: new Date()
            });
        });
    }

    async restoreImageVersion(versionImageId) {
        const { ImageVersion } = this.server.models();

        const originalVersion = await ImageVersion.query().where({
            origin_image_id: versionImageId
        }).first();

        if (!originalVersion) {
            const existedVersion = await ImageVersion.query().where({
                version_image_id: versionImageId
            }).first();

            if (!existedVersion) {
                throw new NotFoundError('Version not found');
            }
        }

        // Use transaction to ensure data consistency
        await ImageVersion.transaction(async (trx) => {
            // First, set is_default to false for all other versions of this origin image
            await ImageVersion.query(trx)
                .where('origin_image_id', versionImageId)
                .update({ is_default: false });

            if (!originalVersion) {
                // Then update the version with is_default=true
                await ImageVersion.query(trx).patch({
                    is_default: true
                }).where({
                    version_image_id: versionImageId
                });
            }
        });
    }

    async removeImageVersion(versionImageId) {

        const { ImageVersion } = this.server.models();
        const version = await ImageVersion.query()
            .where({
                version_image_id: versionImageId
            })
            .first();

        if (!version) {
            throw new NotFoundError('Version not found');
        }

        // Use transaction to ensure data consistency
        await ImageVersion.transaction(async (trx) => {

            // Then delete the version
            await ImageVersion.query(trx).delete().where({
                version_image_id: versionImageId
            });
        });
    }

    async addImageToStack(stackId, imageId, versionImageId, versionName) {
        const { Image, Stack } = this.server.models();
        const image = await Image.query().findById(imageId);

        if (!image) {
            throw new NotFoundError('Image not found');
        }

        if (stackId !== null) { // Allow unsetting the stack by passing null
            const stack = await Stack.query().findById(stackId);
            if (!stack) {
                throw new NotFoundError('Stack not found');
            }
        }

        await this.addImageVersion(stackId, imageId, versionImageId, versionName);

        // Return the newly created version
        return Image.query().patchAndFetchById(imageId, { stack_id: stackId });
    }

    async removeImageFromStack(imageId) {
        const { Image } = this.server.models();
        const image = await Image.query().findById(imageId);

        if (!image) {
            throw new NotFoundError('Image not found');
        }

        // To remove an image from a stack, we simply set its stack_id to null.
        // This is equivalent to calling addImageToStack with stackId = null.
        await Image.query().patchAndFetchById(imageId, { stack_id: null });
        await this.removeImageVersion(imageId);

        // Update the image status and feedback
        await this.deleteImage({
            imageId
        });
    }

    /**
     * Creates a new stack.
     *
     * @param {Object} params - Parameters for creating the stack
     * @param {string} [params.description] - Optional description for the stack
     * @param {string} params.userId - ID of the user creating the stack
     * @returns {Promise<Object>} Newly created stack record
     * @throws {Error} If creation fails
     */
    async createStack({ description, userId, primary_image_id }) {
        const { Stack, Image } = this.server.models();

        if (!userId) {
            throw new Error('User ID is required to create a stack');
        }

        const stack = await Stack.query().insertAndFetch({
            description: description || null,
            user_id: userId,
            created_at: new Date().toISOString()
        });

        if (primary_image_id) {
            await Image.query().patchAndFetchById(primary_image_id, { stack_id: stack.id });
        }

        return stack;
    }

    async removeLabelFromImage(imageId, label) {
        const { Image } = this.server.models();
        const image = await Image.query().findById(imageId);

        if (!image) {
            throw new NotFoundError('Image not found');
        }

        const properties = image.properties || {};
        if (!Array.isArray(properties.Labels) || properties.Labels.length === 0) {
            return [];
        }

        // Remove label by name (case-sensitive)
        properties.Labels = properties.Labels.filter((lab) => lab.Name !== label);

        await Image.query().patchAndFetchById(imageId, { properties });
        // Return only the array of label names as strings, matching addLabelToImage.
        return properties.Labels.map((l) => l.Name);
    }

    // async addCategoryToImage(imageId, category) {
    //     const { Image } = this.server.models();
    //     const image = await Image.query().findById(imageId);

    //     if (!image) {
    //         throw new NotFoundError('Image not found');
    //     }

    //     const properties = image.properties || {};
    //     properties.Categories = properties.Categories || [];

    //     if (category && !properties.Categories.includes(category)) {
    //         properties.Categories.push(category);
    //     }

    //     return Image.query().patchAndFetchById(imageId, { properties });
    // }

    // async removeCategoryFromImage(imageId, category) {
    //     const { Image } = this.server.models();
    //     const image = await Image.query().findById(imageId);

    //     if (!image) {
    //         throw new NotFoundError('Image not found');
    //     }

    //     const properties = image.properties || {};
    //     if (!properties.Categories || properties.Categories.length === 0) {
    //         return image; // No categories to remove
    //     }

    //     properties.Categories = properties.Categories.filter((cat) => cat !== category);

    //     return Image.query().patchAndFetchById(imageId, { properties });
    // }

    async checkUserVideoAccess({ user, role, videoId }) {
        if (role === 'admin') {
            return true; // Admins have access to all images
        }

        const { Video } = this.server.models();

        try {
            // Check if the image exists and belongs to the user
            const video = await Video.query()
                .leftJoin('shoots', 'shoots.id', '=', 'videos.shoot_id')
                .leftJoin('orders', 'orders.id', '=', 'shoots.order_id')
                .leftJoin('clients', 'clients.id', '=', 'orders.client_id')
                .leftJoin('users', 'users.client_id', '=', 'clients.id')
                .where('videos.id', videoId)
                .andWhere(function () {
                    this.where('videos.user_id', user.id)
                        .orWhere('users.id', user.id);
                })
                .first();

            return !!video;
        }
        catch (error) {
            throw new Error(`Failed to check video access: ${error.message}`);
        }
    }

    async getVideosProcessingStatus(videoIds) {
        const { Video } = this.server.models();
        try {
            // Fetch video processing status
            const videos = await Video.query()
                .whereIn('id', videoIds)
                .select('id', 'processing_status', 'placeholder_url', 'preview_url', 'video_hls_url');

            if (videos.length === 0) {
                throw new Error('No videos found');
            }

            return videos.map((video) => ({
                id: video.id,
                processing_status: video.processing_status,
                placeholder_url: video.placeholder_url,
                preview_url: video.preview_url,
                video_hls_url: video.video_hls_url
            }));
        }
        catch (error) {
            throw new Error(`Failed to get video processing status: ${error.message}`);
        }

    }
    /**
     * Creates and adds a comment to either an image or a video.
     * @param {Object} params - Parameters for creating the comment
     * @param {string} params.content - The comment content (required)
     * @param {number} params.user_id - The ID of the user posting the comment (required)
     * @param {string} [params.user_name] - The name of the user posting the comment (optional)
     * @param {number} [params.image_id] - The associated image ID (optional, mutually exclusive with video_id)
     * @returns {Promise<Object>} The created comment record with timestamps
     * @throws {Error} If validation fails or creation fails
     */
    async addComment({ content, user_id, user_name, image_id }) {
        if (!content || typeof content !== 'string') {
            throw new Error('Comment content must be a non-empty string.');
        }

        if (!user_id || isNaN(user_id)) {
            throw new Error('Valid user_id is required.');
        }

        const { Comment, Image } = this.server.models();
        // Check if referenced entity exists
        const image = await Image.query().findById(image_id);
        if (!image) {
            throw new Error(`Image with id ${image_id} does not exist.`);
        }

        // Insert comment with user_name
        const commentData = {
            content,
            user_id,
            user_name: user_name || null,
            image_id: image_id || null
        };

        // Insert the comment and then fetch it to ensure we have the timestamps
        const newComment = await Comment.query().insert(commentData);

        // Fetch the comment with timestamps to ensure they're included in the response
        return await Comment.query().findById(newComment.id);
    }

    async getCommentsByImageId(imageId) {
        const { Comment } = this.server.models();
        try {
            // Fetch comments for the specified image
            const comments = await Comment.query()
                .where('image_id', imageId)
                .orderBy('created_at', 'desc');

            if (comments.length === 0) {
                return [];
            }

            return comments.map((comment) => ({
                id: comment.id,
                content: comment.content,
                user_id: comment.user_id,
                user_name: comment.user_name,
                created_at: comment.created_at,
                updated_at: comment.updated_at
            }));
        }
        catch (error) {
            throw new Error(`Failed to get comments by image ID: ${error.message}`);
        }
    }
};
