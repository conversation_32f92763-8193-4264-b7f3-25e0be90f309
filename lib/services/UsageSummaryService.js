'use strict';

const Schmervice = require('schmervice');

module.exports = class UsageSummaryService extends Schmervice.Service {

    async calculateTotalStorageSize({ user }) {

        const { storageService, contentService } = this.server.services();
        const folders = await contentService.extractContentFolders({ user });

        const bucket = this.server.settings.app.amazon.processed_photos_bucket_name;
        return await storageService.getMultipleFolderSizes(bucket, folders);
    }

    async calculateTotalMembers({ user }) {

        const { Client } = this.server.models();

        const totalMembers = await Client.query()
            .count('users.id')
            .leftJoin('users', 'users.client_id', 'clients.id')
            .where('clients.parent_client_id', user.client_id);

        return totalMembers[0].count;
    }

    async storeUsageSummary({ user }, forceUpdate = false) {

        try {
            const { ClientUsageSummary } = this.server.models();

            if (!user || !user.id) {
                throw new Error('User ID is required');
            }

            const query = ClientUsageSummary.query();
            const client = await user.client;

            if (!client) {
                throw new Error('Client not found');
            }

            const existingSummary = await query.findOne({ client_id: client.id });

            if (existingSummary && !forceUpdate) {
                return existingSummary;
            }

            const totalStorageSize = await this.calculateTotalStorageSize({ user });
            const totalMembers = await this.calculateTotalMembers({ user });

            const usageSummary = {
                client_id: client.id,
                total_storage_size: totalStorageSize,
                total_members: totalMembers
            };

            if (existingSummary) {
                await existingSummary.$query().update(usageSummary);
                return existingSummary;
            }

            return await ClientUsageSummary.query().insert(usageSummary);
        }
        catch (error) {
            this.server.log(['error'], `Error storing usage summary: ${error.message}`);
        }
    }

    async getCurrentUsageSummary({ client }) {

        const { ClientUsageSummary } = this.server.models();

        if (!client) {
            return null;
        }

        const usageSummary = await ClientUsageSummary.query().findOne({ client_id: client.id });

        if (!usageSummary) {
            return null;
        }

        return usageSummary;
    }
};
