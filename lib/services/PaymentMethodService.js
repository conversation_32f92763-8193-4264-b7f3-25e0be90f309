'use strict';

const Schmervice = require('schmervice');

module.exports = class PaymentMethodService extends Schmervice.Service {

    /**
     *
     * @param Stripe.PaymentMethod paymentMethod
     *  The array of services id
     *
     * @return {Object} packages
     *  The db packages data.
     */
    async savePaymentMethodFromStripe({ user, paymentMethod }) {

        const { PaymentMethod } = this.server.models();
        const query = PaymentMethod.query();

        // Check if the payment method already exists
        const existingPaymentMethod = await query.clone()
            .where('user_id', user.id)
            .where('reference', paymentMethod.id)
            .first();

        // If it exists, return the existing payment method
        if (existingPaymentMethod) {
            return existingPaymentMethod;
        }

        // If there are existing default payment methods, set them to non-default
        await query.clone()
            .where('user_id', user.id)
            .andWhere('is_default', true)
            .patch({ is_default: false });

        // Create a new payment method
        return await query.clone().insert({
            user_id: user.id,
            last4_digit: paymentMethod.card.last4,
            status: 'active',
            is_default: true,
            type: paymentMethod.card.brand,
            reference: paymentMethod.id,
            provider: 'stripe',
            full_name: user.name
        });
    }

    async getPaymentMethods({ user }) {

        const { PaymentMethod } = this.server.models();
        const query = PaymentMethod.query();

        // Fetch all payment methods for the user
        return await query
            .where('user_id', user.id)
            .where('status', 'active')
            .orWhere('status', 'inactive')
            .orderBy('created_at', 'desc');
    }

    async deletePaymentMethod({ user, id }) {

        const { PaymentMethod } = this.server.models();
        const query = PaymentMethod.query();

        // Check if the payment method exists
        const existingPaymentMethod = await query.clone()
            .where('user_id', user.id)
            .where('id', id)
            .first();

        // If it exists, delete it
        if (existingPaymentMethod) {
            return await query.clone()
                .where('user_id', user.id)
                .where('id', id)
                .patch({ status: 'deleted' });
        }

        throw new Error('Payment method not found');
    }

    async makeDefaultPaymentMethod({ user, id }) {

        const { PaymentMethod } = this.server.models();
        const query = PaymentMethod.query();

        // If there are existing default payment methods, set them to non-default
        await query.clone()
            .where('user_id', user.id)
            .andWhere('is_default', true)
            .patch({ is_default: false });

        // Check if the payment method exists
        const existingPaymentMethod = await query.clone()
            .where('user_id', user.id)
            .where('id', id)
            .first();

        // If it exists, set it as default
        if (existingPaymentMethod) {
            await query.clone()
                .where('user_id', user.id)
                .where('id', existingPaymentMethod.id)
                .patch({ is_default: true });

            return existingPaymentMethod;
        }

        throw new Error('Payment method not found');
    }

    async connectPaymentGateway({ user }) {

        const { UserPaymentGateway } = this.server.models();
        const { stripeService } = this.server.services();

        const query = UserPaymentGateway.query();
        let customer = await stripeService.getCustomerByEmail(user);

        // If the customer doesn't exist, create a new one
        if (!customer) {
            customer = await stripeService.createCustomer({ payload: { email: user.email, name: user.name, phone: user.phone } });
        }

        // Check if the payment gateway already exists
        const existingPaymentGateway = await query
            .where('user_id', user.id)
            .where('provider', 'stripe')
            .first();

        // If it exists, return the existing payment gateway
        if (existingPaymentGateway) {
            return existingPaymentGateway;
        }

        // If it doesn't exist, create a new one
        return await query.insert({
            user_id: user.id,
            reference: customer.id,
            provider: 'stripe',
            status: 'active'
        });
    }

    async getProviderGateway({ user, provider = 'stripe' }) {

        const { UserPaymentGateway } = this.server.models();
        const query = UserPaymentGateway.query();

        return await query
            .where('user_id', user.id)
            .where('provider', provider)
            .first();
    }

    async getDefaultPaymentMethod({ user }) {

        const { PaymentMethod } = this.server.models();
        const query = PaymentMethod.query();

        // Fetch the default payment method for the user
        return await query
            .where('user_id', user.id)
            .where('is_default', true)
            .where('status', 'active')
            .first();
    }
};
