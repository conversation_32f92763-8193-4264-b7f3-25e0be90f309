'use strict';

const Schmervice = require('schmervice');
const Axios = require('axios');
const { ValidationError } = require('../helpers/errors');

/**
 * Service for Meta Business Platform API interactions
 * @class InstagramService
 * @extends Schmervice.Service
 */
module.exports = class InstagramService extends Schmervice.Service {
    constructor(server, options) {

        super(server, options);
        this.baseUrl = 'https://graph.facebook.com/v18.0';
    }

    /**
     * Get Instagram account information
     * @param {string} accessToken - Access token
     * @param {string} accountId - Instagram account ID
     * @returns {Promise<Object>} Account information
     */
    async getAccountInfo(accessToken, accountId) {

        try {
            const response = await Axios.get(`${this.baseUrl}/${accountId}`, {
                params: {
                    fields: 'id,username,media_count,followers_count,follows_count',
                    access_token: accessToken
                }
            });

            return response.data;
        }
        catch (error) {
            throw new ValidationError(`Failed to get account info: ${  error.message}`);
        }
    }

    /**
     * Get media list for an Instagram account
     * @param {string} accessToken - Access token
     * @param {string} accountId - Instagram account ID
     * @returns {Promise<Object>} Media list response
     */
    async getMediaList(accessToken, accountId) {

        try {
            const response = await Axios.get(`${this.baseUrl}/${accountId}/media`, {
                params: {
                    fields: 'id,caption,media_type,media_url,thumbnail_url,permalink,timestamp',
                    access_token: accessToken
                }
            });

            return response.data;
        }
        catch (error) {
            throw new ValidationError(`Failed to get media list: ${  error.message}`);
        }
    }

    /**
     * Create and publish a media post
     * @param {string} accessToken - Access token
     * @param {string} accountId - Instagram account ID
     * @param {string} imageUrl - URL of the image to post
     * @param {string} caption - Post caption
     * @returns {Promise<Object>} Published post response
     */
    async createMediaPost(accessToken, accountId, imageUrl, caption) {

        try {
            // Create media container
            const containerResponse = await Axios.post(`${this.baseUrl}/${accountId}/media`, null, {
                params: {
                    image_url: imageUrl,
                    caption,
                    access_token: accessToken
                }
            });

            if (!containerResponse.data.id) {
                throw new ValidationError('Failed to create media container');
            }

            // Publish the post
            const publishResponse = await Axios.post(`${this.baseUrl}/${accountId}/media_publish`, null, {
                params: {
                    creation_id: containerResponse.data.id,
                    access_token: accessToken
                }
            });

            return publishResponse.data;
        }
        catch (error) {
            throw new ValidationError(`Failed to create media post: ${error.message}`);
        }
    }
};
