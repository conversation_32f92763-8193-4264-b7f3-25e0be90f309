'use strict';

const Schmervice = require('schmervice');
const AWS  = require('aws-sdk');
const Path = require('path');

module.exports = class StorageService extends Schmervice.Service {

    /**
     * This function initializes the AWS S3 client.
     * @throws {Error} If required AWS credentials are missing
     */
    initialize() {

        const settings = this.server.settings.app.amazon;

        if (!settings) {
            throw new Error('AWS settings are not configured');
        }

        const { aws_id, aws_secret, aws_region, cloudfront_private_key, cloudfront_key_pair_id } = settings;

        // Validate required credentials
        if (!aws_id || !aws_secret || !aws_region) {
            throw new Error('Missing required AWS credentials');
        }

        this.s3 = new AWS.S3({
            accessKeyId: aws_id,
            secretAccessKey: aws_secret,
            region: aws_region,
            useAccelerateEndpoint: true
        });

        // Setup CloudFront signer only if private key is provided
        if (cloudfront_private_key) {
            const keyPairId = cloudfront_key_pair_id;

            if (!cloudfront_private_key.includes('-----B<PERSON>IN PRIVATE KEY-----')) {
                throw new Error('CloudFront private key is not properly formatted');
            }

            this.signer = new AWS.CloudFront.Signer(keyPairId, cloudfront_private_key);
        }
    }

    /**
     * Generate signed cookies for CloudFront access
     * @param {string} resourcePath - The resource path pattern (e.g. '/videos/123/*')
     * @param {number} expires - Expiration time in seconds
     * @returns {Object} Signed cookies object
     */
    generateSignedCookies(resourcePath, expires) {

        if (!this.signer) {
            throw new Error('CloudFront signer not initialized');
        }

        const expiresTimestamp = Math.floor(Date.now() / 1000) + expires;

        return this.signer.getSignedCookies({
            url: `https://${this.server.settings.app.amazon.cloudfront_domain}${resourcePath}`,
            xpires: expiresTimestamp
        });
    }

    /**
     * Upload an object into a given S3 bucket.
     *
     * @param {string} bucket
     *  The name of the bucket.
     * @param {string} filepath
     *  The path of the object inside the bucket.
     * @param {string|Buffer|ReadableStream} object
     *  The object to upload (either as string, Buffer or ReadableStream).
     * @param {object} options
     *  Additional options (e.g., ACL, cache settings), can be any of the
     *  options supported by the AWS S3 SDK. Optional.
     */
    async uploadObject({ bucket, filepath, object, options = {} }) {

        const params = { ...options, Bucket: bucket, Key: filepath, Body: object };

        await this.s3.upload(params).promise();
    }

    /**
     * Retrieves the metadata of a given object from a given S3 bucket.
     *
     * @param {string} bucket
     *  The name of the bucket.
     * @param {string} filepath
     *  The path of the object inside the bucket.
     *
     * @returns {ReadableStream}
     */
    async getObjectMetadata({ bucket, filepath }) {

        return await this.s3.headObject({ Bucket: bucket, Key: filepath }).promise();
    }

    /**
     * Retrieves an object from a given S3 bucket.
     *
     * @param {string} bucket
     *  The name of the bucket.
     * @param {string} filepath
     *  The path of the object inside the bucket.
     * @param {boolean} includeContentLength
     *  Whether to perform an additional HEAD call to obtain
     *  the content length (default: true).
     *
     * @returns {ReadableStream}
     */
    async getObject({ bucket, filepath, includeContentLength = true }) {

        const params = { Bucket: bucket, Key: filepath };

        const { ContentLength: contentLength = null } = includeContentLength && await this.getObjectMetadata({ bucket, filepath });
        const stream = this.s3.getObject(params).createReadStream();

        return {
            contentLength,
            stream
        };
    }

    /**
     * Check whether a given object exists in a given S3 bucket.
     *
     * @param {string} bucket
     *  The name of the bucket.
     * @param {string} filepath
     *  The path of the object inside the bucket.
     *
     * @returns {boolean}
     */
    async objectExists({ bucket, filepath }) {

        const params = { Bucket: bucket, Key: filepath };

        return await this.s3.headObject(params).promise().then(() => true).catch(() => false);
    }

    /**
     * Retrieves the list of objects in a given S3 bucket (or part of it).
     *
     * @param {string} bucket
     *  The name of the bucket.
     * @param {string} prefix
     *  The filename prefix to filter for. Optional.
     * @param {int} maxResults
     *  The maximum number of results to fetch. Optional.
     *
     * @returns {ReadableStream}
     */
    async listObjects({ bucket, prefix, maxResults }) {

        return await this.s3.listObjects({
            Bucket: bucket,
            Prefix: prefix,
            MaxKeys: maxResults
        }).promise().then(({ Contents }) => Contents);
    }

    /**
     * Deletes an object from a given S3 bucket.
     *
     * @param {string} bucket
     *  The name of the bucket.
     * @param {array} filepaths
     *  The paths of the objects inside the bucket (as a list of strings).
     *
     * @returns {ReadableStream}
     */
    async deleteObjects({ bucket, filepaths }) {

        return await this.s3.deleteObjects({
            Bucket: bucket,
            Delete: {
                Objects: filepaths.map((Key) => ({ Key }))
            }
        }).promise();
    }

    /**
     * Create and return base url of S3 for pictures
     *
     * @returns {string}
     */
    getPicturesStaticUrl() {

        const {
            amazon: { packages_pictures_bucket_name: bucket, aws_region: region }
        } = this.server.settings.app;

        return `https://${bucket}.s3.${region}.amazonaws.com`;
    }

    /**
     * Create and return an object containing arrays of possible paths for shoot resources
     *
     * @param {array} shoot_ids
     *  The shoot ids to get the paths for.
     *
     * @returns {object} {rawPaths, processedPaths, briefPaths}
     * The paths of the objects inside the buckets (with a fixed structure required by AWS).
     */
    getPathsByShootIds({ shoot_ids }) {

        const rawPaths = [];
        const processedPaths = [];
        const briefPaths = [];

        for (const id of shoot_ids) {

            rawPaths.push(`${id}/raw-photos`);
            processedPaths.push(`${id}/processed-photos`);
            briefPaths.push(`${id}/brief`);
        }

        return { rawPaths, processedPaths, briefPaths };
    }
    /**
     * Generate presigned URLs for uploading processed photos.
     *
     * @param {object} options
     * @param {string} options.bucket - The name of the S3 bucket.
     * @param {string} options.prefix - The prefix for the S3 object key (e.g., shoot ID).
     * @param {Array} options.files - An array of file objects with name, relativePath, and contentType.
     * @param {number} options.expires - URL expiration time in seconds (default: 3600).
     * @returns {Promise<Array>} An array of objects containing the file info and its presigned URL.
     */

    async getPresignedPutUrls({ bucket, prefix, files, expires = 3600 }) {

        if (!Array.isArray(files)) {
            throw new Error('Files must be an array');
        }

        const presignedUrls = await Promise.all(files.map(async (file) => {

            const key = Path.join(prefix, file.relativePath);

            const params = {
                Bucket: bucket,
                Key: key,
                ContentType: file.contentType,
                Expires: expires
            };

            try {
                const presignedUrl = await this.s3.getSignedUrlPromise('putObject', params);
                return {
                    fileName: file.name,
                    relativePath: file.relativePath,
                    contentType: file.contentType,
                    presignedUrl
                };
            }
            catch (error) {
                throw new Error('Error generating presigned url');
            }
        }));

        return presignedUrls;
    }

    /**
     * Initialize a multipart upload
     * @param {object} options
     * @param {string} options.bucket - The name of the S3 bucket
     * @param {string} options.key - The object key
     * @param {string} options.contentType - The content type of the file
     * @returns {Promise<{uploadId: string}>} The upload ID for the multipart upload
     */
    async initiateMultipartUpload({ bucket, key, contentType }) {

        const params = {
            Bucket: bucket,
            Key: key,
            ContentType: contentType
        };

        try {
            const response = await this.s3.createMultipartUpload(params).promise();
            return {
                uploadId: response.UploadId,
                key: response.Key
            };
        }
        catch (error) {
            throw new Error(`Failed to initiate multipart upload: ${error.message}`);
        }
    }

    /**
     * Get a presigned URL for uploading a part
     * @param {object} options
     * @param {string} options.bucket - The name of the S3 bucket
     * @param {string} options.key - The object key
     * @param {string} options.uploadId - The upload ID
     * @param {number} options.partNumber - The part number
     * @param {number} options.expires - URL expiration time in seconds
     * @returns {Promise<string>} The presigned URL for the part
     */
    async getMultipartPresignedUrl({ bucket, key, uploadId, partNumber, expires = 3600 }) {

        const params = {
            Bucket: bucket,
            Key: key,
            UploadId: uploadId,
            PartNumber: partNumber,
            Expires: expires
        };

        try {
            return await this.s3.getSignedUrlPromise('uploadPart', params);
        }
        catch (error) {
            throw new Error(`Failed to generate presigned URL for part: ${error.message}`);
        }
    }

    /**
     * Complete a multipart upload
     * @param {object} options
     * @param {string} options.bucket - The name of the S3 bucket
     * @param {string} options.key - The object key
     * @param {string} options.uploadId - The upload ID
     * @param {Array<{PartNumber: number, ETag: string}>} options.parts - The parts information
     * @returns {Promise<object>} The completion result
     */
    async completeMultipartUpload({ bucket, key, uploadId, parts }) {

        const params = {
            Bucket: bucket,
            Key: key,
            UploadId: uploadId,
            MultipartUpload: {
                Parts: parts
            }
        };

        try {
            return await this.s3.completeMultipartUpload(params).promise();
        }
        catch (error) {
            throw new Error(`Failed to complete multipart upload: ${error.message}`);
        }
    }

    /**
     * Abort a multipart upload
     * @param {object} options
     * @param {string} options.bucket - The name of the S3 bucket
     * @param {string} options.key - The object key
     * @param {string} options.uploadId - The upload ID
     * @returns {Promise<object>} The abort result
     */
    async abortMultipartUpload({ bucket, key, uploadId }) {

        const params = {
            Bucket: bucket,
            Key: key,
            UploadId: uploadId
        };

        try {
            return await this.s3.abortMultipartUpload(params).promise();
        }
        catch (error) {
            throw new Error(`Failed to abort multipart upload: ${error.message}`);
        }
    }

    /**
     * Generates a presigned URL for downloading a file from S3.
     * @param {object} options
     * @param {string} options.bucket - The name of the S3 bucket.
     * @param {string} options.key - An array of image objects with filepath.
     * @param {number} options.expires - URL expiration time in seconds (default: 3600).
     * @returns {string} - The presigned URL.
     */
    async getFileWithSignedKey({ bucket, key, name, expires = 3600 }) {

        const params = {
            Bucket: bucket,
            Key: key,
            Expires: expires,
            ResponseContentDisposition: `attachment; filename="${name}"`,
            ResponseContentType: 'application/zip'
        };

        try {
            return await this.s3.getSignedUrl('getObject', params);
        }
        catch (error) {
            throw new Error('Failed to generate presigned URL');
        }
    }
    /**
     * Sign a CloudFront URL with the configured private key.
     *
     * @param {Object} params - Parameters for signing
     * @param {string} params.url - The CloudFront URL to sign
     * @param {number} [params.expires=86400] - Expiration time in seconds from now
     * @returns {string} Signed CloudFront URL
     * @throws {Error} If signing fails
     */
    signCloudFrontUrl({ url, expires = 86400 }) {

        if (!url) {
            throw new Error('URL is required for CloudFront signing');
        }

        try {
            const expiresTimestamp = Math.floor(Date.now() / 1000) + expires;
            const formattedUrl = url.startsWith('https://') ? url : `https://${url}`;
            return this.signer.getSignedUrl({
                url: formattedUrl,
                expires: expiresTimestamp
            });
        }
        catch (error) {
            throw new Error(`Failed to sign CloudFront URL: ${error.message}`);
        }
    }

    // Add this method to your storage service
    signCloudFrontUrlWithWildcard({ url, basePath, expires = 3600 }) {

        if (!url || !basePath) {
            throw new Error('URL and basePath are required for CloudFront wildcard signing');
        }

        try {
            const expiresTimestamp = Math.floor(Date.now() / 1000) + expires;
            const formattedUrl = url.startsWith('https://') ? url : `https://${url}`;
            const formattedBasePath = basePath.startsWith('https://') ? basePath : `https://${basePath}`;

            // Create custom policy with wildcard
            const policy = {
                Statement: [{
                    Resource: `${formattedBasePath}/*`, // Wildcard to include all files
                    Condition: {
                        DateLessThan: {
                            'AWS:EpochTime': expiresTimestamp
                        }
                    }
                }]
            };

            // Sign with custom policy
            return this.signer.getSignedUrl({
                url: formattedUrl,
                policy: JSON.stringify(policy)
            });
        }
        catch (error) {
            throw new Error(`Failed to sign CloudFront URL with wildcard: ${error.message}`);
        }
    }

    /**
     * Generate a pre-signed S3 URL for getting an object.
     *
     * @param {Object} params - Parameters for signing
     * @param {string} params.bucket - The name of the S3 bucket
     * @param {string} params.key - The object key in the bucket
     * @param {number} [params.expires=3600] - Expiration time in seconds
     * @param {Object} [params.options={}] - Additional options for the signed URL
     * @returns {string} Pre-signed S3 URL
     * @throws {Error} If signing fails
     */
    async signS3Url({ bucket, key, expires = 3600, options = {} }) {

        if (!bucket || !key) {
            throw new Error('Bucket and key are required for S3 URL signing');
        }

        try {
            const params = {
                Bucket: bucket,
                Key: key,
                Expires: expires,
                ...options
            };

            return await this.s3.getSignedUrlPromise('getObject', params);
        }
        catch (error) {
            throw new Error(`Failed to generate pre-signed S3 URL: ${error.message}`);
        }
    }

    signCookies({ pattern, expires = 3600 }) {

        if (!this.signer) {
            throw new Error('CloudFront signer is not configured');
        }

        const params = {
            policy: JSON.stringify({
                Statement: [
                    {
                        Resource: pattern,
                        Condition: {
                            DateLessThan: {
                                'AWS:EpochTime': Math.floor(Date.now() / 1000) + expires
                            }
                        }
                    }
                ]
            })
        };

        try {
            return this.signer.getSignedCookie(params);
        }
        catch (error) {
            throw new Error(`Failed to sign CloudFront URL: ${error.message}`);
        }
    }

    /**
     * Calculates the total size of all objects in multiple S3 folders.
     * @param {string} bucketName - The name of the S3 bucket.
     * @param {string[]} folderPaths - Array of folder paths in the S3 bucket (e.g., ['path/to/folder1/', 'path/to/folder2/']).
     * @returns {Promise<Object>} - An object mapping folder paths to their sizes in bytes.
     */
    async getMultipleFolderSizes(bucketName, folderPaths) {

        try {
            // Normalize folder paths to ensure they end with a slash
            const normalizedFolderPaths = folderPaths.map((path) => (path.endsWith('/') ? path : `${path}/`));

            // Process all folders concurrently
            const sizePromises = normalizedFolderPaths.map(async (folderPath) => {

                let totalSize = 0;
                let continuationToken = null;

                do {
                    const params = {
                        Bucket: bucketName,
                        Prefix: folderPath,
                        ContinuationToken: continuationToken
                    };

                    const response = await this.s3.listObjectsV2(params).promise();

                    // Sum up the size of all objects in this page
                    if (response.Contents && response.Contents.length > 0) {
                        totalSize += response.Contents.reduce((sum, object) => sum + (object.Size || 0), 0);
                    }

                    // Check if there are more objects to fetch
                    continuationToken = response.IsTruncated ? response.NextContinuationToken : null;

                } while (continuationToken);

                return { folderPath, size: totalSize };
            });

            // Wait for all folder size calculations to complete
            const results = await Promise.all(sizePromises);
            let totalSize = 0;

            results.forEach((result) => {

                totalSize += result.size;
            });

            return totalSize;
        }
        catch (error) {
            console.error('Error calculating folder sizes:', error);
            throw new Error('Failed to calculate folder sizes');
        }
    }
};
