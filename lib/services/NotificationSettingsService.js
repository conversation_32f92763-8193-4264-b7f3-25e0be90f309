'use strict';

const Schmervice = require('schmervice');

module.exports = class NotificationSettingsService extends Schmervice.Service {

    /**
     * Get setting by id
     *
     * @param {Number} id
     *  The id of the setting
     *
     * @return {Object} settings
     * Object of settings
     */
    async getSettingById({ id }) {

        const { NotificationSettings } = this.server.models();
        return await NotificationSettings.query().findById(id);
    }

    /**
     * Returns all children of specific parent
     *
     * @param {Number} id
     *  The id of the parent
     *
     * @return {Array} settings
     * Array of children settings
     */
    async getAllChildrenOfSettings({ id }) {

        const { NotificationSettings } = this.server.models();
        return await NotificationSettings.query().where('parent_id', id);
    }

    /**
     * Returns all parent settings
     *
     * @param {String} user_type
     *  The id of the parent
     *
     * @return {Array} settings
     * Array of all parents settings for user_type
     */
    async getAllParentForUserType({ user_type }) {

        const { NotificationSettings } = this.server.models();
        return await NotificationSettings.query().where('user_type', user_type).where('parent_id', null);
    }

    /**
     * Returns all settings groupped for user
     *
     * @return {Array} settings
     * All the settings grupped for user type
     */
    async getAllSettingsGrouppedForUserType() {

        const { NotificationSettings } = this.server.models();
        const { notificationSettingsService } = this.server.services();

        const user_types = await NotificationSettings.query().distinct('user_type');
        const allOrderedSettings = {};

        for (const {  user_type: type } of user_types) {

            allOrderedSettings[type] = {};
            allOrderedSettings[type] = await notificationSettingsService.getAllParentForUserType({ user_type: type });

            for (let i = 0; i < allOrderedSettings[type].length; ++i) {

                const notification_setting = allOrderedSettings[type][i];
                allOrderedSettings[type][i].children =
                    await notificationSettingsService.getAllChildrenOfSettings({ id: notification_setting.id });
            }
        }

        return allOrderedSettings;
    }

    /**
     * Change the status of a specific setting.
     * If it is a parent change also the status off alla child
     * If it is a child and the status is true change also the status of the parent.
     *
     * * @param {Number} id
     *  The id of the parent
     *
     * * @param {String} statud
     *  The new status of the setting
     */
    async setSettingStatusById({ id, status }) {

        const { NotificationSettings } = this.server.models();
        const { notificationSettingsService } = this.server.services();

        const idArray = [id];

        const { parent_id } = await notificationSettingsService.getSettingById({ id });

        const childIds = (await notificationSettingsService.getAllChildrenOfSettings({ id })).map(({ id }) => id );

        if (parent_id &&  status) {
            await NotificationSettings.query().patchAndFetchById( parent_id, {  value: status });
        }
        else if ( parent_id &&  !status ) {
            const children = await NotificationSettings.query().where('parent_id', parent_id);
            const count = children.filter((child) => child.value).length;
            //the count must be 1 becouse i haven't do update the target settings yet
            if (count === 1) {
                idArray.push(parent_id);
            }
        }

        await NotificationSettings.query().patch({ value: status }).whereIn('id', idArray.concat(childIds));
        return;
    }

    /**
     * Check the status of the setting for role and email_type to decide if the email will be send
     *
     *  @param {String} role
     *  Role of the user that we want to check
     *
     * * @param {String} email_type
     *  Email type that we want to check
     */
    async checkIfEmailIsToSend({ role, email_type }) {

        const { NotificationSettings } = this.server.models();

        const condition = await NotificationSettings.query().where('user_type', role).where('key', email_type).first();

        return condition && condition.value;

    }
};
