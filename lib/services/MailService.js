'use strict';

const Schmervice = require('schmervice');
const AWS = require('aws-sdk');

module.exports = class MailService extends Schmervice.Service {

    /**
     * This function initializes the AWS SES client for mail sending.
     *
     */
    initialize() {

        const SES_CONFIG = {
            accessKeyId: this.server.settings.app.amazon.aws_id,
            secretAccessKey: this.server.settings.app.amazon.aws_secret,
            region: this.server.settings.app.amazon.aws_region
        };

        this.AWS_SES = new AWS.SES(SES_CONFIG);
    }

    /**
     * This function uses aws SES to send bulk emails with the specified destinations and using the specified template
     *
     * @param {String} template
     *  The name of the template
     *
     * @param {Object} destinations
     *  The object containing the destinations for the emails
     *
     * @param {String} defaultData
     *  A string containing a JSON object specifying the default values for the parameters contained in the template body
     *
     *
     * @return {Object} The email promise.
     */
    async sendMultipleTemplatedEmails({ template, destinations, defaultData }) {

        destinations = destinations.map((destination) => {

            return {
                Destination: destination.Destination,
                ReplacementTemplateData: JSON.stringify(destination.ReplacementTemplateData)
            };
        });

        if (destinations.length > 0) {
            defaultData = JSON.stringify(defaultData);
            const params = {
                Source: this.server.settings.app.amazon.default_email_sender,
                Template: template,
                Destinations: destinations,
                DefaultTemplateData: defaultData
            };
            return await this.AWS_SES.sendBulkTemplatedEmail(params).promise();
        }

        this.server.logger.warn('No email was sent due to no destination provided');

        return;
    }

    /**
     * This function uses aws SES to send an email to the specified recipient with the specified body and subject
     *
     * @param {String} recipient
     *  The recipient email address
     *
     * @param {String} subject
     *  The email subject
     *
     * @param {String} htmlBody
     *  The email body in html format
     *
     *  @param {String} textBody
     *  The email body in plaintext format
     *
     * @return {Object} The email promise.
     */
    async sendEmail({ recipient, subject, htmlBody, textBody }) {

        const params = {
            Source: this.server.settings.app.amazon.default_email_sender,
            Destination: {
                ToAddresses: [
                    recipient
                ]
            },
            Message: {
                Body: {
                    Html: {
                        Charset: 'UTF-8',
                        Data: htmlBody
                    },
                    Text: {
                        Charset: 'UTF-8',
                        Data: textBody
                    }
                },
                Subject: {
                    Charset: 'UTF-8',
                    Data: subject
                }
            }
        };

        return await this.AWS_SES.sendEmail(params).promise();
    }

    /**
     * This function uses aws SES to send an email to the specified recipient, using the specified template and data
     *
     * @param {String} recipient
     *  The recipient email address
     *
     * @param {String} template
     *  The name of the template
     *
     * @param {String} template_data
     *  A string containing a JSON object specifying the values for the parameters contained in the template body
     *
     * @return {Object} The email promise.
     */
    async sendTemplatedEMail({ recipient, template, template_data }) {

        const params = {
            Source: this.server.settings.app.amazon.default_email_sender,
            Template: template,
            TemplateData: template_data,
            Destination: {
                ToAddresses: [
                    recipient
                ]
            }
        };

        return await this.AWS_SES.sendTemplatedEmail(params).promise();
    }

    /**
     * This converts an array into a string formatted as a HTML unordered list.
     *
     * @param {Array} Array
     *  The input array
     *
     * @return {String} The formatted string.
     */
    arrayToList({ array }) {

        let arrayHTML = '<ul>';
        array.forEach((el) => {

            arrayHTML += `<li>${  el  }</li>`;
        });

        arrayHTML += '</ul>';
        return arrayHTML;
    }

};
