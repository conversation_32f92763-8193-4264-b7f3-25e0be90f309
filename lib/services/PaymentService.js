'use strict';

const Schmervice = require('schmervice');
const Stripe = require('stripe');
const { ValidationError, NotFoundError, InternalError } = require('../helpers/errors');
const { numberToCentesimal } = require('../helpers/utils');

module.exports = class PaymentService extends Schmervice.Service {

    /**
     * This function initializes Stripe API.
     *
     */
    initialize() {

        this.stripe = Stripe(this.server.settings.app.stripe.stripe_secret);
    }

    /**
     * Starts payment procedure in stripe given payment method or payment intent id and amount.
     *
     * @param {string} payment_method_id
     *  The id of the payment method chosen by the client
     *
     * @param {string} payment_intent_id
     *  The id of the payment intent
     *
     * @param {string} stripe_customer_id
     *  The stripe customer id related to the user
     *
     * @param {string} amount
     *  The amount to be collected by the transaction
     *
     * @param {string} description
     *  The description to be associated to order product
     *
     * @return {Object} billing_address
     *  The billing address
     *
     * @return {Object} intent
     *  The payment intent object.
     *
     * @return {Object} invoice
     *  The invoice object
     */

    async performPayment({ payment_method_id, payment_intent_id, amount, stripe_customer_id, description }) {

        let intent;
        let invoice;

        const { address } = await this.getStripeCustomer({ stripe_customer_id });
        const { country: tax_country = null } = await this.getStripeCustomerTaxId({ stripe_customer_id }) || {};
        if (!payment_intent_id) {
            const { id: tax_rate } = (tax_country === 'AE') && await this.retrieveTaxRate({ tax_country });

            try {
                await this.stripe.invoiceItems.create({
                    customer: stripe_customer_id,
                    unit_amount: numberToCentesimal(amount),
                    currency: 'aed',
                    quantity: 1,
                    description,
                    tax_rates: [tax_rate]
                });
            }
            catch (error) {
                throw new InternalError('Invoice item cannot be created');
            }

            try {
                invoice = await this.stripe.invoices.create({
                    customer: stripe_customer_id
                });

                invoice = await this.stripe.invoices.finalizeInvoice(
                    invoice.id
                );
            }
            catch (error) {
                throw new InternalError('Invoice cannot be created due to internal error');
            }

        }

        if (payment_method_id) {
            await this.attachPaymentMethodToStripeCustomer({ stripe_customer_id, payment_method_id });
            intent = await this.stripe.paymentIntents.confirm(
                invoice.payment_intent,
                { payment_method: payment_method_id });
        }
        else if (payment_intent_id) {
            intent = await this.stripe.paymentIntents.confirm(
                payment_intent_id
            );
        }
        else {
            throw new ValidationError('payment_method_id and payment_intent_id are missing');
        }

        return { intent, invoice, billing_address: address };
    }

    /**
     * Create a stripe customer
     *
     * @param {string} email
     *  The email of the user
     *
     * @param {string} name
     *  The name of the company
     *
     *
     * @return {Object} customer
     *  The stripe customer object.
     */
    async createStripeCustomer({ email, name }) {

        try {
            const customer = await this.stripe.customers.create({
                email,
                name
            });
            return customer;
        }
        catch (error) {
            throw new ValidationError('Customer cannot be created due to invalid parameters');
        }
    }

    /**
     * Attach tax id number to stripe customer
     *
     * @param {string} stripe_customer_id
     *  The id of the stripe customer
     *
     * @param {string} tax_id
     *  The tax id number
     */
    async attachTaxIdToStripeCustomer({ stripe_customer_id, tax_id }) {

        //it is possible to synchronize a stripe customer bypassing the attachment of tax_id (as required by Flashy)
        if (!tax_id) {
            return;
        }

        try {
            const { data: tax } = await this.stripe.customers.listTaxIds(
                stripe_customer_id,
                { limit: 1 }
            );

            if (tax[0]) {
                await this.stripe.customers.deleteTaxId(
                    stripe_customer_id,
                    tax[0].id
                );
            }

            await this.stripe.customers.createTaxId(
                stripe_customer_id,
                {
                    type: 'ae_trn',  //TBD define dynamic type selection in case of different countries
                    value: tax_id
                }
            );
        }
        catch (error) {
            if (error.code === 'tax_id_invalid') {
                throw new ValidationError('Invalid tax id');
            }
            else if (error.code === 'resource_missing') {
                throw new NotFoundError('Customer not found or invalid customer id');
            }
            else {
                throw new InternalError('An internal error occurred');
            }
        }
    }

    /**
     * Synchronize the stripe customer
     *
     * @param {string} stripe_customer_id
     *  The id of the stripe customer
     *
     * @param {string} tax_id
     *  The tax id number
     *
     * @param {Object} billing_address
     *  The billing address object
     *
     * @param {string} country
     *  The country
     *
     * @param {string} name
     *  The company name
     */
    async synchronizeStripeCustomer({ stripe_customer_id, tax_id, billing_address, country = 'AE', name }) {
        //TBD how to handle different country tax ids
        if (country === 'AE') {

            await this.attachTaxIdToStripeCustomer({ stripe_customer_id, tax_id });

        }

        try {
            await this.stripe.customers.update(
                stripe_customer_id,
                {
                    address: billing_address,
                    name
                }
            );
        }
        catch (error) {
            if (error.code === 'resource_missing') {
                throw new NotFoundError('Customer not found or invalid customer id');
            }
            else {
                throw new InternalError('An internal error occurred');
            }
        }

    }

    /**
     * Attach payment method to stripe customer
     *
     * @param {string} stripe_customer_id
     *  The id of the stripe customer
     *
     * @param {string} payment_method_id
     *  The tax id number
     */
    async attachPaymentMethodToStripeCustomer({ stripe_customer_id, payment_method_id }) {

        try {
            await this.stripe.paymentMethods.attach(
                payment_method_id,
                { customer: stripe_customer_id }
            );
        }
        catch (error) {
            if (error.code === 'resource_missing') {
                throw new NotFoundError('Customer or card method id not valid');
            }
            else {
                throw new InternalError('An internal error occurred');
            }
        }

    }

    /**
     * Get a stripe customer
     *
     * @param {string} stripe_customer_id
     *  The email of the user
     *
     * @return {Object} customer
     *  The stripe customer object.
     */
    async getStripeCustomer({ stripe_customer_id }) {

        try {
            const customer = await this.stripe.customers.retrieve(stripe_customer_id);
            return customer;
        }
        catch (error) {
            throw new NotFoundError('Customer not found');
        }

    }

    /**
     * Retrieve a stripe customer tax id
     *
     * @param {string} stripe_customer_id
     *  The email of the user
     *
     * @return {Object} taxId
     *  The stripe tax id object.
     */
    async getStripeCustomerTaxId({ stripe_customer_id }) {

        try {
            const { data: tax } = await this.stripe.customers.listTaxIds(
                stripe_customer_id,
                { limit: 1 }
            );
            return tax[0];
        }
        catch (error) {
            throw new ValidationError('Customer id not valid');
        }

    }

    /**
     * Retrieve a stripe tax rate given country
     *
     * @param {string} tax_country
     *  The country
     *
     * @return {Object} tax
     *  The stripe tax rate object.
     */
    async retrieveTaxRate({ tax_country }) {

        //as requested by the Flashy, tax rate are now applied only for customers from UAE
        if ( tax_country !== 'AE') {
            return;
        }

        let tax;
        //retrieve from stripe currently available tax rates selecting country
        const { data: taxes } = await this.stripe.taxRates.list();
        if (taxes.length !== 0 ) {
            tax = taxes.filter(({ jurisdiction }) => (jurisdiction === tax_country))[0];

        }

        //if there are now tax rates available in general or in the selected country it must be created
        try {
            if (!tax) {
                tax = await this.stripe.taxRates.create({
                    display_name: 'VAT',
                    description: `VAT ${tax_country}`, //the description shown in invoice pdf
                    jurisdiction: tax_country,  //jurisdiction as ISO 3166-1 alpha-2 code (legal indication of country)
                    percentage: 5, //the percentage requested by Flashy
                    inclusive: false, //if taxes must be deducted or added to total
                    country: tax_country //country as ISO 3166-1 alpha-2 code
                });
            }

            return tax;
        }
        catch (error) {
            throw new ValidationError('Tax rate cannot be created due to not valid parameters');
        }

    }
};
