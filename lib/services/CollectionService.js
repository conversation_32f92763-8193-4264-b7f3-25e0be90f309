'use strict';

const Schmervice = require('schmervice');
const { ValidationError } = require('../helpers/errors');

module.exports = class CollectionService extends Schmervice.Service {

    /**
     * Create a new collection for a user
     *
     * @param {number} userId - The ID of the user creating the collection
     * @param {string} name - The name of the collection
     * @returns {Object} The created collection
     */
    async createCollection(userId, name) {

        if (!userId || !name) {
            throw new ValidationError('User ID and collection name are required');
        }

        const { Collection } = this.server.models();

        // Check if user already has a collection with this name
        const existingCollection = await Collection.query()
            .where({
                user_id: userId,
                name
            })
            .first();

        if (existingCollection) {
            throw new ValidationError(`A collection with the name "${name}" already exists`);
        }

        // Create the new collection
        const newCollection = await Collection.query().insert({
            user_id: userId,
            name,
            created_at: new Date(),
            updated_at: new Date()
        });

        return newCollection;
    }

    /**
     * Get all collections for a user with item counts
     *
     * @param {number} userId - The ID of the user
     * @returns {Array} Array of collections with item counts
     */
    async getUserCollections(userId) {

        if (!userId) {
            throw new ValidationError('User ID is required');
        }

        const { Collection } = this.server.models();

        // Get all collections for the user and include item counts
        // Using a subquery for better performance with large collections
        const collections = await Collection.query()
            .where('user_id', userId)
            .select(
                'collections.*',
                Collection.relatedQuery('collectionItems')
                    .count()
                    .as('item_count')
            )
            .orderBy('created_at', 'desc');

        return collections;
    }

    /**
     * Update a collection's information
     *
     * @param {number} collectionId - The ID of the collection to update
     * @param {number} userId - The ID of the user who owns the collection
     * @param {Object} updateData - The data to update (currently only name)
     * @returns {Object} The updated collection
     */
    async updateCollection(collectionId, userId, updateData) {

        if (!collectionId || !userId) {
            throw new ValidationError('Collection ID and User ID are required');
        }

        const { Collection } = this.server.models();

        // Verify the collection exists and belongs to the user
        const collection = await Collection.query()
            .where({
                id: collectionId,
                user_id: userId
            })
            .first();

        if (!collection) {
            throw new ValidationError('Collection not found or does not belong to the user');
        }

        // If updating the name, check for uniqueness
        if (updateData.name && updateData.name !== collection.name) {
            const existingCollection = await Collection.query()
                .where({
                    user_id: userId,
                    name: updateData.name
                })
                .first();

            if (existingCollection) {
                throw new ValidationError(`A collection with the name "${updateData.name}" already exists`);
            }
        }

        // Update the collection
        const updatedCollection = await Collection.query()
            .patchAndFetchById(collectionId, {
                ...updateData,
                updated_at: new Date()
            });

        return updatedCollection;
    }

    /**
     * Delete a collection and all its item associations
     *
     * @param {number} collectionId - The ID of the collection to delete
     * @param {number} userId - The ID of the user who owns the collection
     * @returns {boolean} Success status
     */
    async deleteCollection(collectionId, userId) {

        if (!collectionId || !userId) {
            throw new ValidationError('Collection ID and User ID are required');
        }

        const { Collection, CollectionItem } = this.server.models();

        // Verify the collection exists and belongs to the user
        const collection = await Collection.query()
            .where({
                id: collectionId,
                user_id: userId
            })
            .first();

        if (!collection) {
            throw new ValidationError('Collection not found or does not belong to the user');
        }

        // Use a transaction to ensure data integrity
        await Collection.transaction(async (trx) => {
            // First delete all collection items (associations)
            await CollectionItem.query(trx)
                .where('collection_id', collectionId)
                .delete();

            // Then delete the collection itself
            await Collection.query(trx)
                .deleteById(collectionId);
        });

        return true;
    }

    /**
     * Add multiple images to a collection with duplicate prevention
     *
     * @param {number} collectionId - The ID of the collection to add items to
     * @param {number} userId - The ID of the user who owns the collection
     * @param {Array<number>} ids - Array of image IDs to add to the collection
     * @param {string} type - The type of content ('image' or 'video')
     * @returns {Object} The updated collection with item count
     */
    async addItemsToCollection(collectionId, userId, ids, type) {

        if (!collectionId || !userId || !ids || !ids.length) {
            throw new ValidationError('Collection ID, User ID, and at least one image ID are required');
        }

        const { Collection, CollectionItem } = this.server.models();
        let Content = null;

        if (type === 'image') {
            Content = this.server.models().Image;
        }
        else {
            Content = this.server.models().Video;
        }

        // Verify the collection exists and belongs to the user
        const collection = await Collection.query()
            .where({
                id: collectionId,
                user_id: userId
            })
            .first();

        if (!collection) {
            throw new ValidationError('Collection not found or does not belong to the user');
        }

        // Verify all images exist
        const contents = await Content.query().whereIn('id', ids);
        if (contents.length !== contents.length) {
            throw new ValidationError('One or more contents IDs are invalid');
        }

        let existingItems = [];

        if (type === 'image') {
            // Find existing items in the collection to prevent duplicates
            existingItems = await CollectionItem.query()
                .where('collection_id', collectionId)
                .whereIn('image_id', ids);
        }
        else {
            // Find existing items in the collection to prevent duplicates
            existingItems = await CollectionItem.query()
                .where('collection_id', collectionId)
                .whereIn('video_id', ids);
        }

        let existingContentIdSet = null;
        // Get the image IDs that are already in the collection
        if (type === 'image') {
            existingContentIdSet = new Set(existingItems.map((item) => item.image_id));
        }
        else {
            existingContentIdSet = new Set(existingItems.map((item) => item.video_id));
        }

        // Filter out the image IDs that are not already in the collection
        const newContentIds = ids.filter((id) => !existingContentIdSet.has(id));

        // If there are no new images to add, return the collection with current item count
        if (newContentIds.length === 0) {
            // Get the collection with item count
            const collectionWithCount = await Collection.query()
                .where('id', collectionId)
                .select(
                    'collections.*',
                    Collection.relatedQuery('collectionItems')
                        .count()
                        .as('item_count')
                )
                .first();

            return collectionWithCount;
        }

        // Use a transaction to ensure data integrity
        let updatedCollection;
        await Collection.transaction(async (trx) => {

            let itemsToInsert = [];
            let itemVideoInsert = [];
            let itemImageInsert = [];

            if (type === 'image') {
                // Prepare the items to insert
                itemImageInsert = ids.map((id) => ({
                    collection_id: collectionId,
                    image_id: id,
                    video_id: null,
                    added_at: new Date()
                }));
            }
            else {
                // Prepare the items to insert
                itemVideoInsert = ids.map((id) => ({
                    collection_id: collectionId,
                    image_id: null,
                    video_id: id,
                    added_at: new Date()
                }));
            }

            itemsToInsert = [
                ...itemImageInsert,
                ...itemVideoInsert
            ];

            if (itemsToInsert.length === 0) {
                // No new items to insert
                return;
            }

            // Insert the new items
            await CollectionItem.query(trx).insert(itemsToInsert);

            // Update the collection's updated_at timestamp
            await Collection.query(trx)
                .patch({ updated_at: new Date() })
                .where('id', collectionId);

            // Get the updated collection with item count
            updatedCollection = await Collection.query(trx)
                .where('id', collectionId)
                .select(
                    'collections.*',
                    Collection.relatedQuery('collectionItems', trx)
                        .count()
                        .as('item_count')
                )
                .first();
        });

        return updatedCollection;
    }
    /**
     * Remove multiple images from a collection
     *
     * @param {number} collectionId - The ID of the collection to remove items from
     * @param {number} userId - The ID of the user who owns the collection
     * @param {Array<number>} imageIds - Array of image IDs to remove from the collection
     * @returns {Object} Object containing the count of removed items
     */
    async removeItemsFromCollection(collectionId, userId, imageIds) {

        if (!collectionId || !userId || !imageIds || !imageIds.length) {
            throw new ValidationError('Collection ID, User ID, and at least one image ID are required');
        }

        const { Collection, CollectionItem } = this.server.models();

        // Verify the collection exists and belongs to the user
        const collection = await Collection.query()
            .where({
                id: collectionId,
                user_id: userId
            })
            .first();

        if (!collection) {
            throw new ValidationError('Collection not found or does not belong to the user');
        }

        // Use a transaction to ensure data integrity
        let removedCount = 0;
        await Collection.transaction(async (trx) => {
            // Delete the specified collection items
            removedCount = await CollectionItem.query(trx)
                .where('collection_id', collectionId)
                .whereIn('image_id', imageIds)
                .delete();

            // Update the collection's updated_at timestamp if items were removed
            if (removedCount > 0) {
                await Collection.query(trx)
                    .patch({ updated_at: new Date() })
                    .where('id', collectionId);
            }
        });

        return { removedCount };
    }
};
