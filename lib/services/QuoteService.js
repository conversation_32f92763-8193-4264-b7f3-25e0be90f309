/* eslint-disable max-len */
'use strict';

const Schmervice = require('schmervice');
const { Pinecone } = require('@pinecone-database/pinecone');
const OpenAI = require('openai');
const { Readable } = require('stream');

module.exports = class QuoteService extends Schmervice.Service {

    constructor(server, options) {

        super(server, options);

        this.pinecone = new Pinecone({
            apiKey: this.server.settings.app.pinecone.api_key
        });

        this.openai = new OpenAI({
            apiKey: this.server.settings.app.openai.api_key
        });

        this.INDEX_NAME = 'invoices-store';
        this.embeddingsCache = new Map();

    }

    /**
     * Generate a quote based on provided services and stream the results.
     * @param {Array<Object>} services - An array of service objects to generate a quote for.
     */
    async generateQuoteStream(services) {

        try {
            const vectorStoreResults = await this.queryPinecone(services);
            const quoteStream = this.generateQuoteContentStream(services, vectorStoreResults);
            return { quoteStream, vectorStoreResults };
        }
        catch (error) {
            throw new Error(`Failed to generate quote stream: ${error}`);
        }
    }

    /**
     * Query Pinecone for similar services.
     * @param {Array<Object>} services - An array of service objects to query for.
     * @returns {Promise<Array<Object>>} An array of matching results from Pinecone.
     */
    async queryPinecone(services) {

        const index = this.pinecone.index(this.INDEX_NAME);
        const allResults = await Promise.all(services.map(async (service) => {

            const queryEmbedding = await this.getEmbeddings(service.description);
            const result = await index.query({
                vector: queryEmbedding,
                topK: 15,
                includeMetadata: true
            });
            return result.matches;
        }));

        return this.deduplicateAndSortResults(allResults.flat());
    }

    /**
     * Deduplicate and sort the results from Pinecone.
     * @param {Array<Object>} results - The raw results from Pinecone.
     * @returns {Array<Object>} Deduplicated and sorted results.
     */
    deduplicateAndSortResults(results) {

        const uniqueResults = Array.from(new Map(results.map((item) => [item.id, item])).values());
        return uniqueResults.sort((a, b) => b.score - a.score);
    }

    /**
     * Get embeddings for a given text.
     * @param {string} text - The text to get embeddings for.
     * @returns {Promise<Array<number>>} The embedding vector.
     */
    async getEmbeddings(text) {

        if (this.embeddingsCache.has(text)) {
            return this.embeddingsCache.get(text);
        }

        const response = await this.openai.embeddings.create({
            model: 'text-embedding-3-small',
            input: text
        });
        const { embedding } = response.data[0];
        this.embeddingsCache.set(text, embedding);
        return embedding;
    }

    /**
     * Generate quote content using OpenAI's GPT model and stream the results.
     * @param {Array<Object>} services - The services to generate a quote for.
     * @param {Array<Object>} vectorStoreResults - The results from Pinecone query.
     * @returns {Readable} A readable stream of the generated quote content.
     */
    generateQuoteContentStream(services, vectorStoreResults) {

        const prompt = this.createPrompt(services, vectorStoreResults);
        const stream = new Readable({
            read() {}
        });

        this.openai.chat.completions.create({
            model: 'gpt-4-turbo-preview',
            messages: [
                { role: 'system', content: 'You are an expert quotation generator for content creation services, specializing in photography and videography. Provide accurate and consistent pricing based on the given data.' },
                { role: 'user', content: prompt }
            ],
            temperature: 0.1,
            stream: true
        }).then(async (response) => {

            for await (const chunk of response) {
                const content = chunk.choices[0]?.delta?.content || '';
                if (content) {
                    stream.push(content);
                }
            }

            stream.push(null); // End of stream
        }).catch((error) => {

            stream.emit('error', error);
            throw new Error(`Failed to generate quote content stream: ${error}`);
        });

        return stream;
    }

    /**
     * Create a prompt for the OpenAI model based on services and vector store results.
     * @param {Array<Object>} services - The services to generate a quote for.
     * @param {Array<Object>} vectorStoreResults - The results from Pinecone query.
     * @returns {string} The generated prompt.
     */
    createPrompt(services, vectorStoreResults) {

        const parsedMetadata = vectorStoreResults.map((match) => {

            return {
                company: match.metadata.company,
                full_text: match.metadata.full_text,
                invoice_date: match.metadata.invoice_date,
                invoice_number: match.metadata.invoice_number,
                services: match.metadata.services,
                total_amount: match.metadata.total_amount
            };
        });

        return `
    Generate a detailed and accurate quotation for the following services:
    
    ${JSON.stringify(services, null, 2)}
    
    Use the following data from similar services to inform your pricing:
    
    ${JSON.stringify(parsedMetadata, null, 2)}
    
    Additionally, consider the following service descriptions, quantity guidelines, and pricing examples:
    
    Service Descriptions:
    | Service Sample    | Description                                   |
    |-------------------|-----------------------------------------------|
    | Accommodation     | Specifies for whom, not always in numbers     |
    | Extras            | Non standardized                              |
    | Photography       | Includes number of professionals              |
    | Videography       | Includes number of professionals              |
    | Assistant         | Includes number of professionals              |
    | Project Manager   | Includes number of professionals              |
    | Models            | Includes number of professionals              |
    | Child Models      | Includes number of professionals              |
    | Hair & Makeup     | Includes number of professionals              |
    | Wardrobe Stylist  | Includes number of professionals              |
    | Cast Coordinator  | Includes number of professionals              |
    | Catering          | Describes the service for number of people    |
    | Transportation    | Describes the service for number of people    |
    | Pre-production    | Describes the service                         |
    | Editing           | Describes the service                         |
    | Deliverables      | Describes the service                         |
    
    Quantity and Rate Guidelines:
    | Service           | Quantity                           | Rate Example | Quantity Example |
    |-------------------|-----------------------------------|--------------|-------------------|
    | Accommodation     | Multiplied by number of hotel nights | 650          | 4                 |
    | Photography       | Multiplied by number of days       | 6,000        | 3                 |
    | Videography       | Multiplied by number of days       | 8,000        | 3                 |
    | Assistant         | Multiplied by number of days       | 1,500        | 3                 |
    | Project Manager   | Multiplied by number of days       | 2,000        | 4                 |
    | Models            | Multiplied by number of days       | 6,000        | 3                 |
    | Hair & Makeup     | Multiplied by number of days       | 3,000        | 3                 |
    | Wardrobe Stylist  | Multiplied by number of days       | 2,000        | 3                 |
    | Cast Coordinator  | Multiplied by number of days       | 2,000        | 2                 |
    | Catering          | Multiplied by number of days       | 75           | 30                |
    | Transportation    | Multiplied by number of days       | 3,000        | 2                 |
    | Pre-production    | One-off                            | 5,000        | 1                 |
    | Editing           | One-off                            | 2,500        | 1                 |
    | Deliverables      | One-off                            | 7,000        | 1                 |
    
    Pricing Examples:
    | Service           | Description                                      | Price Example |
    |-------------------|--------------------------------------------------|---------------|
    | Accommodation     | 1photo+1videographer for 2 nights at 650/night/person | 2,600         |
    | Photography       | 1 photographer for 3 days                         | 18,000        |
    | Videography       | 1 videographer for 3 days                         | 24,000        |
    | Assistant         | 1 assistant for 3 days                            | 4,500         |
    | Project Manager   | 1 project manager for 4 days                      | 8,000         |
    | Models            | could be 3 models (2k each) for 3 days            | 18,000        |
    | Hair & Makeup     | 1 hair and makeup assistant for 3 days            | 9,000         |
    | Wardrobe Stylist  | 1 wardrobe assistant for 3 days                   | 6,000         |
    | Cast Coordinator  | 1 coordinator for 2 days                          | 4,000         |
    | Catering          | catering for entire production on site            | 2,250         |
    | Transportation    | 2 return tickets for photo+video                  | 6,000         |
    | Pre-production    | Preproduction work (quantity not linked to work days) | 5,000         |
    | Editing           | Editing work (quantity not linked to work days)   | 2,500         |
    | Deliverables      | Deliverables work (non-specified)                 | 7,000         |
    
    Please provide three estimates: Minimum, Average, and Maximum. Format your response as follows:
    
    1. For each estimate (Minimum, Average, Maximum), create a markdown table with the following columns:
       - Description ( generate a brief description of the service )
       - Tax
       - Quantity
       - Rate
       - Amount
    
    2. After each table, provide:
       - Subtotal
       - VAT Total (calculate based on the VAT table below)
       - Total (Subtotal + VAT Total)
    
    3. After all three estimates, provide a brief explanation of the rate estimates, considering factors such as:
       - Type of service (e.g., F&B Photography, Real Estate Photography, Production Services)
       - Duration of the shoot
       - Number of images or videos
       - Location (if specified)
       - Any additional services or equipment mentioned
    
    Use this format for each estimate:
    
    ## [Estimate Type] Estimate
    
    | Description        | Tax | Quantity | Rate | Amount |
    |--------------------|-----|----------|------|--------|
    |describe service [1]| SR  | [Qty]    | [Rate] | [Amount] |
    |describe service [2]| SR  | [Qty]    | [Rate] | [Amount] |
    ... (add rows for each service)
    
    Subtotal: [Subtotal]
    VAT Total: [VAT Amount]
    Total: [Total Amount]
    
    Repeat this format for Minimum, Average, and Maximum estimates.
    
    After all three estimates, provide your explanation:
    
    ## Explanation of Rate Estimates
    
    [Your explanation here]
    
    Important:
    - Base your pricing strictly on the metadata provided from the vector store results.
    - Ensure your pricing estimates are realistic and data-driven.
    - If there's insufficient information for a particular service, make a reasonable estimate based on the most similar services in the provided data.
    - Analyze patterns in pricing for similar services, considering all relevant factors.
    - If exact matches aren't available, interpolate based on the closest matches.
    
    VAT Calculation:
    Use the following table to determine the correct VAT rate:
    
    | Invoice to       | Service in UAE | Service in GCC (not UAE) | Service outside GCC |
    |------------------|-----------------|--------------------------|----------------------|
    | UAE entity       | 5% VAT          | 5% VAT                   | 5% VAT               |
    | non-UAE entity   | 5% VAT          | ZR Zero Rated (0%)       | ZR Zero Rated (0%)   |
    
    - Determine the service location based on the service description.
    - Apply the appropriate VAT rate according to the table.
    - Show your VAT calculations clearly in the estimates.
    `;
    }

};
