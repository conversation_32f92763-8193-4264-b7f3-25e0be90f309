'use strict';

const Schmervice = require('schmervice');

module.exports = class MailService extends Schmervice.Service {

    /**
     * This function uses aws SES to send bulk emails with the specified destinations and using the specified template
     *
     * @param {String} template
     *  The name of the template
     *
     * @param {Object} destinations
     *  The object containing the destinations for the emails
     *
     * @param {String} defaultData
     *  A string containing a JSON object specifying the default values for the parameters contained in the template body
     *
     *
     * @return {Object} The email promise.
     */
    // eslint-disable-next-line require-await
    async sendMultipleTemplatedEmails({ template, destinations, defaultData }) {

        this.server.logger.info({ template, destinations, defaultData }, 'Sending a (mocked) list of templated emails');

        return;
    }

    /**
     * This function uses aws SES to send an email to the specified recipient with the specified body and subject
     *
     * @param {String} recipient
     *  The recipient email address
     *
     * @param {String} subject
     *  The email subject
     *
     * @param {String} htmlBody
     *  The email body in html format
     *
     *  @param {String} textBody
     *  The email body in plaintext format
     *
     * @return {Object} The email promise.
     */
    // eslint-disable-next-line require-await
    async sendEmail({ recipient, subject, htmlBody, textBody }) {

        this.server.logger.info({ recipient, subject, htmlBody, textBody }, 'Sending a (mocked) email');

        return;
    }

    /**
     * This function uses aws SES to send an email to the specified recipient, using the specified template and data
     *
     * @param {String} recipient
     *  The recipient email address
     *
     * @param {String} template
     *  The name of the template
     *
     * @param {String} template_data
     *  A string containing a JSON object specifying the values for the parameters contained in the template body
     *
     * @return {Object} The email promise.
     */
    // eslint-disable-next-line require-await
    async sendTemplatedEMail({ recipient, template, template_data }) {

        this.server.logger.info({ recipient, template, template_data }, 'Sending a (mocked) templated email');

        return;
    }
};
