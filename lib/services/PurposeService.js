'use strict';

const Schmervice = require('schmervice');
const { NotFoundError } = require('../helpers/errors');

module.exports = class PurposeService extends Schmervice.Service {

    /**
     * Get purposes (from the db) given filters.
     *
     *
     * @return {Object} purposes
     *  The db purposes data.
     */
    async getPurposes() {

        const { Purpose } = this.server.models();

        const purposes = await Purpose.query();

        return { purposes };
    }

    /**
     * Get a purpose (from the db) given its id.
     *
     * @param {string} id
     *  The id of the purpose.
     *
     * @return {Object} The db purpose data.
     */
    async getPurpose({ id }) {

        const { Purpose } = this.server.models();

        const purpose = await Purpose.query().findById(id);

        if (!purpose) {
            throw new NotFoundError('Purpose not found');
        }

        return { purpose };
    }
};
