'use strict';

const Schmervice = require('schmervice');
const {
    convertStorageSizeStringToBytes
} = require('../helpers/utils');

/**
 * Service class for handling Stripe related operations.
 */
module.exports = class SubscriptionService extends Schmervice.Service {

    /**
     * Initializes the Stripe API.
     */
    initialize() {
    }

    async getPackagePlanByReference({ reference }) {

        const { PackagePlan } = this.server.models();
        const query = PackagePlan.query();

        // Check if the package plan already exists
        const existingPackagePlan = await query
            .where('stripe_reference', reference)
            .first();

        // If it exists, return the existing package plan
        if (existingPackagePlan) {
            return existingPackagePlan;
        }

        throw new Error('Package plan not found');
    }

    async getPackagePlanById({ id }) {

        const { PackagePlan } = this.server.models();
        const query = PackagePlan.query();

        // Check if the package plan already exists
        const existingPackagePlan = await query
            .where('id', id)
            .first();

        // If it exists, return the existing package plan
        if (existingPackagePlan) {
            return existingPackagePlan;
        }

        throw new Error('Package plan not found');
    }

    async createSubscriptionPlan({ ...plan }) {

        const { stripeService } = this.server.services();
        let subscriptionPlan = null;

        if (plan.amount) {
            subscriptionPlan = await stripeService.createSubscriptionPrice({
                name: plan.name,
                description: plan.description,
                unit_amount: plan.amount * 100,
                currency: plan.currency,
                interval: plan.interval,
                interval_count: plan.interval_count
            });
        }

        // Save the subscription plan to the database
        const { PackagePlan } = this.server.models();
        const query = PackagePlan.query();
        let existingSubscriptionPlan = null;

        if (subscriptionPlan) {
            // Check if the subscription plan already exists
            existingSubscriptionPlan = await query
                .where('stripe_reference', subscriptionPlan.id)
                .first();

            // If it exists, return the existing subscription plan
            if (existingSubscriptionPlan) {
                return existingSubscriptionPlan;
            }
        }

        // If it doesn't exist, create a new one
        existingSubscriptionPlan = await query.insert({
            name: plan.name,
            label: plan.label,
            description: plan.description,
            price: plan.amount,
            discount: plan.discount,
            origin_price: plan.origin_price,
            provider: 'stripe',
            plans: plan.plans,
            currency: plan.currency,
            interval: plan.interval,
            interval_count: plan.interval_count,
            status: 'active',
            stripe_reference: subscriptionPlan ? subscriptionPlan?.id : null
        });

        return { existingSubscriptionPlan };
    }

    async paySubscriptionPlan({ user, plan_id }) {

        const { paymentMethodService, stripeService, notificationService } = this.server.services();
        const { Billing, PackagePlan } = this.server.models();

        const defaultPaymentMethod = await paymentMethodService.getDefaultPaymentMethod({ user });
        const providerReference = await paymentMethodService.getProviderGateway({ user });

        if (!defaultPaymentMethod) {
            throw new Error('No default payment method found for user');
        }

        if (!providerReference) {
            throw new Error('No provider reference found for user');
        }

        // Get the subscription plan from the database
        const packagePlan = await PackagePlan.query()
            .where('id', plan_id)
            .first();

        if (!packagePlan) {
            throw new Error('Subscription plan not found');
        }

        await this.isSubscriptionPayValid({ user, requestPackagePlan: packagePlan });
        // Create subscription in Stripe
        const stripeSubscription = await stripeService.createSubscription({
            customer: providerReference.reference,
            price: packagePlan.stripe_reference,
            metadata: {
                user_id: user.id,
                plan_id: packagePlan.id
            }
        });

        const paymentIntent = stripeSubscription.latest_invoice.payment_intent;
        let required3ds = null;

        if (paymentIntent.status === 'requires_confirmation') {
            required3ds = {
                clientSecret: paymentIntent.client_secret
            };
        }

        const client = await user.client;
        // Save subscription in database
        const billing = await Billing.query().insert({
            user_id: user.id,
            package_id: packagePlan.id,
            name: packagePlan.name,
            address: client.main_location,
            phone: user.phone || null,
            email: user.email || null,
            status: required3ds ? 'pending_payment' : 'active',
            reference: stripeSubscription.id,
            period_start: new Date(stripeSubscription.current_period_start * 1000),
            period_end: new Date(stripeSubscription.current_period_end * 1000),
            canceled_at: stripeSubscription.canceled_at ? new Date(stripeSubscription.canceled_at * 1000) : null,
            amount: packagePlan.price,
            currency: packagePlan.currency,
            interval: packagePlan.interval,
            interval_count: packagePlan.interval_count
        });

        if (billing.status === 'active') {
            await notificationService.sendNotification({
                type: 'subscriptionSuccess',
                target: {
                    email: user.email,
                    client_id: user.client_id
                },
                metaData: {
                    plan_name: billing.name,
                    plans: packagePlan.plans,
                    user: {
                        first_name: user.name
                    },
                    subscription: {
                        start_date: billing.period_start.toISOString(),
                        renewal_date: billing.period_end.toISOString()
                    },
                    host: process.env.CURRENT_HOST
                }
            });
        }

        return {
            billing,
            required3ds
        };
    }

    async isSubscriptionPayValid({ user, requestPackagePlan }) {

        if (requestPackagePlan.price <= 0) {
            throw new Error('Please contact our sale team');
        }

        const paymentGateways = await user.userPaymentGateways;

        if (!paymentGateways || paymentGateways.length === 0) {
            throw new Error('No payment gateways found for user');
        }

        const { stripeService } = this.server.services();
        const providerReference = await paymentGateways[0].reference;
        const stripeSubscription = await stripeService.getCurrentActiveSubscription({
            customer: providerReference
        });

        if (stripeSubscription) {
            const currentPackagePlanReference = stripeSubscription.items.data[0].price.id;

            const currentPackagePlan = await this.getPackagePlanByReference({ reference: currentPackagePlanReference });

            if (!currentPackagePlan) {
                throw new Error('Package plan not found');
            }

            if (requestPackagePlan.id === currentPackagePlan.id) {
                throw new Error('Package plan is already active');
            }

            if (requestPackagePlan.weight <= currentPackagePlan.weight) {
                throw new Error('Cannot downgrade to a lower package plan');
            }
        }

        return true;
    }

    async getCurrentActiveSubscription({ user }) {

        const { Billing } = this.server.models();

        const query = Billing.query();
        const stripeSubscription = await query
            .where('user_id', user.id)
            .where('status', 'active')
            .first();

        return stripeSubscription;
    }

    async upgradeSubscription({ user, plan_id, currentSubscription }) {

        if (!currentSubscription) {
            throw new Error('No active subscription found');
        }

        // Get required services and models
        const { stripeService, paymentMethodService, notificationService } = this.server.services();
        const { Billing } = this.server.models();

        // Get the new package plan
        const newPackagePlan = await this.getPackagePlanById({ id: plan_id });
        if (!newPackagePlan) {
            throw new Error('New package plan not found');
        }

        // Check if upgrade is valid
        await this.isSubscriptionPayValid({ user, requestPackagePlan: newPackagePlan });

        // Get provider gateway for the user
        const providerReference = await paymentMethodService.getProviderGateway({ user });
        if (!providerReference) {
            throw new Error('No payment provider reference found');
        }

        const defaultPaymentMethod = await paymentMethodService.getDefaultPaymentMethod({ user });
        if (!defaultPaymentMethod) {
            throw new Error('No default payment method found for user');
        }

        // Check if subscription was set to cancel at period end and reset if needed
        const stripeSubscription = await stripeService.getSubscription(currentSubscription.id);
        if (stripeSubscription.cancel_at_period_end) {
            // Reset the cancellation status before upgrading
            await stripeService.stripe.subscriptions.update(currentSubscription.id, {
                cancel_at_period_end: false
            });
        }

        // Get proration info before proceeding with the upgrade
        const prorationPreview = await this.calculateProration({
            subscriptionId: currentSubscription.id,
            newPriceId: newPackagePlan.stripe_reference
        });

        // Update subscription in Stripe
        const { updatedStripeSubscription, requires3DS, clientSecret } = await stripeService.updateSubscription({
            subscriptionId: currentSubscription.id,
            newPriceId: newPackagePlan.stripe_reference,
            defaultPaymentMethod: defaultPaymentMethod.reference
        });

        // First fetch the subscription record before patching
        const currentInternalSubscription = await Billing.query()
            .where('reference', currentSubscription.id)
            .first();

        if (!currentInternalSubscription) {
            throw new Error('Current billing record not found');
        }

        const billingPayload = {
            package_id: newPackagePlan.id,
            name: newPackagePlan.name,
            amount: newPackagePlan.price,
            user_id: currentInternalSubscription.user_id,
            address: currentInternalSubscription.address,
            phone: currentInternalSubscription.phone,
            email: currentInternalSubscription.email,
            status: 'active',
            reference: updatedStripeSubscription.id,
            canceled_at: null, // Ensure canceled_at is null for upgraded subscription
            currency: newPackagePlan.currency,
            interval: newPackagePlan.interval,
            interval_count: newPackagePlan.interval_count,
            period_start: currentInternalSubscription.period_start,
            period_end: new Date(updatedStripeSubscription.current_period_end * 1000),
            updated_at: new Date()
        };

        // Check if 3DS authentication is required
        if (requires3DS) {
            billingPayload.status = 'pending_payment';
            // Update the billing record in the database
            await Billing.query()
                .insert(billingPayload);

            // Return early with 3DS authentication details
            return {
                subscription: null,
                required3ds: {
                    clientSecret
                },
                prorationPreview
            };
        }

        // Then patch it and continue with your logic
        await Billing.query()
            .where('reference', currentSubscription.id)
            .patch({
                status: 'inactive',
                canceled_at: null // Clear any cancellation timestamp
            });

        // Update the billing record in the database
        const newSubscription = await Billing.query()
            .insert(billingPayload);

        await notificationService.sendNotification({
            type: 'subscriptionSuccess',
            target: {
                email: user.email,
                client_id: user.client_id
            },
            metaData: {
                plan_name: newSubscription.name,
                plans: newPackagePlan.plans,
                user: {
                    first_name: user.name
                },
                subscription: {
                    start_date: newSubscription.period_start.toISOString(),
                    renewal_date: newSubscription.period_end.toISOString()
                },
                host: process.env.CURRENT_HOST
            }
        });

        return {
            required3ds: null,
            subscription: newSubscription,
            proration: prorationPreview
        };
    }

    async calculateProration({ subscriptionId, newPriceId }) {

        const { stripeService } = this.server.services();

        try {
            // Retrieve the current subscription to get the subscription item ID
            const subscription = await stripeService.getSubscription(subscriptionId);
            const subscriptionItemId = subscription.items.data[0].id;

            // Calculate proration by retrieving an upcoming invoice
            const invoice = await stripeService.retrieveUpcomingInvoice({
                subscription: subscriptionId,
                subscription_items: [{
                    id: subscriptionItemId,
                    price: newPriceId
                }]
            });

            // Format the proration information
            return {
                prorationDate: new Date(),
                amount: invoice.amount_due / 100, // Convert from cents to dollars
                currency: invoice.currency,
                details: invoice.lines.data.map((line) => ({
                    description: line.description,
                    amount: line.amount / 100,
                    period: {
                        start: new Date(line.period.start * 1000),
                        end: new Date(line.period.end * 1000)
                    }
                }))
            };
        }
        catch (error) {
            throw new Error(`Failed to calculate proration: ${error.message}`);
        }
    }

    async cancelSubscription({ user, immediately = false }) {

        // Get required services and models
        const { stripeService, paymentMethodService } = this.server.services();
        const { Billing } = this.server.models();

        // Get current active subscription
        const currentSubscription = await this.getCurrentActiveSubscription({ user });
        if (!currentSubscription) {
            throw new Error('No active subscription found to cancel');
        }

        // Get provider gateway for the user
        const providerReference = await paymentMethodService.getProviderGateway({ user });
        if (!providerReference) {
            throw new Error('No payment provider reference found');
        }

        try {
            // Cancel subscription in Stripe
            const cancelOptions = {
                prorate: true
            };

            // If immediately is true, cancel right now, otherwise at period end
            if (!immediately) {
                cancelOptions.cancel_at_period_end = true;
            }

            await stripeService.cancelSubscription({
                subscriptionId: currentSubscription.reference,
                ...cancelOptions
            });

            // Update the billing record in the database
            await Billing.query()
                .where('id', currentSubscription.id)
                .patch({
                    status: immediately ? 'canceled' : 'active',
                    canceled_at: new Date(),
                    updated_at: new Date()
                });

            // Return updated subscription
            return await Billing.query().findById(currentSubscription.id);
        }
        catch (error) {
            throw new Error(`Failed to cancel subscription: ${error.message}`);
        }
    }

    async getCurrentPlanFromUser({ user }) {

        const { Billing } = this.server.models();
        const query = Billing.query();

        const billing = await query
            .where('user_id', user.id)
            .where('status', 'active')
            .first();

        if (!billing) {
            return {
                plan: await this.getInitialPlanFromUser(),
                billing: null
            };
        }

        const { PackagePlan } = this.server.models();
        const queryPlan = PackagePlan.query();

        const plan = await queryPlan
            .where('id', billing.package_id)
            .first();

        if (!plan) {
            return {
                plan: await this.getInitialPlanFromUser(),
                billing: null
            };
        }

        return {
            plan,
            billing
        };
    }

    async getInitialPlanFromUser() {

        const { PackagePlan } = this.server.models();

        const queryPlan = PackagePlan.query();
        return await queryPlan
            .where('weight', 0)
            .where('price', null)
            .first();
    }

    async validateActionWithCurrentPlans({ user }) {

        const client = await user.client;

        if (!client) {
            return true;
        }

        const { plan } = await this.getCurrentPlanFromUser({ user });
        const { plans: { max_members, max_storage_size } } = plan;
        const { usageSummaryService } = this.server.services();
        const currentUsageSummary = await usageSummaryService.getCurrentUsageSummary({ client });

        if (!currentUsageSummary) {
            return true;
        }

        const { total_members, total_storage_size } = currentUsageSummary;

        if (total_members >= max_members) {
            throw new Error('Maximum number of members reached');
        }

        const maxStorageSizeInBytes = convertStorageSizeStringToBytes(max_storage_size);
        if (total_storage_size >= maxStorageSizeInBytes) {
            throw new Error('Maximum storage size reached');
        }
    }

    async getBillingHistory({ user }) {

        const { Billing } = this.server.models();
        const query = Billing.query();

        const billingHistory = await query
            .where('user_id', user.id)
            .orderBy('created_at', 'desc');

        if (!billingHistory) {
            return null;
        }

        return billingHistory;
    }

    async confirmSubscriptionPayment(user, paymentIntentId) {

        const { stripeService, notificationService } = this.server.services();
        const { Billing, PackagePlan } = this.server.models();

        // Verify payment intent with Stripe
        const confirmResult = await stripeService.confirmSubscriptionPayment(paymentIntentId);

        if (!confirmResult.success || !confirmResult.subscription) {
            throw new Error(`Payment confirmation failed: ${confirmResult.status || 'unknown status'}`);
        }

        // Use transaction to ensure database consistency
        const trx = await Billing.startTransaction();

        try {
            // Find the pending billing record
            const pendingBilling = await Billing.query(trx)
                .where({
                    reference: confirmResult.subscription,
                    user_id: user.id,
                    status: 'pending_payment'
                })
                .first();

            if (!pendingBilling) {
                await trx.rollback();
                throw new Error('No pending billing record found for this subscription');
            }

            // Find any existing active billing for this subscription
            const existingActiveBilling = await Billing.query(trx)
                .where({
                    reference: confirmResult.subscription,
                    user_id: user.id,
                    status: 'active'
                })
                .first();

            // Activate the pending billing
            const updatedBilling = await Billing.query(trx)
                .patchAndFetchById(pendingBilling.id, {
                    status: 'active',
                    updated_at: new Date()
                });

            // Deactivate any existing active billing
            if (existingActiveBilling) {
                await Billing.query(trx)
                    .patchAndFetchById(existingActiveBilling.id, {
                        status: 'inactive',
                        updated_at: new Date()
                    });
            }

            await trx.commit();

            // Get plan details for notification
            const packagePlan = await PackagePlan.query()
                .where('id', updatedBilling.package_id)
                .first();

            // Send notification about successful subscription
            if (packagePlan) {
                await notificationService.sendNotification({
                    type: 'subscriptionSuccess',
                    target: {
                        email: user.email,
                        client_id: user.client_id
                    },
                    metaData: {
                        plan_name: updatedBilling.name,
                        plans: packagePlan.plans,
                        user: { first_name: user.name },
                        subscription: {
                            start_date: updatedBilling.period_start.toISOString(),
                            renewal_date: updatedBilling.period_end.toISOString()
                        },
                        host: process.env.CURRENT_HOST
                    }
                });
            }

            return updatedBilling;
        }
        catch (error) {
            await trx.rollback();
            throw new Error(`Failed to confirm subscription payment: ${error.message}`);
        }
    }
};
