'use strict';

const Schmervice = require('schmervice');
const Shoot = require('../models/Shoot');
const { ValidationError } = require('../helpers/errors');

const buildTransitionKey = ({ role, from, to }) => `${role}$${from}$${to}`;

module.exports = class ShootStatusService extends Schmervice.Service {

    initialize() {

        const { Shoot, PhotographerShoot } = this.server.models();
        const { storageService, shootService } = this.server.services();

        const { toSchedule, scheduled, photographerAssigned, photosUploaded, confirmed, photosReady, canceled, completed } = Shoot.statuses;

        const {
            raw_photos_bucket_name: rawBucket,
            processed_photos_bucket_name: processedBucket
        } = this.server.settings.app.amazon;

        const conditions = {
            isScheduled: ({ shoot }) => shoot.scheduled,
            hasPhotographer: ({ shoot }) => shoot.photographer_id !== null,
            isAssignedPhotographer: ({ shoot, user }) => shoot.photographer_id === user.photographer_id,
            isProposedPhotographer: ({ shoot, user }) =>

                PhotographerShoot
                    .query()
                    .findById([user.photographer_id, shoot.id]).count().then(( { count } ) => count > 0 ),
            isPhotographerIdle: ({ shoot, user, photographerIdleMinutes = 90 }) =>

                shootService.getOverlappingShoots({ shoot, photographerIdleMinutes })
                    .where('status', Shoot.statuses.confirmed)
                    .where('photographer_id', user.photographer_id)
                    .count().first().then(( { count } ) => count === '0' ),
            noPhotographer: ({ shoot }) => shoot.photographer_id === null,
            rawPhotosUploaded: ({ shoot }) => storageService.objectExists({ bucket: rawBucket, filepath: `${shoot.id}/raw-photos` }),
            processedPhotosUploaded: ({ shoot }) => storageService.objectExists({ bucket: processedBucket, filepath: `${shoot.id}/processed-photos` })
        };
        //TODO add transition for photographer not assigned to shoot (scheduled) but existing in photographers_shoots relation
        // transition defined as {role, from, to}
        const transitions = [
            // Schedule a shoot
            { roles: ['admin'],
                from: toSchedule,
                to: scheduled
            },
            { roles: ['client'],
                from: toSchedule,
                to: scheduled,
                conditionCallbacks: [
                    conditions.isScheduled
                ]
            },
            // Assign a photographer
            { roles: ['admin'],
                from: scheduled,
                to: photographerAssigned,
                conditionCallbacks: [
                    conditions.hasPhotographer
                ]
            },
            // Confirm the photographer assignment
            { roles: ['photographer'],
                from: photographerAssigned,
                to: confirmed,
                conditionCallbacks: [
                    conditions.isAssignedPhotographer
                    //conditions.isPhotographerIdle  => disabled for now
                ]
            },
            // Confirm the photographer assignment if its a videoshoot skip the upload Raws status
            { roles: ['photographer'],
                from: confirmed,
                to: photosReady,
                conditionCallbacks: [
                    conditions.isAssignedPhotographer
                ]
            },
            // Confirm the photographer assignment
            { roles: ['admin'],
                from: photographerAssigned,
                to: confirmed,
                conditionCallbacks: [
                    conditions.hasPhotographer
                ]
            },
            // Reassigned to confirmed shoot if shoot is rescheduled
            { roles: ['admin'],
                from: confirmed,
                to: photographerAssigned
            },
            { roles: ['client'],
                from: confirmed,
                to: photographerAssigned
            },
            { roles: ['admin'],
                from: toSchedule,
                to: photographerAssigned,
                conditionCallbacks: [
                    conditions.isScheduled,
                    conditions.hasPhotographer
                ]
            },
            { roles: ['client'],
                from: toSchedule,
                to: photographerAssigned,
                conditionCallbacks: [
                    conditions.isScheduled,
                    conditions.hasPhotographer
                ]
            },
            { roles: ['admin'],
                from: photographerAssigned,
                to: photographerAssigned,
                conditionCallbacks: [
                    conditions.isScheduled,
                    conditions.hasPhotographer
                ]
            },
            { roles: ['admin'],
                from: confirmed,
                to: photographerAssigned
            },
            // Confirm photographer matching to shoot
            { roles: ['photographer'],
                from: scheduled,
                to: confirmed,
                conditionCallbacks: [
                    conditions.noPhotographer,
                    conditions.isProposedPhotographer
                ]
            },

            // Upload processed photos
            { roles: ['admin'],
                from: photosUploaded,
                to: photosReady

            },
            // Re-upload processed photos
            { roles: ['admin'],
                from: completed,
                to: photosReady
            },
            { roles: ['admin'],
                from: photosReady,
                to: completed
            },
            { roles: ['admin'],
                from: photosReady,
                to: photosReady
            },
            // Cancel a shoot (from various starting statuses)
            { roles: ['admin'],
                from: toSchedule,
                to: canceled
            },
            { roles: ['admin'],
                from: scheduled,
                to: canceled
            },
            { roles: ['admin'],
                from: photographerAssigned,
                to: canceled
            },
            { roles: ['admin'],
                from: confirmed,
                to: canceled
            },
            { roles: ['admin'],
                from: confirmed,
                to: photosUploaded,
                conditionCallbacks: [
                    conditions.rawPhotosUploaded
                ]
            },
            { roles: ['admin'],
                from: confirmed,
                to: photosUploaded,
                conditionCallbacks: [
                    conditions.rawPhotosUploaded
                ]
            },
            { roles: ['admin'],
                from: confirmed,
                to: photosReady
            },
            // Complete a shoot
            { roles: ['client'],
                from: photosReady,
                to: completed
            },
            // Upload raw photos
            { roles: ['photographer'],
                from: confirmed,
                to: photosUploaded,
                conditionCallbacks: [
                    conditions.rawPhotosUploaded,
                    conditions.isAssignedPhotographer
                ]
            }
        ];

        this.transitionsDict = Object.fromEntries(transitions.flatMap(({ roles, from, to, conditionCallbacks }) =>

            roles.map((role) => [
                buildTransitionKey({ role, from, to }),
                { role, from, to, conditionCallbacks }
            ])));
    }

    /**
     * Determine if change of status is legit
     *
     * @param {Shoot} shoot
     *  The shoot for which we want to change status
     *
     * @param {string} role
     *  The role of the user
     *
     * @param {Object} user
     *  The object related to the user
     *
     * @param {string} targetStatus
     *  The target status
     *
     * @return {boolean}
     *  The legitimacy of the transition.
     */
    async isLegitTransition({ shoot, role, user, targetStatus }) {

        const { logger } = this.server;

        const {
            photographer_rest_between_shoots_in_minutes: photographerIdleMinutes
        } = this.server.settings.app.photographers;

        const transition = this.transitionsDict[buildTransitionKey({ role, from: shoot.status, to: targetStatus })];

        if (!transition) {
            return false;
        }

        const { conditionCallbacks } = transition;

        if (!conditionCallbacks) {
            return true;
        }

        const conditions = await Promise.all(conditionCallbacks.map(
            (condition) =>

                condition(
                    {
                        shoot,
                        user,
                        photographerIdleMinutes
                    }))).catch((err) => {

            logger.error({ shoot, role, targetStatus, err }, 'Error while evaluating conditionCallback for a shoot status transition');
            return false;
        });
        return conditions.every((c) => c === true);
    }

    /**
     * Updates shoot status after payment
     *
     * @param {Shoot} shoot
     *  The shoot for which we want to change status
     *
     * @param {Object} user
     *  The object related to the user
     *
     * @param {string} role
     *  The role of the user
     *
     * @return {Object}
     *  The updated shoot.
     */
    async updatePayedShootStatus({ shoot, user, role, forecast = false }) {

        const { Shoot } = this.server.models();
        const { shootService } = this.server.services();
        const { datetime, duration, photographer_id, redeemable_counter } = shoot;
        let scheduled = (datetime || false) && duration && true;
        let status = Shoot.statuses.toSchedule;
        if (photographer_id && scheduled) {
            status = Shoot.statuses.photographerAssigned;
        }
        else if (scheduled) {
            status = Shoot.statuses.scheduled;
        }
        else if (redeemable_counter) {
            status = Shoot.statuses.redeemable;
            scheduled = false;
        }

        if (forecast === true) {
            return { scheduled, status };
        }

        return await shootService.updateShoot({ shoot, user, role, newValues: { status, scheduled } });

    }

    /**
     * Determine if shoot can be redeemed
     *
     * @param {Shoot} redeemable_shoot
     *  The shoot for which we want to redeem
     *
     * @return {boolean}
     *  The legitimacy of the redemption.
     */
    isLegitRedeem({ redeemable_shoot }) {

        const { status } = redeemable_shoot;

        if (status !== Shoot.statuses.redeemable) {
            throw new ValidationError('Shoot cannot be redeemed');
        }

    }
};
