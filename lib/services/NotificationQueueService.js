'use strict';

const Schmervice = require('schmervice');

module.exports = class NotificationQueueService extends Schmervice.Service {

    /**
     * Enqueue a given notification.
     *
     * @param {number} shoot_id
     *  The shoot id related to the notification (if any). Optional.
     *
     * @param {Object} target
     *  Target user of the notification.
     *
     * @param {String} type
     *  Type of notification.
     *
     * @param {Object} metaData
     *  The notification metadata.
     *
     * @param {Array} times
     *  The list of date at which the notification should be sent.
     */
    async enqueueNotification({ shoot_id, target, type, metaData, times }) {

        const { NotificationQueue } = this.server.models();

        if (!times) {
            return;
        }

        const logContext = {
            context: 'NotificationQueueService.enqueueNotification',
            type,
            metaData,
            shoot_id,
            target,
            dispatch_time: times
        };

        const { server } = this;
        await NotificationQueue.query().insert({
            shoot_id,
            target,
            type,
            metaData,
            dispatch_time: times
        }).catch((err) => server.logger.warn({ ...logContext, err }, 'Unable to enqueue notification'));
    }

    /**
     * Delete all queued notifications related to a given shoot.
     *
     * @param {number} shoot_id
     *  The shoot id to delete notifications for.
     *
     * **/
    async deleteAllQueuedNotificationsForShoot({ shoot_id }) {

        const { NotificationQueue } = this.server.models();

        await NotificationQueue.query().where('shoot_id', shoot_id).delete();

    }

    /**
     * Get all notifications queued to be sent before a given time.
     *
     * @param {DateTime} time
     *  The time to filter notifications for.
     *
     **/
    async getAllNotificationsBeforeTime({ time }) {

        const { NotificationQueue } = this.server.models();

        return await NotificationQueue.query().where('dispatch_time', '<=', time);
    }

    /**
     * Delete all queued notifications by their internal ids.
     *
     * @param {Array} ids
     *  Array with the internal ids of the queued notifications to delete.
     *
     **/
    async deleteQueuedNotificationsById({ ids }) {

        const { NotificationQueue } = this.server.models();
        await NotificationQueue.query().whereIn('id', ids).delete();
    }

    /**
     * Delete a specific queued notification for a specific shoot.
     *
     * @param {Object} shoot
     *  The related shoot.
     * @param {String} type
     *  The notification type.
     *
     **/
    async deleteNotificationByShootIdAndNotificationType({ shoot, type }) {

        const { NotificationQueue } = this.server.models();
        await NotificationQueue.query()
            .where('shoot_id', shoot.id)
            .where('type', type)
            .delete();
    }
};
