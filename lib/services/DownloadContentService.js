'use strict';

const Schmervice = require('schmervice');
const AWS = require('aws-sdk');

/**
 * @typedef {Object} JobRecord
 * @property {number} id - The unique identifier of the job
 * @property {number} shootId - The ID of the associated shoot
 * @property {string} status - Current status of the job
 * @property {string} message - Job message or description
 * @property {string} [downloadUrl] - URL where the processed content can be downloaded
 * @property {Date} createdAt - Job creation timestamp
 * @property {Date} updatedAt - Last update timestamp
 */

/**
 * Service for managing download requests through SQS queue and database.
 * Handles job enqueueing, status checking, and queue management.
 *
 * @class DownloadContentService
 * @extends Schmervice.Service
 */
module.exports = class DownloadContentService extends Schmervice.Service {

    /**
     * Job status enumeration for download request jobs.
     *
     * @readonly
     * @enum {string}
     */
    static get JobStatus() {

        return {
            PENDING: 'pending',
            PROCESSING: 'processing',
            COMPLETED: 'completed',
            FAILED: 'failed'
        };
    }

    /**
     * Initialize the service and set up AWS SQS client.
     * Configures AWS credentials and sets up necessary service connections.
     */
    initialize() {

        const { DownloadRequestJob } = this.server.models();
        this.DownloadRequestJob = DownloadRequestJob;

        this.sqs = new AWS.SQS({
            accessKeyId: this.server.settings.app.amazon.aws_id,
            secretAccessKey: this.server.settings.app.amazon.aws_secret,
            region: this.server.settings.app.amazon.aws_region
        });

        this.queueUrl = this.server.settings.app.amazon.sqs_download_queue_url;
    }

    /**
     * Formats a job database record into a consistent response object.
     *
     * @param {Object} job - Raw job record from database
     * @returns {JobRecord} Formatted job record
     * @private
     */
    _formatJobRecord(job) {

        return {
            id: job.id,
            shootId: job.shoot_id,
            status: job.status,
            message: job.message,
            progress: job.progress || 0,
            downloadUrl: job.downloadUrl || '',
            createdAt: job.created_at,
            updatedAt: job.updated_at
        };
    }

    async _removeExistingJob({ shootId, trx }) {

        const existingJob = await this.DownloadRequestJob.query(trx)
            .where('shoot_id', shootId)
            .orderBy('created_at', 'desc')
            .first();

        if (!existingJob) {
            return;
        }

        await this.DownloadRequestJob.query(trx)
            .delete()
            .where('id', existingJob.id);
    }

    /**
     * Checks if a job already exists for the given shoot ID.
     * Performs status-based validation and queue verification for active jobs.
     *
     * @param {Object} options - Options object
     * @param {number} options.shootId - The ID of the shoot to check
     * @param {Transaction} options.trx - Transaction object for database operations
     * @returns {Promise<JobRecord|null>} The existing job record if found and valid, null otherwise
     * @private
     */
    async _checkExistingJob({ shootId, trx }) {

        const existingJob = await this.DownloadRequestJob.query(trx)
            .where('shoot_id', shootId)
            .orderBy('created_at', 'desc')
            .first();

        if (!existingJob) {
            return null;
        }

        return this._formatJobRecord(existingJob);
    }

    /**
     * Enqueues a new download job and creates a database record.
     * If a job already exists for the shoot, returns the existing job instead.
     *
     * @param {Object} options - Options object
     * @param {number} options.shootId - The ID of the shoot to be downloaded
     * @param {string} [options.message='Download requested'] - Optional message for the download request
     * @returns {Promise<JobRecord>} The job record with queue information
     * @throws {Error} If job creation or queue operation fails
     */
    async enqueueDownloadJob({ shootId, message = 'Download requested' }) {

        const trx = await this.DownloadRequestJob.startTransaction();

        try {
            const existingJob = await this._checkExistingJob({ shootId, trx });

            if (existingJob) {
                await trx.commit();
                return this._formatJobRecord(existingJob);
            }

            const jobRecord = await this.DownloadRequestJob.query(trx).insert({
                shoot_id: shootId,
                message,
                status: DownloadContentService.JobStatus.PENDING
            });

            const messageParams = {
                QueueUrl: this.queueUrl,
                MessageBody: JSON.stringify({
                    shootId,
                    action: 'PROCESS_DOWNLOAD',
                    timestamp: new Date().toISOString()
                }),
                MessageAttributes: {
                    shootId: {
                        DataType: 'String',
                        StringValue: shootId.toString()
                    }
                }
            };

            await this.sqs.sendMessage(messageParams).promise();
            await trx.commit();

            return this._formatJobRecord(jobRecord);
        }
        catch (error) {
            await trx.rollback();
            throw new Error(`Failed to enqueue download job: ${error.message}`);
        }
    }

    /**
     * Retrieves the current status of a download job.
     * Returns the most recent job record for the given shoot ID.
     * For active jobs, verifies if they still exist in the queue.
     *
     * @param {Object} options - Options object
     * @param {number} options.shootId - The ID of the shoot to check
     * @returns {Promise<JobRecord>} The current status information of the job
     * @throws {Error} If no job is found or status check fails
     */
    async getJobStatus({ shootId }) {

        const trx = await this.DownloadRequestJob.startTransaction();

        try {
            const jobRecord = await this.DownloadRequestJob.query(trx)
                .where('shoot_id', shootId)
                .orderBy('created_at', 'desc')
                .first()
                .withGraphFetched('shoot');

            if (!jobRecord) {
                await trx.commit();
                throw new Error(`No download job found for shoot ID: ${shootId}`);
            }

            await trx.commit();
            jobRecord.message = this._parseJobStatus(jobRecord.status);

            if (jobRecord.status === DownloadContentService.JobStatus.COMPLETED) {
                const { storageService } = this.server.services();
                const bucket = this.server.settings.app.amazon.processed_photos_bucket_name;

                jobRecord.downloadUrl = await storageService.getFileWithSignedKey({
                    bucket,
                    key: `${jobRecord.downloadUrl}`,
                    name: `${jobRecord.shoot.id}-processed-content.zip`,
                    expires: 172800
                });
                return this._formatJobRecord(jobRecord);
            }

            return this._formatJobRecord(jobRecord);
        }
        catch (error) {
            await trx.rollback();
            return this._formatJobRecord({
                id: 0,
                shoot_id: shootId,
                status: DownloadContentService.JobStatus.FAILED,
                message: 'Your request has some errors, please try again!',
                progress: 0,
                downloadUrl: '',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            });
        }
    }

    _parseJobStatus(status) {

        if (!status) {
            return '';
        }

        switch (status) {
            case DownloadContentService.JobStatus.PENDING:
                return 'We are going to start the download process soon!';
            case DownloadContentService.JobStatus.PROCESSING:
                return 'Processing ...';
            case DownloadContentService.JobStatus.COMPLETED:
                return 'Download is ready!';
            case DownloadContentService.JobStatus.FAILED:
                return 'Download failed, please try again!';
            default:
                return '';
        }
    }
};
