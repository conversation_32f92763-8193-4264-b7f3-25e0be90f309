'use strict';

const Schmervice = require('schmervice');

const types = {
    adminShootConfirmed: 'adminShootConfirmed',
    clientContentReady: 'clientContentReady',
    adminContentReady: 'adminContentReady',
    clientShootConfirmed: 'clientShootConfirmed',
    phShootConfirmed: 'phShootConfirmed',
    adminRawsUploaded: 'adminRawsUploaded',
    clientRawsUploaded: 'clientRawsUploaded',
    phRawsUploaded: 'phRawsUploaded',
    shootCreated: 'shootCreated',
    reviewShoot: 'reviewShoot',
    adminShootCompleted: 'adminShootCompleted',
    adminShootUnassigned: 'adminShootUnassigned',
    phShootUnassigned: 'phShootUnassigned',
    phShootAssigned: 'phShootAssigned',
    adminShootAssigned: 'adminShootAssigned',
    shootUnassigned: 'shootUnassigned',
    newCustomerRegistered: 'newCustomerRegistered',
    newPhotographerRegistered: 'newPhotographerRegistered',
    accountCreated: 'accountCreated',
    adminImageResubmitted: 'adminImageResubmitted',
    clientImageReuploaded: 'clientImageReuploaded',
    adminRawsSelection: 'adminRawsSelection',
    clientRawsPreviewEnabled: 'clientRawsPreviewEnabled',
    invoice: 'invoice',
    shootEdited: 'shootEdited',
    welcome: 'welcome',
    subscriptionSuccess: 'subscriptionSuccess',
    subscriptionExpiry: 'subscriptionExpiry',
    subscriptionCancel: 'subscriptionCancel',
    customSubscriptionRequest: 'customSubscriptionRequest'
};
module.exports = class NotificationService extends Schmervice.Service {

    static get types() {

        return { ...types };
    }

    /**
     * This function acts as an abstraction layer and takes care of
     * carrying out checks and sending notifications in the appropriate way
     *
     * @param {String} type
     *  The type of the template
     *
     * @param {Object} target
     *  The object containing the user target for the notification
     *
     * @param {Object} metaData
     *  The object containing the metadata to complete the template
     *
     */
    async sendNotification({ type, target = {}, metaData = {} }) {

        const { mailService, notificationSettingsService } = this.server.services();

        let role;
        if (target.client_id) {
            role = 'client';
        }
        else if (target.photographer_id) {
            role = 'photographer';
        }
        else {
            role = 'admin';
        }

        const toSend = await notificationSettingsService.checkIfEmailIsToSend({ role, email_type: type });

        //I did  this because if is not exist any setting associates i want to sent it(for now)
        if (toSend === false) {
            return true;
        }

        let emailTo;
        if (!target.photographer_id && !target.client_id && !target.email) {
            emailTo = (type === 'newCustomerRegistered' || type === types.adminContentReady) ?
                this.server.settings.app.amazon.admin_sales_email_receiver :
                this.server.settings.app.amazon.admin_operation_email_receiver;
        }
        else {
            emailTo = target.email;
        }

        const destinations = [{ Destination: { ToAddresses: [emailTo] } }];

        const { server } = this;

        const logContext = {
            context: 'NotificationService.sendNotification',
            metaData,
            emailTo
        };

        return await mailService.sendMultipleTemplatedEmails({ template: types[type], destinations, defaultData: metaData }).then(() => true)
            .catch((err) => {

                server.logger.warn({ ...logContext, err }, 'Unable to send notification email');
                return false;
            });

    }
};
