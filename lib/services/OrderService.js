'use strict';

const Schmervice = require('schmervice');
const { UnauthorizedError, NotFoundError } = require('../helpers/errors');

module.exports = class OrderService extends Schmervice.Service {

    /**
     * Get order detail (from the db) given id
     *
     * @param {number} id
     *  The id of the order.
     *
     * @param {Object} user
     *  The object related to the user
     *
     * @param {string} role
     *  The role of the user
     *
     * @return {Object} order
     *  The order details.
     */
    async getOrder({ id, user, role }) {

        const { Order } = this.server.models();
        const query = Order.query().findById(id);
        if (role === 'photographer') {
            throw new UnauthorizedError('Action can not be performed');
        }
        else if (role === 'client' && !user.parent_client_id) {
            query.where('client_id', user.client_id );
        }
        else if (role === 'client' && user.parent_client_id) {
            query.where('client_id', user.parent_client_id );
        }

        const order = await query;
        if (!order) {
            throw new NotFoundError('Unable to find the order');
        }

        return { order };
    }

    /**
     * Patch order (in the db) given id
     *
     * @param {number} id
     *  The id of the order.
     *
     * @param {Object} user
     *  The object related to the user
     *
     * @param {string} role
     *  The role of the user
     *
     * @param {Object} newValues
     *  The data to be updated
     *
     * @return {Object} updatedOrder
     *  The updated order details.
     */
    async updateOrder({ id, user, role, newValues }) {

        const { Order } = this.server.models();
        await this.getOrder({ id, user, role });
        const order = await Order.query().patchAndFetchById(id, newValues);
        return { updatedOrder: order };

    }

    /**
     * Get Client id by order id
     *
     * @param {order_id} id
     *  The id of the order.
     *
     * @return client_id client_id
     *  The id of the client associated to the order
     */
    async getClientByOrderId({ order_id }) {

        const { Order } = this.server.models();
        const order = await Order.query().findById(order_id);
        return order.client_id;
    }
};
