'use strict';

const Schmervice = require('schmervice');
const { NotFoundError, ValidationError, rethrow } = require('../helpers/errors');

module.exports = class PackageService extends Schmervice.Service {

    /**
     * Get packages (from the db) given filters.
     *
     * @param {array} services
     *  The array of services id
     *
     * @param {string} role
     *  The role of the user.
     *
     * @return {Object} packages
     *  The db packages data.
     */
    async getPackages({ services, role }) {

        const { Package } = this.server.models();
        const query = Package.query();

        if (role !== 'admin') {
            query.whereNot('status', 'hidden');
        }

        if (services) {
            query.whereIn('service_id', services);
        }

        const packages = await query;
        return { packages };
    }

    /**
     * Get formatted packages (custom or fixed) given raw packages and role.
     *
     * @param {array} packages
     *  The array of raw packages
     *
     * @return {Object} formattedPackages
     *  The formatted packages data.
     *
     * @return {Object} totalFixedPrice
     *  The total fixed price.
     *
     * @return {Object} totalFixedRevenue
     *  The total fixed revenue.
     */
    async getFormattedPackages({ packages }) {

        const { Package } = this.server.models();
        const { serviceService } = this.server.services();
        const formattedPackages = [];
        let totalFixedPrice = 0.0;
        let totalFixedRevenue = 0.0;
        const packageIds = packages.map(({ package_id }) => package_id).filter((el) => el);

        const templates = (packageIds.length > 0) ? await Package.query().whereIn('id', packageIds) : [];
        if (packageIds.length !== templates.length) {
            throw new ValidationError('Package not found');
        }

        const templatesById = Object.fromEntries(templates.map((pack) => [pack.id, pack]));

        for (const { package_id, service_id, picture_number, video_number, video_duration } of packages) {
            if (package_id) {
                const template = templatesById[package_id];
                const formattedPack = {
                    service_id: template.service_id,
                    picture_number: template.picture_number,
                    package: { ...template }
                };
                totalFixedPrice += parseFloat(template.price);
                totalFixedRevenue += parseFloat(template.photographer_revenue);
                formattedPackages.push(formattedPack);
            }
            else if (service_id) {
                await serviceService.getService({ id: service_id })
                    .catch(rethrow(ValidationError, 'Package can not be formatted due to non-existent service'));

                const formattedPack = {
                    service_id,
                    picture_number,
                    package: { service_id, picture_number, name: 'custom', video_number, video_duration }
                };
                formattedPackages.push(formattedPack);
            }
        }

        return { formattedPackages, totalFixedPrice, totalFixedRevenue };
    }

    /**
     * Create a new package (in the db) given its data
     *
     * @param {Object} name
     *  The name of the package
     *
     * @param {number} service_id
     *  The service related to the package
     *
     * @param {number} price
     *  The price of the package
     *
     * @param {number} photographer_revenue
     *  The revenue for the photographer
     *
     * @param {Object} duration
     *  The name of the package
     *
     * @param {number} picture_number
     *  The number of pictures of the package
     *
     * @param {Object} description
     *  The description of the package
     *
     * @param {string} is_plus
     *  The type of the package [normal, plus]
     *
     * @param {string} status
     *  The status of the package
     *
     * @param {Object} savedPackage
     *  The created package.
     */
    async createPackage({ name, service_id, price, photographer_revenue,
        duration, picture_number, description, is_plus, status, stripe_product_id, price_tag, package_type
    }) {

        const { Package } = this.server.models();

        const savedPackage = await Package.query().insertAndFetch({
            name,
            service_id,
            price,
            photographer_revenue,
            duration,
            picture_number,
            description,
            is_plus,
            status,
            package_type,
            stripe_product_id,
            price_tag
        });

        return { savedPackage };

    }

    /**
     * Get a package (from the db) given its id.
     *
     * @param {string} id
     *  The id of the package.
     *
     * @param {string} role
     *  The role of the user.
     *
     * @return {Object} The db package data.
     */

    // Redeploying
    async getPackage({ id, role }) {

        const { Package } = this.server.models();

        const query = Package.query().findById(id);

        if (role !== 'admin') {
            query.whereNot('status', 'hidden');
        }

        const pack = await query;

        if (!pack) {
            throw new NotFoundError('Package not found');
        }

        return { pack };
    }

    /**
     * Delete a package (from the db) given its id.
     *
     * @param {string} id
     *  The id of the package.
     *
     * @return {Object} The db deleted package data.
     */
    async deletePackage({ id }) {

        const { Package } = this.server.models();
        const { storageService, stripeService } = this.server.services();
        const {
            amazon: { packages_pictures_bucket_name: bucket }
        } = this.server.settings.app;
        const { server } = this;

        const logContext = {
            context: 'PackageService.deleteService'
        };
            // Fetch package details including Stripe product ID
        const pack = await Package.query().findById(id);

        if (!pack) {
            throw new NotFoundError('Package not found');
        }

        // If package type is 'b2c', handle associated Stripe product and prices
        if (pack.package_type === 'b2c' && pack.stripe_product_id) {
            // Deactivate all prices associated with the product
            const prices = await stripeService.listPrices({ productId: pack.stripe_product_id });
            for (const price of prices.data) {
                await stripeService.deletePrice({ priceId: price.id });
            }

            // Delete Stripe product
            await stripeService.deleteProduct({ productId: pack.stripe_product_id });
        }

        // Delete package from database
        await Package.query().deleteById(id);

        //delete image from S3
        if (pack.num_pictures > 0) {
            const buildFilepath = (i) => `package/${id}/${i + 1}`;
            const picturesToDelete = [];
            for (let i = 0; i < pack.num_pictures; ++i) {
                picturesToDelete.push(buildFilepath(i));
            }

            await storageService.deleteObjects({
                bucket,
                filepaths: picturesToDelete
            }).catch((err) => server.logger.warn({ ...logContext, picturesToDelete, err }, 'Unable to delete extra pictures'));
        }

        return { deletedPackage: pack };
    }

    /**
     * Patch package (in the db) given id and data
     *
     * @param {number} id
     *  The id of the package.
     *
     * @param {string} role
     *  The role of the user.
     *
     * @param {Object} newValues
     *  The data to be updated
     *
     * @return {Object} package
     *  The updated package data.
     */
    async updatePackage({ id, role, newValues }) {

        const {  stripeService } = this.server.services();
        const { Package } = this.server.models();

        const existingPackage = await this.getPackage({ id, role });

        // Update package in database
        const updatedPackage = await Package.query().patchAndFetchById(id, newValues);

        if (existingPackage.package_type !== updatedPackage.package_type && updatedPackage.package_type === 'b2c'
            && !updatedPackage.stripe_product_id) {
            // Create a new Stripe product
            const { product, price: productPrice } = await stripeService.createProduct({
                name: updatedPackage.name,
                description: updatedPackage.description,
                amount: parseInt(newValues.price) * 100
            });

            // Store the Stripe product ID and price ID in your database
            await Package.query().patchAndFetchById(id, {
                stripe_product_id: product.id,
                price_tag: productPrice.id
            });

            return { updatedPackage };
        }

        // Check if package type is 'b2c' and Stripe product ID exists
        if (updatedPackage.package_type === 'b2c' && updatedPackage.stripe_product_id) {
            // Update Stripe product if details have changed
            if (newValues.name || newValues.description) {
                await stripeService.updateProduct({
                    productId: updatedPackage.stripe_product_id,
                    updates: {
                        name: newValues.name || existingPackage.name,
                        description: newValues.description || existingPackage.description
                    }
                });
            }

            // Update Stripe price if price has changed
            if (newValues.price && newValues.price !== existingPackage.price) {
                const newPrice = await stripeService.createPrice({
                    product: updatedPackage.stripe_product_id,
                    unit_amount: `${newValues.price}00`
                });

                // Deactivate old price
                if (existingPackage.price_tag) {
                    await stripeService.deletePrice({ priceId: existingPackage.price_tag });
                }

                // store new price ID in your database
                await Package.query().patchAndFetchById(id, { price_tag: newPrice.id });
            }
        }

        return { updatedPackage };
    }

};
