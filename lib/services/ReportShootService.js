'use strict';

const Schmervice = require('schmervice');
const { NotFoundError } = require('../helpers/errors');
const Shoot = require('../models/Shoot');

module.exports = class ReportShootService extends Schmervice.Service {

    /**
     * Create a new report (in the db) given its data
     *
     * @param {id} id
     *  Id of the related report
     *
     * @param {totStat} totStat
     *
     * @returns reportShoots with all created report
     */
    async createReportShoot( { id, totStat } ) {

        const { ReportShoot } = this.server.models();

        //retrieve statuses for fixed mapping with FE, added calculated key
        const { statuses } = Shoot;
        statuses.shoots = 'shoots';
        statuses.pictures = 'pictures';

        const reportShoots = await ReportShoot.query().insertAndFetch(Object.entries(totStat).map(([key, value]) => ({
            report_id: id,
            key: `tot_${statuses[key]}`,
            value
        })));

        return reportShoots;
    }

    /**
     * Returns shoot reports (from db) given filters
     *
     * @param {date} time
     *   The interval of time to be queried
     *
     * @param {bool} deltaCalculus
     *   Checks if data from previous day must be selected to do delta calculus
     *
     * @returns {Array} dailyReports
     *   The db formatted reports shoots data.
     */
    async getStatForTimeRange( { time, deltaCalculus = true } ) {

        const { Report, ReportShoot } = this.server.models();

        //If required, the day before from is taken into consideration to enable calculus for delta
        if (deltaCalculus) {
            time.from.setDate(time.from.getDate() - 1);
        }

        const { from, to } = time;

        const reports = await Report.query()
            .whereBetween('date', [from.toISOString().slice(0, 10), to.toISOString().slice(0, 10)]);

        if (reports.length === 0 ) {
            throw new NotFoundError('Unable to find the requested interval of time');
        }

        const mappedReports = Object.fromEntries(reports.map(({ id, date }) => ([id, date])));

        const dailyRawReports = await ReportShoot.query()
            .whereIn('report_id', Object.keys(mappedReports))
            .orderBy('report_id', 'ASC')
            .leftJoinRelated('report')
            .select('key as key', 'value as value', 'report_id as report');

        let dailyReports = dailyRawReports
            .reduce((accumulator, { report, key, value }) => ({

                ...accumulator,
                [report]: {
                    ...(accumulator[report] || {}),
                    ...{ [key.replace(/^tot_/, '')]:
                        {
                            total: value,
                            delta: value - (accumulator[report - 1])?.[key.replace(/^tot_/, '')]?.total || 0
                        }
                    }
                } }),
            {});
        dailyReports = Object.entries(dailyReports).map(([id, value]) => ({ [mappedReports[id].toISOString().slice(0, 10)]: value }));

        if (deltaCalculus) {
            dailyReports = dailyReports.filter(( value ) => (new Date(Object.keys(value)[0]) > new Date(time.from)));
        }

        return dailyReports;
    }
};
