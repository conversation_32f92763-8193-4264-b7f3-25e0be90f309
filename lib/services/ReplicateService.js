'use strict';

const Replicate = require('replicate');

// Predefined camera movement prompts for different content types
const CAMERA_MOVEMENT_PROMPTS = [
    {
        'content_type': 'ecommerce',
        'label': 'Zoom Focus',
        'prompt': 'A single item is placed in the frame [Zoom in], then [Hold for 1 second] before [Zoom out slightly].'
    },
    {
        'content_type': 'ecommerce',
        'label': 'Side Glide',
        'prompt': 'The product appears centered [Truck left, Pan right], maintaining soft motion throughout.'
    },
    {
        'content_type': 'ecommerce',
        'label': 'Depth Motion',
        'prompt': 'A clean product scene [Push in, Pull out], creating a sense of dimensional shift.'
    },
    {
        'content_type': 'ecommerce',
        'label': 'Rotation Drift',
        'prompt': 'A minimal layout [Pan left, Truck right], offering a soft orbit around the subject.'
    },
    {
        'content_type': 'ecommerce',
        'label': 'Crisp Entry',
        'prompt': 'A static product layout [Static shot], slowly replaced by a [Zoom in, Pan left] movement.'
    },
    {
        'content_type': 'interiors',
        'label': 'Walkthrough Motion',
        'prompt': 'A room is shown from the entry point [Push in, Truck right], simulating movement into the space.'
    },
    {
        'content_type': 'interiors',
        'label': 'Wide Scan',
        'prompt': 'The camera begins still [Static shot], then moves with a slow [Pan right, Tilt up].'
    },
    {
        'content_type': 'interiors',
        'label': 'Elevated Glide',
        'prompt': 'A spacious interior [Pedestal up, Truck left], revealing vertical and lateral depth.'
    },
    {
        'content_type': 'interiors',
        'label': 'Cinematic Entry',
        'prompt': 'From a fixed point, the view begins to [Push in], then transitions to a [Pan left].'
    },
    {
        'content_type': 'interiors',
        'label': 'Open Reveal',
        'prompt': 'A room interior [Truck right, Zoom out], creating a slow reveal from a side angle.'
    },
    {
        'content_type': 'fnb',
        'label': 'Tabletop Drift',
        'prompt': 'A food or drink setup is shown [Truck left, Zoom in], creating a slow cinematic drift.'
    },
    {
        'content_type': 'fnb',
        'label': 'Minimal Reveal',
        'prompt': 'A centered item [Zoom in, Tilt down], holding briefly for emphasis.'
    },
    {
        'content_type': 'fnb',
        'label': 'Diagonal Motion',
        'prompt': 'A well-lit scene [Truck right, Tilt up], creating an angled reveal of the composition.'
    },
    {
        'content_type': 'fnb',
        'label': 'Overhead Shift',
        'prompt': 'A flat lay or tabletop view [Pedestal down], followed by a slow [Pan right].'
    },
    {
        'content_type': 'fnb',
        'label': 'Ambient Focus',
        'prompt': 'A dish or drink is present [Static shot], slowly shifting with a [Zoom in, Pan left].'
    },
    {
        'content_type': 'headshots',
        'label': 'Steady Close',
        'prompt': 'A person is framed naturally [Push in], then held briefly before a subtle [Zoom out].'
    },
    {
        'content_type': 'headshots',
        'label': 'Soft Orbit',
        'prompt': 'A neutral portrait [Truck left, Pan right], orbiting subtly around the subject.'
    },
    {
        'content_type': 'headshots',
        'label': 'Over-the-Shoulder Drift',
        'prompt': 'A figure is shown in focus [Truck right], shifting slightly with a [Tilt up].'
    },
    {
        'content_type': 'headshots',
        'label': 'Background Reveal',
        'prompt': 'A subject is shown front-facing [Zoom in, Pan left], gradually introducing the background.'
    },
    {
        'content_type': 'headshots',
        'label': 'Framed Focus',
        'prompt': 'A calm scene [Static shot], then transitioned into a soft [Pull out, Tilt down].'
    }
];

/**
 * Service class for interacting with the Replicate API.
 * Provides methods for creating and managing predictions, image manipulation, and background removal.
 */
module.exports = class ReplicateService {

    /**
     * Creates a new instance of ReplicateService.
     * @param {Object} server - The server instance containing configuration settings.
     * @param {Object} server.settings.app.replicate - Replicate configuration settings.
     * @param {string} server.settings.app.replicate.api_key - Replicate API key.
     */
    constructor(server) {

        this.server = server;
        this.replicate = new Replicate({
            auth: this.server.settings.app.replicate.api_key
        });
    }

    /**
     * Creates a new prediction using the Replicate API.
     * @param {Object} params - The prediction parameters.
     * @param {string} params.version - The model version to use.
     * @param {Object} params.input - The input data for the prediction.
     * @returns {Promise<Object>} The created prediction object.
     * @throws {Error} Throws an error with code 'REPLICATE_API_ERROR' if the API call fails.
     */
    async createPrediction({ version, input }) {

        try {
            const prediction = await this.replicate.predictions.create({
                version,
                input
            });
            return prediction;
        }
        catch (error) {
            error.code = 'REPLICATE_API_ERROR';
            throw error;
        }
    }

    /**
     * Retrieves a prediction by its ID.
     * @param {string} predictionId - The ID of the prediction to retrieve.
     * @returns {Promise<Object>} The prediction object.
     * @throws {Error} Throws an error with code 'REPLICATE_API_ERROR' if the API call fails.
     */
    async getPrediction(predictionId) {

        try {
            return await this.replicate.predictions.get(predictionId);
        }
        catch (error) {
            error.code = 'REPLICATE_API_ERROR';
            throw error;
        }
    }

    /**
     * Cancels a running prediction.
     * @param {string} predictionId - The ID of the prediction to cancel.
     * @returns {Promise<Object>} The cancelled prediction object.
     * @throws {Error} Throws an error with code 'REPLICATE_API_ERROR' if the API call fails.
     */
    async cancelPrediction(predictionId) {

        try {
            return await this.replicate.predictions.cancel(predictionId);
        }
        catch (error) {
            error.code = 'REPLICATE_API_ERROR';
            throw error;
        }
    }

    /**
     * Lists all predictions.
     * @returns {Promise<Array<Object>>} Array of prediction objects.
     * @throws {Error} Throws an error with code 'REPLICATE_API_ERROR' if the API call fails.
     */
    async listPredictions() {

        try {
            return await this.replicate.predictions.list();
        }
        catch (error) {
            error.code = 'REPLICATE_API_ERROR';
            throw error;
        }
    }

    /**
     * Performs generative fill on an image using the flux-fill-pro model.
     * @param {Object} params - The parameters for generative fill.
     * @param {string} params.image - The input image URL or base64 string.
     * @param {string} params.mask - The mask image URL or base64 string.
     * @param {string} params.prompt - The text prompt for generation.
     * @returns {Promise<string>} The prediction ID.
     * @throws {Error} Throws an error with code 'REPLICATE_API_ERROR' if the API call fails.
     */
    async generateObjects({ image, mask, prompt }) {

        const prediction = await this.replicate.predictions.create({
            model: 'black-forest-labs/flux-fill-pro',
            input: {
                mask,
                image,
                prompt
            }
        });

        return prediction.id;
    }

    async outpaint({ image, outpaint, prompt  }) {

        const prediction = await this.replicate.predictions.create({
            model: 'black-forest-labs/flux-fill-pro',
            input: {
                image,
                outpaint,
                prompt
            }
        });

        return prediction.id;
    }

    /**
     * Removes an object from an image using a mask.
     * Handles image resizing and optimization if the image size exceeds 1.5MB.
     * @param {Object} params - The parameters for object removal.
     * @param {Buffer|Blob} params.image - Image blob from multipart form data
     * @param {Buffer|Blob} params.mask - Mask blob from multipart form data
     * @returns {Promise<string>} The prediction ID.
     * @throws {Error} Throws an error with code 'REPLICATE_API_ERROR' if the API call fails.
     */
    async removeObject({ image, mask }) {

        const version = '0e3a841c913f597c1e4c321560aa69e2bc1f15c65f8c366caafc379240efd8ba';

        const prediction = await this.replicate.predictions.create({
            version,
            input: { image, mask }
        });

        return prediction.id;
    }
    /**
     * Removes the background from an image.
     * @param {Object} params - The parameters for background removal.
     * @param {string} params.image - The input image URL or base64 string.
     * @returns {Promise<string>} The prediction ID.
     * @throws {Error} Throws an error with code 'REPLICATE_API_ERROR' if the API call fails.
     */
    async removeBackground({ image }) {

        const version = 'fb8af171cfa1616ddcf1242c093f9c46bcada5ad4cf6f2fbe8b81b330ec5c003';

        const prediction = await this.replicate.predictions.create({
            version,
            input: { image }
        });

        return prediction.id;

    }

    /**
     * Generates a video using the Minimax Video-01-Director model with predefined camera movements.
     * This method uses predefined prompts based on content type and label for image-to-video generation.
     *
     * @param {Object} params - The parameters for video generation.
     * @param {string} params.content_type - The type of content (ecommerce, interiors, fnb, headshots).
     * @param {string} params.label - The specific camera movement label within the content type.
     * @param {string} params.first_frame_image - Image URL or base64 string for image-to-video generation.
     * @returns {Promise<string>} The prediction ID.
     * @throws {Error} Throws an error with code 'REPLICATE_API_ERROR' if the API call fails.
     * @throws {Error} Throws an error with code 'INVALID_PARAMETERS' if content_type or label is invalid.
     */
    async generateVideoWithDirector({ content_type, label, first_frame_image }) {

        try {
            // Find the matching prompt based on content_type and label
            const promptConfig = CAMERA_MOVEMENT_PROMPTS.find(
                (config) => config.content_type === content_type && config.label === label
            );

            if (!promptConfig) {
                const error = new Error(`Invalid content_type or label: ${content_type}, ${label}`);
                error.code = 'INVALID_PARAMETERS';
                throw error;
            }

            const input = {
                prompt: promptConfig.prompt,
                first_frame_image
            };

            const prediction = await this.replicate.predictions.create({
                model: 'minimax/video-01-director',
                input
            });

            return prediction.id;
        }
        catch (error) {
            if (!error.code) {
                error.code = 'REPLICATE_API_ERROR';
            }

            throw error;
        }
    }
};
