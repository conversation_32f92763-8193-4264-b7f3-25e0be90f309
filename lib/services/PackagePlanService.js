'use strict';

const Schmervice = require('schmervice');

module.exports = class PackagePlanService extends Schmervice.Service {
    async getPackagePlans({ user }) {

        const { PackagePlan } = this.server.models();
        const query = PackagePlan.query();

        // Check if the package plan already exists
        let packagePlans = await query
            .where('status', 'active')
            .orderBy('weight', 'asc');

        if (!packagePlans || packagePlans.length === 0) {
            return [];
        }

        const { subscriptionService } = this.server.services();

        // Get the user's subscription plan
        const userSubscription = await subscriptionService.getCurrentActiveSubscription({ user });
        const userSubscriptionPlan = userSubscription ? await subscriptionService.getPackagePlanById({ id: userSubscription.package_id }) : null;

        // If the user has an active subscription, find the corresponding package plan
        packagePlans = packagePlans.map((plan) => {

            plan.is_subscribed = !!(userSubscriptionPlan && plan.id === userSubscriptionPlan.id);
            plan.is_enabled = !!(userSubscriptionPlan && plan.order > userSubscriptionPlan.order);
            return plan;
        });

        return packagePlans;
    }

    async requestCustomPackage({ user }) {

        const client = await user.client;

        if (!client) {
            throw new Error('Client not found');
        }

        const { notificationService } = this.server.services();
        await notificationService.sendNotification({
            type: 'customSubscriptionRequest',
            target: {
                email: this.server.settings.app.amazon.admin_techs_email_receiver
            },
            metaData: {
                user: {
                    first_name: user.name,
                    email: user.email,
                    phone: user.phone
                },
                time: new Date().toISOString(),
                client: {
                    company_name: client.company_name,
                    contact_name: client.contact_name,
                    contact_email: client.contact_email,
                    contact_phone: client.contact_phone,
                    main_location: client.main_location,
                    country: client.country
                },
                host: process.env.CURRENT_HOST
            }
        });
    }
};
