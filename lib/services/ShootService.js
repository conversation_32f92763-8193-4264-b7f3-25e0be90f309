/* eslint-disable @hapi/scope-start */
'use strict';

const Schmervice = require('schmervice');
const { NotFoundError, rethrow, InternalError } = require('../helpers/errors');
const { raw } = require('objection');
const { UnauthorizedError } = require('../helpers/errors');
const { types } = require('../services/NotificationService.js');
const {
    stringifyLocation,
    formattedDateTime,
    formattedDateForEmail
} = require('../helpers/utils');

module.exports = class ShootService extends Schmervice.Service {
    /**
   * Get shoots (from the db) given filters.
   *
   * @param {number} limit
   *  The number of shoots.
   *
   * @param {number} offset
   *  The offset clause.
   *
   * @param {string} starts_at
   *  The starting date.
   *
   * @param {string} ends_at
   *  The ending date.
   *
   * @param {array} services
   *  The array of services id
   *
   * @param {array} statuses
   *  The array of statuses.
   *
   * @param {object} user
   *  The object related to the user
   *
   * @param {array} cities
   *  The object related to the location
   *
   * @param {string} role
   *  The role of the user
   *
   * @return {Object} shoots
   *  The db shoots data.
   *
   * @return {Object} total
   *  The amount of records in db not taking into account pagination.
   */

    async getShoots({ limit, offset, role, user, statuses, starts_at, ends_at, cities, services, searchQuery }) {
        const { Shoot } = this.server.models();

        const applyFilters = (query) => {

            if (statuses) {
                query.whereIn('shoots.status', statuses);
                if (!statuses.includes(Shoot.statuses.completed) && !statuses.includes(Shoot.statuses.canceled)) {
                    query.whereNotIn('shoots.status', [Shoot.statuses.completed, Shoot.statuses.canceled]);
                }
            }

            if (role === 'photographer') {
                query.where('shoots.photographer_id', user.photographer_id);
            }
            else if (role === 'client') {
                // Sub client: Fetch orders linked to the parent client
                if (user.parent_client_id) {
                    query.where('orders.client_id', user.parent_client_id);
                }
                // Main client: Fetch own shoots and sub accounts' shoots
                else {
                    query.where('orders.client_id', user.client_id);
                }
            }

            if (starts_at) {
                query.where('shoots.datetime', '>=', starts_at);
            }

            if (ends_at) {
                query.where('shoots.datetime', '<=', ends_at);
            }

            // Filter by cities using safe bindings
            if (cities && cities.length) {
                const placeholders = cities.map(() => '?').join(', ');
                const queryText = `address->>'city' = ANY(ARRAY[${placeholders}]::text[])`;
                query.whereRaw(queryText, cities);
            }

            // Service filtering
            if (services && services.length) {
                query.whereIn('shoots_services.service_id', services);
            }

            if (searchQuery) {
                if (/^\d/.test(searchQuery)) { // If the search query starts with a digit
                    query.where(
                        Shoot.raw('??::text', ['shoots.id']),
                        'like',
                        `${searchQuery}%`
                    );
                }
                else if (/^[a-zA-Z]/.test(searchQuery)) { // If the search query starts with a letter
                    query.where((builder) => {
                        if (role !== 'client') {
                            builder.where('clients.company_name', 'ILike', `%${searchQuery}%`);
                        }

                        builder.orWhere('users.name', 'ILike', `%${searchQuery}%`)
                            .orWhere('shoots.outlet_name', 'ILike', `%${searchQuery}%`)
                            .orWhere('shoots.name', 'ILike', `%${searchQuery}%`)
                            .orWhere('shoots.notes', 'ILike', `%${searchQuery}%`);
                    });
                }
            }

        };

        const query = Shoot.query()
            .select(
                'shoots.*',
                'users.name as photographer_name',
                'clients.company_name as customer_name',
                'shoots_services.service_id as service_id',
                Shoot.raw('COUNT(*) OVER() AS total')
                // Shoot.raw('(SELECT json_agg(raws.*) FROM raws WHERE raws.shoot_id = shoots.id) AS raws'),
                // Shoot.raw('(SELECT json_agg(images.*) FROM images WHERE images.shoot_id = shoots.id) AS images')
            )
            .leftJoin('users', 'users.photographer_id', '=', 'shoots.photographer_id')
            .join('orders', 'orders.id', '=', 'shoots.order_id')
            .join('clients', 'clients.id', '=', 'orders.client_id')
            .join('shoots_services', 'shoots_services.shoot_id', '=', 'shoots.id')
            .orderBy('shoots.datetime', 'DESC')
            .limit(limit)
            .offset(offset);

        // Apply basic filters to both data and total queries
        applyFilters(query);

        const shoots = await query;
        const total = shoots.length > 0 ? shoots[0].total : 0;  // Retrieve total from the window function

        const formattedShoots = shoots.map((shoot) => {
            delete shoot.total; // Remove the 'total' property
            return {
                ...shoot,
                services: [{
                    service_id: shoot.service_id,
                    picture_number: shoot.picture_number,
                    package: { name: shoot.name || 'custom' }
                }]
            };
        });

        return {
            shoots: formattedShoots,
            total
        };
    }

    /**
   * Get shoot detail (from the db) given id
   *
   * @param {number} id
   *  The id of the shoot.
   *
   * @param {Object} user
   *  The object related to the user
   *
   * @param {string} role
   *  The role of the user
   *
   * @return {Object} shoot
   *  The shoot details.
   */
    async getShoot({ id, user, role }) {
        const { Shoot, ShootService, PhotographerShoot } =
      this.server.models();
        const query = Shoot.query()
            .findById(id)
            .leftJoinRelated('[photographer.user, order.client]') // Added images relation here
            .select(
                'shoots.*',
                'order:client.company_name as customer_name',
                'photographer:user.name as photographer_name'
            );

        if (role === 'photographer') {
            query
                .where('shoots.photographer_id', user.photographer_id)
                .orWhere((queryBuilder) =>
                    queryBuilder
                        .where('shoots.status', Shoot.statuses.scheduled)
                        .whereNull('shoots.photographer_id')
                        .where('shoots.id', id)
                        .whereExists(
                            PhotographerShoot.query()
                                .where('shoot_id', id)
                                .where('photographer_id', user.photographer_id)
                        )
                );
        }
        else if (role === 'client' && !user.parent_client_id) {
            query.where('order.client_id', user.client_id);
        }
        else if (role === 'client' && user.parent_client_id) {
            //this condition finds redeemable from parent client or redeemed shoot from the current user (consumer_client_id)
            query.where('consumer_client_id', user.client_id).orWhere(
                (queryBuilder) =>
                    queryBuilder
                        .where('shoots.id', id)
                        .where('order.client_id', user.parent_client_id)
                // .whereNotNull('redeemable_total')
            );
        }
        else if (role !== 'admin' && role !== 'editor') {
            throw new InternalError(`Invalid user role (${role})`);
        }

        const shoot = await query;
        if (!shoot) {
            throw new NotFoundError('Unable to find the shoot');
        }

        shoot.services = await ShootService.query().where('shoot_id', shoot.id);
        shoot.services = shoot.services.map(
            ({ service_id, picture_number, package: pack }) => ({
                service_id,
                picture_number,
                package: {
                    name: pack.name || 'custom',
                    ...(pack?.id && { id: pack.id })
                }
            })
        );

        return { shoot };
    }

    /**
   * Patch shoot (in the db) given id
   *
   * @param {Shoot} shoot
   *  The shoot to be updated. We assume that validation
   *  on the fact that the user can update it has already
   *  been done (e.g., by the `getShoot` method).
   *
   * @param {User} user
   *  The User db object (NOTE: if not given, the update will complete
   *  but leave the `last_edit_by` field unchanged).
   *
   * @param {Object} newValues
   *  The data to be updated
   *
   * @return {Object} shoot
   *  The updated shoot details.
   */
    async updateShoot({
        shoot,
        user = {},
        newValues,
        hardUpdate = false,
        packages
    }) {
        const { Shoot, ShootService } = this.server.models();

        await Shoot.query().patchAndFetchById(shoot.id, {
            ...newValues,
            last_edit_by: user.id,
            last_update: Date.now()
        });

        if (hardUpdate) {
            await ShootService.query().where('shoot_id', shoot.id).delete();

            await ShootService.query().insert(
                packages.map((pack) => ({
                    shoot_id: shoot.id,
                    ...pack
                }))
            );
        }

        // NOTE: we fetch the shoot as if we were admins to "skip" the role validation, since it
        //       should have already been performed by the caller (by providing the `shoot` param).
        const { shoot: updatedShoot } = await this.getShoot({
            id: shoot.id,
            role: 'admin'
        });

        return updatedShoot;
    }

    /**
   * Download photos from storage
   *
   * @param {Object} shoot
   *  The shoot related to the download
   *
   * @param {string} downloadType
   *  The type of download
   *
   */
    async downloadPhotos({ shoot, downloadType }) {
        const { storageService } = this.server.services();
        const bucket =
      downloadType === 'raw'
          ? this.server.settings.app.amazon.raw_photos_bucket_name
          : this.server.settings.app.amazon.processed_photos_bucket_name;

        const filepath = `${shoot.id}/${downloadType}-photos`;
        return await storageService
            .getObject({ bucket, filepath })
            .catch(rethrow(NotFoundError, 'Photos not found or not available'));
    }

    /**
   * Dowload brief from storage
   *
   * @param {Object} shoot
   *  The shoot related to brief
   *
   * @param {Object} brief
   *  The stream of the brief.
   */
    async downloadBrief({ shoot }) {
        const { storageService } = this.server.services();
        const bucket = this.server.settings.app.amazon.briefs_bucket_name;

        const filepath = `${shoot.id}/brief`;
        const { Metadata } = await storageService
            .getObjectMetadata({ bucket, filepath })
            .catch(rethrow(NotFoundError, 'Brief not found or not available'));
        const { stream, contentLength } = await storageService
            .getObject({ bucket, filepath })
            .catch(rethrow(NotFoundError, 'Brief not found or not available'));

        return { stream, contentLength, filename: Metadata['x-amz-meta-filename'] };
    }

    /**
   * Upload photos to storage
   *
   * @param {Object} shoot
   *  The shoot related to the upload
   *
   * @param {string} uploadType
   *  The type of upload
   *
   * @param {Object} photos
   *  The stream of photos.
   */
    async uploadPhotos({ shoot, uploadType, photos }) {
        const { storageService } = this.server.services();

        const bucket =
      uploadType === 'raw'
          ? this.server.settings.app.amazon.raw_photos_bucket_name
          : this.server.settings.app.amazon.processed_photos_bucket_name;

        const filepath = `${shoot.id}/${uploadType}-photos`;

        await storageService.uploadObject({ bucket, filepath, object: photos });
    }

    formatS3TagString(tags) {
        return Object.entries(tags)
            .map(([key, value]) => `${key}=${value}`)
            .join('&');
    }

    /**
   * Upload brief to storage
   *
   * @param {Object} shoot
   *  The shoot related to the upload
   *
   * @param {Object} brief
   *  The stream of the brief.
   *
   * @param {string} filename
   *  The original filename of the brief.
   */
    async uploadBrief({ shoot, brief, filename }) {
        const { storageService } = this.server.services();

        const bucket = this.server.settings.app.amazon.briefs_bucket_name;
        const filepath = `${shoot.id}/brief`;

        await storageService.uploadObject({
            bucket,
            filepath,
            object: brief,
            options: { Metadata: { 'x-amz-meta-filename': filename } }
        });
    }

    /**
   * Create a new shoot (in the db) given its data
   *
   * @param {Object} packages
   *  The packages related to the shoot
   *
   * @param {number} photographer_id
   *  The id of the photographer chosen for the shoot
   *
   * @param {number} client_id
   *  The id of the client that requires the shoot
   *
   * @param {Object} location
   *  The object of the location related to the shoot
   *
   * @param {number} price
   *  The price of the shoot
   *
   * @param {number} photographer_revenue
   *  The revenue for the photographer
   *
   * @param {Object} time
   *  The object of the interval of time related to the shoot
   *
   * @param {number} duration
   *  The duration of the shoot
   *
   * @param {string} notes
   *  The notes related to the shoot
   *
   * @param {string} contact_phone
   *  The phone number of the person to be contacted for the shoot
   *
   * @param {string} contact_name
   *  The name of the person to be contacted for the shoot
   *
   * @param {number} redeemable_total
   *  The number of time the shoot can be redeemed
   *
   * @param {Object} savedShoot
   *  The created shoot.
   *
   *  @param {Object} name
   *  The name of the shoot.
   *
   * @return {Object} shoot
   *  The shoot data.
   */
    async createShoot({
        packages,
        photographer_id,
        client_id,
        location,
        price,
        photographer_revenue,
        time,
        duration,
        notes,
        contact_phone,
        contact_name,
        role,
        is_payed,
        user,
        redeemable_total,
        name,
        type,
        content
    }) {
        const { Shoot, Order, ShootService } = this.server.models();
        const { reportUserService } = this.server.services();

        const trx = await Order.startTransaction();
        try {
            const isPayed = role === 'admin' && is_payed;
            let order_price;
            if (redeemable_total) {
                order_price = redeemable_total !== -1 ? redeemable_total * price : 0;
            }
            else {
                order_price = price;
            }

            const savedOrder = await Order.query(trx).insertAndFetch({
                client_id,
                purchase_type: type === 'express' ? 'stripe-card' : 'manual',
                payment_details: {},
                price: order_price,
                billing_address: {
                    city: '',
                    country: '',
                    line1: '',
                    line2: '',
                    postal_code: '',
                    state: ''
                },
                payment_confirmed: true,
                payment_date: new Date(),
                status: 'none'
            });

            let isScheduled = time && time.from ? true : false;
            let startingStatus = Shoot.statuses.toSchedule;
            if (photographer_id && isScheduled) {
                startingStatus = Shoot.statuses.photographerAssigned;
            }
            else if (isScheduled) {
                startingStatus = Shoot.statuses.scheduled;
            }

            if (redeemable_total) {
                isScheduled = false;
                startingStatus = Shoot.statuses.redeemable;
            }

            const status = startingStatus;

            const pkg = packages[0].package;

            const savedShoot = await Shoot.query(trx).insertAndFetch({
                photographer_id: !redeemable_total ? photographer_id : null,
                order_id: savedOrder.id,
                datetime: !redeemable_total ? time?.from || null : null,
                address: location,
                price,
                photographer_revenue,
                notes,
                type,
                content,
                contact_phone,
                contact_name,
                picture_number: pkg.picture_number ? pkg.picture_number : 0,
                video_number: pkg.video_number ? pkg.video_number : 0,
                video_duration: pkg.video_duration ? pkg.video_duration : 0,
                duration,
                scheduled: isPayed ? isScheduled : false,
                status,
                ...(redeemable_total && {
                    redeemable_total,
                    redeemable_counter: redeemable_total !== -1 ? redeemable_total : 0
                }),
                last_edit_by: user.id,
                name,
                consumer_client_id: savedOrder.client_id
            });
            await ShootService.query(trx).insert(
                packages.map((pack) => ({
                    shoot_id: savedShoot.id,
                    ...pack
                }))
            );

            await trx.commit();

            // NOTE: this is to count the number of created shoots for the reports
            reportUserService.createReportUser({
                shoot: savedShoot,
                performer_user_id: user.id,
                target_photographer_id: photographer_id,
                starting_status: Shoot.statuses.notCreated,
                target_status: status
            });

            return { savedShoot };
        }
        catch (err) {
            await trx.rollback();
            throw err;
        }
    }

    /**
   * Redeem a shoot given redeemable and new values
   *
   * @param {Shoot} redeemable_shoot
   *  The shoot to be redeemed.
   *
   * @param {User} user
   *  The User db object
   *
   * @param {Object} newValues
   *  The data to be updated
   *
   * @param {string} role
   * The role of the user
   *
   * @return {Object} savedShoot
   *  The redeemed shoot.
   */
    async redeemShoot({ user, redeemable_shoot, newValues, role }) {
        const { Shoot, ShootService } = this.server.models();
        const { reportUserService, orderService } = this.server.services();

        const updatedRedeemableShoot = await Shoot.query().patchAndFetchById(
            redeemable_shoot.id,
            {
                redeemable_counter:
          redeemable_shoot.redeemable_total !== -1
              ? raw('redeemable_counter - 1')
              : raw('redeemable_counter + 1')
            }
        );

        if (updatedRedeemableShoot.redeemable_counter < 0) {
            throw new UnauthorizedError(
                `Shoot cannot be redeemed. The total of ${redeemable_shoot.redeemable_total} has been reached`
            );
        }

        const {
            time: { from, duration },
            photographer_id = null,
            notes,
            location,
            outlet_code,
            outlet_name,
            poc_email,
            poc_phone,
            poc_name,
            name,
            client_id
        } = newValues;

        const {
            order_id,
            address,
            contact_phone,
            contact_name,
            picture_number,
            price,
            photographer_revenue,
            type,
            name: redeemable_name,
            redeemable_total,
            is_brief_uploaded,
            video_number,
            video_duration,
            content
        } = redeemable_shoot;

        const isScheduled = from ? true : false;

        let startingStatus = Shoot.statuses.toSchedule;
        if (photographer_id && isScheduled) {
            startingStatus = Shoot.statuses.photographerAssigned;
        }
        else if (isScheduled) {
            startingStatus = Shoot.statuses.scheduled;
        }

        const savedShoot = await Shoot.query().insertAndFetch({
            name: name || redeemable_name,
            photographer_id,
            order_id,
            address: location || address,
            notes,
            contact_phone,
            contact_name,
            picture_number,
            price,
            type,
            photographer_revenue,
            datetime: from,
            duration,
            scheduled: isScheduled,
            status: startingStatus,
            last_edit_by: user.id,
            redeemable_shoot_id: redeemable_shoot.id,
            is_brief_uploaded,
            outlet_code,
            outlet_name,
            poc_email,
            poc_phone,
            poc_name,
            consumer_client_id: client_id || user.client_id,
            content,
            video_duration,
            video_number
        });

        const shootServices = await ShootService.query().where(
            'shoot_id',
            redeemable_shoot.id
        );
        await ShootService.query().insert(
            shootServices.map((shootService) => {
                delete shootService.id;
                return {
                    ...shootService,
                    shoot_id: savedShoot.id
                };
            })
        );

        if (redeemable_total === -1) {
            const { order } = await orderService.getOrder({
                id: order_id,
                user,
                role
            });
            await orderService.updateOrder({
                id: order_id,
                user,
                role,
                newValues: {
                    price: parseFloat(price) + parseFloat(order.price)
                }
            });
        }

        // NOTE: this is to count the number of created shoots for the reports
        reportUserService.createReportUser({
            shoot: savedShoot,
            performer_user_id: user.id,
            target_photographer_id: photographer_id,
            starting_status: Shoot.statuses.notCreated,
            target_status: savedShoot.status
        });

        return savedShoot;
    }

    /**
   * Get all shoots for specific status
   *
   * @param {status} indicates the state for which you want to filter
   *
   * @return {Object} count, picture_count
   * The number of selected status shoots and the total photos
   */
    async getCountShootsAndPictureCountForStatus({ status }) {
        const { Shoot } = this.server.models();

        const { count = '0', picture_count = '0' } =
      (await Shoot.query()
          .where('status', status)
          .count()
          .sum('picture_number as picture_count')
          .first()) || {};

        return {
            count: parseInt(count),
            picture_count: parseInt(picture_count)
        };
    }
    /**
 *
 * @param {Object} newShoot - The shoot after update
 */
    async handleScheduledToAssignedTransition(newShoot) {
        const { userService, notificationService, serviceService } = this.server.services();

        const { user } = await userService.getClientById({
            client_id: newShoot.consumer_client_id
        });

        const client = await userService.getFormattedUser({ id: user.id, role: 'client' });

        const { service } = await serviceService.getService({ id: newShoot.services[0].service_id });

        // Fetching details for the assigned photographer
        const photographer = newShoot.photographer_id
            ? await userService.getPhotographerById({ photographer_id: newShoot.photographer_id })
            : null;

        // Format Date & Time
        let formattedDate;
        let formattedTime;
        if (newShoot.datetime) {
            const { fromTime, toTime } = formattedDateTime({
                duration: newShoot.duration,
                fromDate: newShoot.datetime.toISOString()
            });
            formattedDate = formattedDateForEmail({
                fromDate: newShoot.datetime.toISOString()
            });
            formattedTime =  `${ fromTime } - ${ toTime}`;
        }

        const datetime = { date: formattedDate, time: formattedTime };

        // Notify the assigned photographer
        if (photographer) {
            const photographerNotification = {
                type: types.phShootAssigned,
                target: photographer.user,
                metaData: {
                    location: stringifyLocation({ address: newShoot.address }),
                    deliverables: newShoot.content === 'videography' ? newShoot.video_number : newShoot.picture_number,
                    isVideography: newShoot.content === 'videography',
                    packageName: newShoot.name,
                    shootId: newShoot.id,
                    outletCode: newShoot.outlet_code || null,
                    outletName: newShoot.outlet_name || null,
                    revenue: newShoot.photographer_revenue,
                    ...datetime
                }
            };

            notificationService.sendNotification(photographerNotification);
        }

        // Notify the admin about the new assignment
        const adminNotification = {
            type: types.adminShootAssigned,
            metaData: {
                phName: photographer ? photographer.user.name : '',
                shootId: newShoot.id,
                company: client.company_name,
                packageName: newShoot.name,
                location: stringifyLocation({ address: newShoot.address }),
                category: service.name,
                outletCode: newShoot.outlet_code || 'NA',
                outletName: newShoot.outlet_name || 'NA',
                deliverables: newShoot.content === 'videography' ? newShoot.video_number : newShoot.picture_number,
                price: newShoot.price,
                isVideography: newShoot.content === 'videography',
                ...datetime

            }

        };

        notificationService.sendNotification(adminNotification);
    }

    /**
 *
 * @param {Object} newShoot - The shoot after update
 * @param {Object} actionPerformer - The user that does the action
 */
    async handleAssignedToConfirmedTransition(newShoot, actionPerformer) {
        const { userService, notificationService, serviceService } = this.server.services();

        // Fetching details for the assigned photographer
        const photographer = newShoot.photographer_id
            ? await userService.getPhotographerById({ photographer_id: newShoot.photographer_id })
            : null;

        const { user } = await userService.getClientById({
            client_id: newShoot.consumer_client_id
        });

        const client = await userService.getFormattedUser({ id: user.id, role: 'client' });
        const parentClientId = await userService.getParentClientId({ userId: client.id });

        // Format Date & Time
        let formattedDate;
        let formattedTime;
        if (newShoot.datetime) {
            const { fromTime, toTime } = formattedDateTime({
                duration: newShoot.duration,
                fromDate: newShoot.datetime.toISOString()
            });
            formattedDate = formattedDateForEmail({
                fromDate: newShoot.datetime.toISOString()
            });
            formattedTime =  `${ fromTime } - ${ toTime}`;
        }

        const datetime = { date: formattedDate, time: formattedTime };

        const { service } = await serviceService.getService({ id: newShoot.services[0].service_id });

        // [1] Notify the client
        if (client) {
            const clientNotification = {
                type: types.clientShootConfirmed,
                target: client,
                metaData: {
                    location: stringifyLocation({ address: newShoot.address }),
                    deliverables: newShoot.content === 'videography' ? newShoot.video_number : newShoot.picture_number,
                    isVideography: newShoot.content === 'videography',
                    packageName: newShoot.name,
                    company: client.company_name,
                    shootId: newShoot.id,
                    ...datetime
                }
            };

            notificationService.sendNotification(clientNotification);
        }

        // if isSubClient
        if (parentClientId) {
            const parent = userService.getClientById({ client_id: parentClientId }).user;
            const clientNotification = {
                type: types.clientShootConfirmed,
                target: parent,
                metaData: {
                    location: stringifyLocation({ address: newShoot.address }),
                    deliverables: newShoot.content === 'videography' ? newShoot.video_number : newShoot.picture_number,
                    isVideography: newShoot.content === 'videography',
                    packageName: newShoot.name,
                    company: client.company_name,
                    shootId: newShoot.id,
                    ...datetime
                }
            };

            notificationService.sendNotification(clientNotification);
        }

        // Notify the assigned photographer
        if (photographer) {
            const photographerNotification = {
                type: types.phShootConfirmed,
                target: photographer.user,
                metaData: {
                    shootId: newShoot.id,
                    packageName: newShoot.name,
                    isVideography: newShoot.content === 'videography',
                    deliverables: newShoot.content === 'videography' ? newShoot.video_number : newShoot.picture_number,
                    company: client.company_name,
                    location: stringifyLocation({ address: newShoot.address }),
                    outletCode: newShoot.outlet_code || null,
                    outletName: newShoot.outlet_name || null,
                    revenue: newShoot.photographer_revenue,
                    ...datetime
                }
            };

            notificationService.sendNotification(photographerNotification);
        }

        // Notify the admin about the new assignment
        const adminNotification = {
            type: types.adminShootConfirmed,
            metaData: {
                phName: photographer ? photographer.user.name : '',
                actionPerformer: actionPerformer.name,
                shootId: newShoot.id,
                company: client.company_name,
                packageName: newShoot.name,
                location: stringifyLocation({ address: newShoot.address }),
                category: service.name,
                outletCode: newShoot.outlet_code || 'NA',
                outletName: newShoot.outlet_name || 'NA',
                deliverables: newShoot.content === 'videography' ? newShoot.video_number : newShoot.picture_number,
                price: newShoot.price,
                isVideography: newShoot.content === 'videography',
                ...datetime
            }

        };

        notificationService.sendNotification(adminNotification);
    }
    /**
 *
 * @param {Object} newShoot - The shoot after update
 * @param {Object} actionPerformer - The user that does the action
 */
    async handleConfirmedToUploadedTransition(newShoot, actionPerformer) {
        const { userService, notificationService, serviceService } = this.server.services();

        // Fetching details for the assigned photographer
        const photographer = newShoot.photographer_id
            ? await userService.getPhotographerById({ photographer_id: newShoot.photographer_id })
            : null;

        const { user } = await userService.getClientById({
            client_id: newShoot.consumer_client_id
        });

        const client = await userService.getFormattedUser({ id: user.id, role: 'client' });
        const parentClientId = await userService.getParentClientId({ userId: client.id });

        // Format Date & Time
        let formattedDate;
        let formattedTime;
        if (newShoot.datetime) {
            const { fromTime, toTime } = formattedDateTime({
                duration: newShoot.duration,
                fromDate: newShoot.datetime.toISOString()
            });
            formattedDate = formattedDateForEmail({
                fromDate: newShoot.datetime.toISOString()
            });
            formattedTime =  `${ fromTime } - ${ toTime}`;
        }

        const datetime = { date: formattedDate, time: formattedTime };

        const { service } = await serviceService.getService({ id: newShoot.services[0].service_id });

        // [1] Notify the client
        if (client) {
            const clientNotification = {
                type: types.clientRawsUploaded,
                target: client,
                metaData: {
                    shootId: newShoot.id,
                    packageName: newShoot.name,
                    isVideography: newShoot.content === 'videography',
                    deliverables: newShoot.content === 'videography' ? newShoot.video_number : newShoot.picture_number
                }
            };

            notificationService.sendNotification(clientNotification);
        }

        if (parentClientId) {
            const parent = userService.getClientById({ client_id: parentClientId }).user;
            const clientNotification = {
                type: types.clientRawsUploaded,
                target: parent,
                metaData: {
                    shootId: newShoot.id,
                    packageName: newShoot.name,
                    isVideography: newShoot.content === 'videography',
                    deliverables: newShoot.content === 'videography' ? newShoot.video_number : newShoot.picture_number
                }
            };

            notificationService.sendNotification(clientNotification);
        }

        // [2] Notify the photographer
        if (photographer) {
            const photographerNotification = {
                type: types.phRawsUploaded,
                target: photographer.user,
                metaData: {
                    shootId: newShoot.id,
                    packageName: newShoot.name,
                    isVideography: newShoot.content === 'videography',
                    deliverables: newShoot.content === 'videography' ? newShoot.video_number : newShoot.picture_number,
                    company: client.company_name
                }
            };

            notificationService.sendNotification(photographerNotification);
        }

        // [3] Notify the admin
        const adminNotification = {
            type: types.adminRawsUploaded,
            metaData: {
                shootId: newShoot.id,
                company: client.company_name,
                packageName: newShoot.name,
                location: stringifyLocation({ address: newShoot.address }),
                category: service.name,
                outletCode: newShoot.outlet_code || 'NA',
                outletName: newShoot.outlet_name || 'NA',
                deliverables: newShoot.content === 'videography' ? newShoot.video_number : newShoot.picture_number,
                price: newShoot.price,
                actionPerformer: actionPerformer.name,
                isVideography: newShoot.content === 'videography',
                ...datetime
            }

        };

        notificationService.sendNotification(adminNotification);
    }
    /**
 *
 * @param {Object} newShoot - The shoot after update
 * @param {Object} actionPerformer - The user that does the action
 */
    async handleUploadedToReadyTransition(newShoot) {
        const { userService, notificationService } = this.server.services();

        const { user } = await userService.getClientById({
            client_id: newShoot.consumer_client_id
        });
        const client = await userService.getFormattedUser({ id: user.id, role: 'client' });
        const parentClientId = await userService.getParentClientId({ userId: client.id });

        // [1] Notify the client
        if (client) {
            const clientNotification = {
                type: types.clientContentReady,
                target: client,
                metaData: {
                    shootId: newShoot.id,
                    packageName: newShoot.name,
                    isVideography: newShoot.content === 'videography',
                    deliverables: newShoot.content === 'videography' ? newShoot.video_number : newShoot.picture_number
                }
            };

            notificationService.sendNotification(clientNotification);
        }

        if (parentClientId) {
            const parent = await userService.getClientById({ client_id: parentClientId }).user;
            const clientNotification = {
                type: types.clientContentReady,
                target: parent,
                metaData: {
                    shootId: newShoot.id,
                    packageName: newShoot.name,
                    isVideography: newShoot.content === 'videography',
                    deliverables: newShoot.content === 'videography' ? newShoot.video_number : newShoot.picture_number
                }
            };

            notificationService.sendNotification(clientNotification);
        }

        // [2] Notify the admin
        const adminNotification = {
            type: types.adminContentReady,
            metaData: {
                shootId: newShoot.id,
                packageName: newShoot.name,
                isVideography: newShoot.content === 'videography',
                deliverables: newShoot.content === 'videography' ? newShoot.video_number : newShoot.picture_number,
                company: client.company_name
            }

        };
        notificationService.sendNotification(adminNotification);
    }

    /**
 *
 * @param {Object} newShoot - The shoot after update
 * @param {Object} actionPerformer - The user that does the action
 */
    async handleReadyToCompletedTransition(newShoot) {
        const { userService, notificationService, serviceService } = this.server.services();

        // Fetching details for the assigned photographer
        const photographer = newShoot.photographer_id
            ? await userService.getPhotographerById({ photographer_id: newShoot.photographer_id })
            : null;

        const { user } = await userService.getClientById({
            client_id: newShoot.consumer_client_id
        });

        const client = await userService.getFormattedUser({ id: user.id, role: 'client' });
        const parentClientId = await userService.getParentClientId({ userId: client.id });
        let formattedTime; let formattedDate;
        if (newShoot.datetime) {
            const { fromTime, toTime } = formattedDateTime({
                duration: newShoot.duration,
                fromDate: newShoot.datetime.toISOString()
            });
            formattedDate = formattedDateForEmail({
                fromDate: newShoot.datetime.toISOString()
            });
            formattedTime =  `${ fromTime } - ${ toTime}`;
        }

        const datetime = { date: formattedDate, time: formattedTime };

        const { service } = await serviceService.getService({ id: newShoot.services[0].service_id });

        // [1] Notify the client
        if (client) {
            const clientNotification = {
                type: types.reviewShoot,
                target: client
            };
            notificationService.sendNotification(clientNotification);
        }

        if (parentClientId) {
            const parent = await userService.getClientById({ client_id: parentClientId }).user;
            const clientNotification = {
                type: types.reviewShoot,
                target: parent
            };
            notificationService.sendNotification(clientNotification);
        }

        // [2] Notify the photographer
        if (photographer) {
            const photographerNotification = {
                type: types.reviewShoot,
                target: photographer.user
            };
            await notificationService.sendNotification(photographerNotification);
        }

        // [3] Notify the admin
        const adminNotification = {
            type: types.adminShootCompleted,
            metaData: {
                shootId: newShoot.id,
                company: client.company_name,
                packageName: newShoot.name,
                location: stringifyLocation({ address: newShoot.address }),
                category: service.name,
                outletCode: newShoot.outlet_code || 'NA',
                outletName: newShoot.outlet_name || 'NA',
                deliverables: newShoot.content === 'videography' ? newShoot.video_number : newShoot.picture_number,
                price: newShoot.price,
                isVideography: newShoot.content === 'videography',
                ...datetime
            }

        };
        await notificationService.sendNotification(adminNotification);
    }
    /**
 * Handle notifications for when a shoot transitions from scheduled to assigned.
 * It's assumed that the transition has been verified before this function is called.
 *
 * @param {Object} newShoot - The shoot after update
 * @param {Object} actionPerformer - The user that does the action
 */
    async handleShootUnassignedNotification(oldShoot, actionPerformer, role) {
        const { userService, notificationService } = this.server.services();

        // Fetching details for the assigned photographer
        const photographer = oldShoot.photographer_id
            ? await userService.getPhotographerById({ photographer_id: oldShoot.photographer_id })
            : null;

        const { user } = await userService.getClientById({
            client_id: oldShoot.consumer_client_id
        });

        const client = await userService.getFormattedUser({ id: user.id, role: 'client' });

        // Format Date & Time
        let formattedDate;
        let formattedTime;
        if (oldShoot.datetime) {
            const { fromTime, toTime } = formattedDateTime({
                duration: oldShoot.duration,
                fromDate: oldShoot.datetime.toISOString()
            });
            formattedDate = formattedDateForEmail({
                fromDate: oldShoot.datetime.toISOString()
            });
            formattedTime =  `${ fromTime } - ${ toTime}`;
        }

        const datetime = { date: formattedDate, time: formattedTime };

        // Notify the assigned photographer
        if (photographer) {
            const photographerNotification = {
                type: types.shootUnassigned,
                target: photographer.user,
                metaData: {
                    location: stringifyLocation({ address: oldShoot.address }),
                    deliverables: oldShoot.content === 'videography' ? oldShoot.video_number : oldShoot.picture_number,
                    isVideography: oldShoot.content === 'videography',
                    packageName: oldShoot.name,
                    shootId: oldShoot.id,
                    outletCode: oldShoot.outlet_code || null,
                    outletName: oldShoot.outlet_name || null,
                    actionPerformer: role === 'admin' ? actionPerformer.name : photographer.user.name,
                    company: client.company_name,
                    role,
                    ...datetime
                }
            };

            await notificationService.sendNotification(photographerNotification);
        }

        // [2] Notify the admin
        const adminNotification = {
            type: types.shootUnassigned,
            metaData: {
                location: stringifyLocation({ address: oldShoot.address }),
                deliverables: oldShoot.content === 'videography' ? oldShoot.video_number : oldShoot.picture_number,
                isVideography: oldShoot.content === 'videography',
                packageName: oldShoot.name,
                shootId: oldShoot.id,
                outletCode: oldShoot.outlet_code || null,
                outletName: oldShoot.outlet_name || null,
                actionPerformer: role === 'admin' ? actionPerformer.name : photographer.user.name,
                company: client.company_name,
                role,
                ...datetime
            }
        };
        await notificationService.sendNotification(adminNotification);
    }
    /**
 * Handles notifications based on the status transition of a shoot.
 *
 * @param {Object} oldShoot - The shoot before update.
 * @param {Object} newShoot - The shoot after update.
 */
    handleStatusChangeNotification({ oldShoot, newShoot, user, role }) {
        if (oldShoot.status === 'scheduled' && newShoot.status === 'assigned') {
            this.handleScheduledToAssignedTransition(newShoot);
        }
        else if ((oldShoot.status === 'assigned' || oldShoot.status === 'confirmed' ) && newShoot.status === 'scheduled' ) {
            this.handleShootUnassignedNotification(oldShoot, user, role);
        }
        else if (oldShoot.status === 'assigned' && newShoot.status === 'confirmed') {
            this.handleAssignedToConfirmedTransition(newShoot, user);
        }
        else if (oldShoot.status === 'confirmed' && newShoot.status === 'uploaded') {
            this.handleConfirmedToUploadedTransition(newShoot, user);
        }
        else if (oldShoot.status === 'uploaded' && newShoot.status === 'ready') {
            this.handleUploadedToReadyTransition(newShoot);
        }
        else if (newShoot.status === 'canceled') {
        }
        else if (oldShoot.status === 'ready' && newShoot.status === 'completed') {
            this.handleReadyToCompletedTransition(newShoot);
        }
    }

    /**
   * Builds a query that fetches all the shoots that overlap a given
   * one (up to some given tolerance).
   *
   * @param {Shoot} shoot
   *  The shoot to fetch overlapping shoots for.
   * @param {int} photographerIdleMinutes
   *  The tolerance (in minutes) to add to the start and end of
   *  each shoot when checking for overlaps, in order to account
   *  for a given idle time for photographers (default: 0).
   *
   * @returns {QueryBuilder}
   */
    getOverlappingShoots({ shoot, photographerIdleMinutes = 0 }) {
        const { Shoot } = this.server.models();

        return Shoot.query()
            .whereNot('id', shoot.id)
            .whereRaw(
                `(
                (
                    (?)::timestamp - ( ${photographerIdleMinutes} || ' minutes')::interval < datetime 
                        and 
                    datetime < ((?)::timestamp 
                        + ( ${shoot.duration} || ' minutes')::interval 
                        + ( ${photographerIdleMinutes} || ' minutes')::interval
                )
                or
                (
                    datetime - ( ${photographerIdleMinutes} || ' minutes')::interval) < (?)::timestamp
                        and
                    (?)::timestamp < datetime
                        + ( duration || ' minutes')::interval
                        + ( ${photographerIdleMinutes} || ' minutes')::interval
                )
            )`,
                [shoot.datetime, shoot.datetime, shoot.datetime, shoot.datetime]
            );
    }

    /**
   * Retrieve max price in shoots
   *
   * @param {string} user_id user id
   *
   * @param {Object} role user role
   *
   * @returns {string} max_price
   */
    async getMaxPriceForUser({ user, role }) {
        const { Shoot } = this.server.models();

        const query = Shoot.query().leftJoinRelated('[ order ]');
        if (role === 'client') {
            query
                .where('order.client_id', user.client_id)
                .select(raw('coalesce(max(??), 0)', 'shoots.price').as('max_price'));
        }
        else if (role === 'photographer') {
            query
                .where('shoots.photographer_id', user.photographer_id)
                .select(
                    raw('coalesce(max(??), 0)', 'shoots.photographer_revenue').as(
                        'max_price'
                    )
                );
        }
        else if (role === 'admin' || role === 'editor') {
            query.select(raw('coalesce(max(??), 0)', 'shoots.price').as('max_price'));
        }
        else {
            throw new InternalError(`Invalid user role (${role})`);
        }

        const { max_price = 0 } = (await query.first()) || {};
        return max_price;
    }

    /**
   * Retrieve list of cities and states
   *
   * @param {Object} user user object
   *
   * @param {Object} role user role
   *
   * @returns {Array} cities
   * @returns {Array} states
   */
    async getListOfCitiesAndStates({ user, role }) {
        const { Shoot } = this.server.models();

        const query = Shoot.query().leftJoinRelated('[ order ]');
        if (role === 'client') {
            query.where('order.client_id', user.client_id);
        }
        else if (role === 'photographer') {
            query.where('shoots.photographer_id', user.photographer_id);
        }
        else if (role !== 'admin' && role !== 'editor') {
            throw new InternalError(`Invalid user role (${role})`);
        }

        const addresses = await query.select('address as address');
        const cities = [
            ...new Set(
                addresses.map(({ address }) => address?.city).filter((el) => el)
            )
        ];
        const states = [
            ...new Set(
                addresses.map(({ address }) => address?.state).filter((el) => el)
            )
        ];
        return { states, cities };
    }

    async getListServicesOfUsers({ user, role }) {
        if (role === 'admin' || role === 'editor' || role === 'photographer') {
            return null;
        }

        const { Shoot } = this.server.models();
        const query = Shoot.query().leftJoinRelated('[ order ]');

        if (role === 'client') {
            query.where('order.client_id', user.client_id);
        }

        query.joinRelated('services');
        const servicesNames = await query.select('services.name as name');
        const services = [...new Set(servicesNames.map(({ name }) => name))];
        return { services };
    }

    /**
   * Delete all shoot belonging to a specific user
   *
   * @param {integer} user
   *  The user to delete shoots for.
   *
   * @params {string} loggedRole
   *  The role of the user who is logged in.
   *
   * @returns {Array} shootIds, possibleUploadedShootIds
   * The list of shoot ids that were deleted.
   */
    async deleteShootsByUser({ user, loggedRole }) {
        const { Shoot, Order } = this.server.models();

        const query = Shoot.query().delete();

        if (user.role === 'photographer') {
            query.whereIn('photographer_id', user.photographer_id);
        }
        else if (user.role === 'client') {
            query.whereIn(
                'order_id',
                Order.query().select('id').where('client_id', user.client_id)
            );
        }

        const shoots = await query.returning('*');

        const possibleUploadedStatuses = [
            Shoot.statuses.photosUploaded,
            Shoot.statuses.photosReady,
            Shoot.statuses.completed,
            Shoot.statuses.canceled
        ];

        const possibleUploadedShootIds = shoots
            .filter(({ status }) => possibleUploadedStatuses.includes(status))
            .map(({ id }) => id);

        const shootIds = shoots.map(({ id }) => id);

        return { shootIds, possibleUploadedShootIds };
    }

    /**
   * Delete all storage belonging to shoots
   *
   * @param {Array} shoot_ids
   *  The list of shoot ids for which storage must be deleted.
   *
   */
    async deleteAllStorageResourcesByShootIds({ shoot_ids }) {
        const { storageService } = this.server.services();

        const { rawPaths, processedPaths, briefPaths } =
      await storageService.getPathsByShootIds({ shoot_ids });

        const {
            raw_photos_bucket_name,
            processed_photos_bucket_name,
            briefs_bucket_name
        } = this.server.settings.app.amazon;
        try {
            storageService.deleteObjects({
                bucket: raw_photos_bucket_name,
                filepaths: rawPaths
            });
            storageService.deleteObjects({
                bucket: processed_photos_bucket_name,
                filepaths: processedPaths
            });
            storageService.deleteObjects({
                bucket: briefs_bucket_name,
                filepaths: briefPaths
            });
        }
        catch (error) {
            throw new InternalError(
                `Error deleting storage resources: ${error.message}`
            );
        }
    }

    /**
 * Sends a notification for shoot creation.
 *
 * @param {Object} shoot - Information about the shoot.
 * @param {Object} actionPerformer - The user who is performing the action.
 * @param {string} role - The role of the user performing the action.
 */
    async sendCreateNotification( shoot  ) {
    // Destructure services for easier access
        const { userService, notificationService, serviceService } = this.server.services();

        if (shoot.status === 'redeemable') {
            return;
        }

        let date = null;
        let time = null;

        // Fetch client details
        const { user } = await userService.getClientById({
            client_id: shoot.consumer_client_id
        });
        const client = await userService.getFormattedUser({ id: user.id, role: 'client' });
        const parentClientId = await userService.getParentClientId({ userId: client.id });

        // Fetch service
        const { service } = await serviceService.getService({ id: shoot.services[0].service_id });

        if (shoot.datetime) {
            const { fromTime, toTime } = formattedDateTime({ duration: shoot.duration, fromDate: shoot.datetime.toISOString() });
            date = formattedDateForEmail({ fromDate: shoot.datetime.toISOString() });
            time =  `${ fromTime } - ${ toTime}`;
        }

        const metaData = {
            shootId: shoot.id,
            company: client.company_name,
            packageName: shoot.name,
            location: stringifyLocation({ address: shoot.address }),
            date: date || 'NA',
            time: time || 'NA',
            category: service.name,
            outletCode: shoot.outlet_code || 'NA',
            outletName: shoot.outlet_name || 'NA',
            deliverables: shoot.content === 'videography' ? shoot.video_number : shoot.picture_number,
            price: shoot.price,
            isVideography: shoot.content === 'videography'
        };

        if (client) {
            const clientNotification = {
                type: types.shootCreated,
                target: client,
                metaData
            };
            notificationService.sendNotification(clientNotification);
        }

        if (parentClientId) {
            const parent = await userService.getClientById({ client_id: parentClientId }).user;
            const clientNotification = {
                type: types.shootCreated,
                target: parent,
                metaData
            };
            notificationService.sendNotification(clientNotification);
        }

        const adminNotification = {
            type: types.shootCreated,
            metaData
        };
        notificationService.sendNotification(adminNotification);

        // Retrieve photographer details if applicable
        if (shoot.photographer_id) {
            const phTarget = await userService.getPhotographerById({ photographer_id: shoot.photographer_id }).user;
            const phNotification = {
                type: types.phShootAssigned,
                target: phTarget,
                metaData: {
                    location: stringifyLocation({ address: shoot.address }),
                    deliverables: shoot.content === 'videography' ? shoot.video_number : shoot.picture_number,
                    isVideography: shoot.content === 'videography',
                    packageName: shoot.name,
                    shootId: shoot.id,
                    outletCode: shoot.outlet_code || null,
                    outletName: shoot.outlet_name || null,
                    revenue: shoot.photographer_revenue,
                    date,
                    time
                }
            };
            notificationService.sendNotification(phNotification);
        }

    }

    /**
 * Checks for changes in specific fields of oldShoot and newShoot.
 *
 * @param {Object} oldShoot - The original shoot object.
 * @param {Object} newShoot - The edited shoot object.
 * @param {Object} oldService - The original service object.
 * @param {Object} service - The edited service object.
 * @returns {boolean} - Whether there are differences in the specified fields.
 */
    hasChanges(oldShoot, newShoot, oldService, service) {
    // Comparing fields
        return (
            oldShoot.name !== newShoot.name ||
        oldShoot.datetime.toISOString() !== newShoot.datetime.toISOString() ||
        stringifyLocation({ address: oldShoot.address }) !== stringifyLocation({ address: newShoot.address }) ||
        oldService.name !== service.name ||
        oldShoot.services[0].package.name !== newShoot.services[0].package.name ||
        (oldShoot.content === 'videography' ? oldShoot.video_number : oldShoot.picture_number) !==
        (newShoot.content === 'videography' ? newShoot.video_number : newShoot.picture_number) ||
        oldShoot.price !== newShoot.price
        );
    }

    /**
 * Sends a notification for shoot editing.
 *
 * @param {Object} oldShoot - Information about the original shoot.
 * @param {Object} newShoot - Information about the edited shoot.
 * @param {Object} actionPerformer - Information about the user that performed the action.
 */
    async sendEditNotification( oldShoot, newShoot, actionPerformer ) {
        const { userService, notificationService, serviceService } = this.server.services();

        // Fetching details for the assigned photographer
        const newPhotographer = newShoot.photographer_id
            ? await userService.getPhotographerById({ photographer_id: newShoot.photographer_id })
            : null;

        // Fetching details for the assigned photographer
        const oldPhotographer = oldShoot.photographer_id
            ? await userService.getPhotographerById({ photographer_id: oldShoot.photographer_id })
            : null;

        // Fetch client details
        const { user } = await userService.getClientById({
            client_id: newShoot.consumer_client_id
        });
        const client = await userService.getFormattedUser({ id: user.id, role: 'client' });
        const parentClientId = await userService.getParentClientId({ userId: client.id });

        const { service: oldService } = await serviceService.getService({ id: oldShoot.services[0].service_id });
        const { service } = await serviceService.getService({ id: newShoot.services[0].service_id });

        let newFormattedDate; let newFormattedTime;
        let oldFormattedDate; let oldFormattedTime;

        if (oldShoot.datetime) {
            const { fromTime, toTime } = formattedDateTime({ duration: oldShoot.duration, fromDate: oldShoot.datetime.toISOString() });
            oldFormattedDate = formattedDateForEmail({ fromDate: oldShoot.datetime.toISOString() });
            oldFormattedTime =  `${ fromTime } - ${ toTime}`;
        }

        if (newShoot && newShoot.datetime) {
            const { fromTime, toTime } = formattedDateTime({ duration: newShoot.duration, fromDate: newShoot.datetime.toISOString() });
            newFormattedDate = formattedDateForEmail({ fromDate: newShoot.datetime.toISOString() });
            newFormattedTime =  `${ fromTime } - ${ toTime}`;
        }

        const editMetadata = {
            shootId: oldShoot.id,
            oldShootName: oldShoot.name,
            oldDate: oldFormattedDate,
            oldTime: oldFormattedTime,
            oldLocation: stringifyLocation({ address: oldShoot.address }),
            oldCategory: oldService.name,
            oldPackage: oldShoot.services[0].package.name,
            oldDeliverables: oldShoot.content === 'videography' ? oldShoot.video_number : oldShoot.picture_number,
            oldPrice: oldShoot.price,
            newShootName: newShoot.name,
            newDate: newFormattedDate,
            newTime: newFormattedTime,
            newLocation: stringifyLocation({ address: newShoot.address }),
            newCategory: service.name,
            newPackage: newShoot.services[0].package.name,
            newDeliverables: newShoot.content === 'videography' ? newShoot.video_number : newShoot.picture_number,
            newPrice: newShoot.price,
            isVideography: newShoot.content === 'videography'
        };

        const isNewPhotographer = newPhotographer && ( oldPhotographer.user.id !== newPhotographer.user.id);

        // [1] when the shoot status is assigned && newPhotographer
        if ( isNewPhotographer && oldShoot.status === 'assigned') {
            // Notify the assigned photographer
            const oldPhNotification = {
                type: types.shootUnassigned,
                target: oldPhotographer.user,
                metaData: {
                    location: stringifyLocation({ address: oldShoot.address }),
                    deliverables: oldShoot.content === 'videography' ? oldShoot.video_number : oldShoot.picture_number,
                    isVideography: oldShoot.content === 'videography',
                    packageName: oldShoot.name,
                    shootId: oldShoot.id,
                    outletCode: oldShoot.outlet_code || null,
                    outletName: oldShoot.outlet_name || null,
                    actionPerformer: actionPerformer.name,
                    company: client.company_name,
                    role: 'admin',
                    time: oldFormattedTime,
                    date: oldFormattedDate
                }
            };
            const newPhNotification = {
                type: types.phShootAssigned,
                target: newPhotographer.user,
                metaData: {
                    location: stringifyLocation({ address: newShoot.address }),
                    deliverables: newShoot.content === 'videography' ? newShoot.video_number : newShoot.picture_number,
                    isVideography: newShoot.content === 'videography',
                    packageName: newShoot.name,
                    shootId: newShoot.id,
                    outletCode: newShoot.outlet_code || null,
                    outletName: newShoot.outlet_name || null,
                    revenue: newShoot.photographer_revenue,
                    date: newFormattedDate,
                    time: newFormattedTime
                }
            };

            // Notify the admin about the new assignment
            const adminNotification = {
                type: types.adminShootAssigned,
                metaData: {
                    phName: newPhotographer ? newPhotographer.user.name : '',
                    shootId: newShoot.id,
                    company: client.company_name,
                    packageName: newShoot.name,
                    location: stringifyLocation({ address: newShoot.address }),
                    category: service.name,
                    outletCode: newShoot.outlet_code || 'NA',
                    outletName: newShoot.outlet_name || 'NA',
                    deliverables: newShoot.content === 'videography' ? newShoot.video_number : newShoot.picture_number,
                    price: newShoot.price,
                    date: newFormattedDate,
                    time: newFormattedTime,
                    isVideography: newShoot.content === 'videography'
                }
            };
            // Send The notifications
            notificationService.sendNotification(oldPhNotification);
            notificationService.sendNotification(newPhNotification);
            notificationService.sendNotification(adminNotification);
        }
        else if ( isNewPhotographer && oldShoot.status === 'confirmed' ) {
            // Notify the assigned photographer
            const oldPhNotification = {
                type: types.shootUnassigned,
                target: oldPhotographer.user,
                metaData: {
                    location: stringifyLocation({ address: oldShoot.address }),
                    deliverables: oldShoot.content === 'videography' ? oldShoot.video_number : oldShoot.picture_number,
                    isVideography: oldShoot.content === 'videography',
                    packageName: oldShoot.name,
                    shootId: oldShoot.id,
                    outletCode: oldShoot.outlet_code || null,
                    outletName: oldShoot.outlet_name || null,
                    actionPerformer: actionPerformer.name,
                    company: client.company_name,
                    role: 'admin',
                    time: oldFormattedTime,
                    date: oldFormattedDate
                }
            };

            const newPhNotification = {
                type: types.phShootConfirmed,
                target: newPhotographer.user,
                metaData: {
                    shootId: newShoot.id,
                    packageName: newShoot.name,
                    isVideography: newShoot.content === 'videography',
                    deliverables: newShoot.content === 'videography' ? newShoot.video_number : newShoot.picture_number,
                    company: client.company_name,
                    location: stringifyLocation({ address: newShoot.address }),
                    outletCode: newShoot.outlet_code || null,
                    outletName: newShoot.outlet_name || null,
                    revenue: newShoot.photographer_revenue,
                    date: newFormattedDate,
                    time: newFormattedTime
                }
            };

            // Notify the admin about the new assignment
            const adminNotification = {
                type: types.adminShootConfirmed,
                metaData: {
                    phName: newPhotographer ? newPhotographer.user.name : '',
                    actionPerformer: actionPerformer.name,
                    shootId: newShoot.id,
                    company: client.company_name,
                    packageName: newShoot.name,
                    location: stringifyLocation({ address: newShoot.address }),
                    category: service.name,
                    outletCode: newShoot.outlet_code || 'NA',
                    outletName: newShoot.outlet_name || 'NA',
                    deliverables: newShoot.content === 'videography' ? newShoot.video_number : newShoot.picture_number,
                    price: newShoot.price,
                    isVideography: newShoot.content === 'videography',
                    date: newFormattedDate,
                    time: newFormattedTime
                }
            };

            notificationService.sendNotification(oldPhNotification);
            notificationService.sendNotification(newPhNotification);
            notificationService.sendNotification(adminNotification);
        }

        // [3] send these editing notifications if there has been any changes to the shoot data
        // Check for changes in specific fields before notifying
        if (this.hasChanges(oldShoot, newShoot, oldService, service)) {
            const adminEditNotification = {
                type: types.shootEdited,
                metaData: { ...editMetadata, actionPerformer: actionPerformer.name }
            };
            const clientEditNotification = {
                type: types.shootEdited,
                target: client,
                metaData: { ...editMetadata }
            };

            notificationService.sendNotification(adminEditNotification);
            notificationService.sendNotification(clientEditNotification);

            if (!isNewPhotographer && oldPhotographer) {
                const phEditNotification = {
                    type: types.shootEdited,
                    target: oldPhotographer.user,
                    metaData: { ...editMetadata,
                        actionPerformer: actionPerformer.name,
                        newPrice: newShoot.photographer_revenue,
                        oldPrice: oldShoot.photographer_revenue
                    }
                };
                notificationService.sendNotification(phEditNotification);
            }

            // if subClient
            if (parentClientId) {
                const parent = await userService.getClientById({ client_id: parentClientId }).user;
                notificationService.sendNotification({ ...clientEditNotification, target: parent });
            }
        }
    }

    /**
   * Centralized method to patch is_brief_uploaded in redeemed shoots
   * @param {Object} user
   *  The user that is performing the action
   * @param {Object} shoot
   *  The created (or old) shoot
   * @param {Boolean} is_brief_uploaded
   *  The boolean that refers to brief upload
   */
    async updateRedeemedShootsBrief({ shoot, user = {}, is_brief_uploaded }) {
        const { Shoot } = this.server.models();
        try {
            await Shoot.query().where('redeemable_shoot_id', shoot.id).patch({
                is_brief_uploaded,
                last_edit_by: user.id,
                last_update: Date.now()
            });
        }
        catch (error) {
            this.server.logger.warn({
                context: 'ShootService.updateRedeemedShootsBrief',
                error
            });
            return;
        }

        return;
    }

    /**
   * Assigns shoot consumed by a subclient to a provided main client
   * @param {Object} affectedUser
   *  The affected user
   */
    async assignSubClientsShootsToMainClient({ affectedUser }) {
        const { Shoot } = this.server.models();
        try {
            const shoots = await Shoot.query()
                .leftJoinRelated('order')
                .where('order.client_id', affectedUser.client_id);

            const shootIds = shoots.map(({ id }) => id);

            await Shoot.query()
                .leftJoinRelated('order')
                .whereIn('id', shootIds)
                .patch({
                    consumer_client_id: affectedUser.client_id,
                    last_update: Date.now()
                });
        }
        catch (error) {
            this.server.logger.warn({
                context: 'ShootService.assignSubClientsShootsToMainClient',
                error
            });
            return false;
        }

        return true;
    }
    /**
 * Assigns shoots consumed by a subclient to a provided main client
 * @param {Object} options
 *  - {Object} affectedUser: The affected user (main client)
 *  - {Object} subUser: The subclient user
 */
    async assignSubClientShootsToMainClient({ affectedUser, subUser }) {
        const { Shoot } = this.server.models();

        try {
        // Retrieve shoots associated with the subclient
            const shoots = await Shoot.query()
                .leftJoinRelated('order')
                .where('order.client_id', subUser.client_id);

            const shootIds = shoots.map(({ id }) => id);

            // Update shoots to be associated with the main client
            await Shoot.query()
                .leftJoinRelated('order')
                .whereIn('id', shootIds)
                .patch({
                    consumer_client_id: affectedUser.client_id,
                    last_update: new Date()
                });

            return true;
        }
        catch (error) {
            this.server.logger.warn({
                context: 'ShootService.assignSubClientShootsToMainClient',
                error
            });
            return false;
        }
    }

    /**
     * Get dashboard statistics for a user
     *
     * @param {Object} user - The user object
     * @param {string} role - User role (photographer/client)
     * @returns {Object} Dashboard statistics including upcoming, completed and monthly shoots
     */
    async getDashboardStats({ user, role }) {
        if (role !== 'client') {
            throw new Error('Only client role is supported');
        }

        const { Shoot, Image } = this.server.models();

        // Base query with client role filters
        const baseQuery = () => {
            const query = Shoot.query().join('orders', 'orders.id', '=', 'shoots.order_id');

            if (user.parent_client_id) {
                // Sub-client: only see their specific shoots
                query.where('shoots.consumer_client_id', user.client_id);
            }
            else {
                // Main client: see their own shoots and sub-client shoots
                query.where('orders.client_id', user.client_id);
            }

            return query;
        };

        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

        // Get upcoming shoots and previous period
        const upcomingQuery = baseQuery()
            .whereIn('shoots.status', [
                Shoot.statuses.photographerAssigned,
                Shoot.statuses.confirmed,
                Shoot.statuses.scheduled
            ])
            .count('* as count')
            .first();

        const previousUpcomingQuery = baseQuery()
            .whereIn('shoots.status', [
                Shoot.statuses.photographerAssigned,
                Shoot.statuses.confirmed,
                Shoot.statuses.scheduled
            ])
            .where('datetime', '<', startOfMonth)
            .count('* as count')
            .first();

        // Get completed shoots and previous period
        const completedQuery = baseQuery()
            .whereIn('shoots.status', [
                Shoot.statuses.completed,
                Shoot.statuses.photosReady
            ])
            .count('* as count')
            .first();

        const previousCompletedQuery = baseQuery()
            .whereIn('shoots.status', [
                Shoot.statuses.completed,
                Shoot.statuses.photosReady
            ])
            .where('datetime', '<', startOfMonth)
            .count('* as count')
            .first();

        // Get this month's and last month's shoots
        const monthlyQuery = baseQuery()
            .whereBetween('datetime', [startOfMonth, endOfMonth])
            .count('* as count')
            .first();

        const lastMonthlyQuery = baseQuery()
            .whereBetween('datetime', [startOfLastMonth, endOfLastMonth])
            .count('* as count')
            .first();

        // Get total images count
        const totalImagesQuery = Image.query()
            .join('shoots', 'shoots.id', '=', 'images.shoot_id')
            .join('orders', 'orders.id', '=', 'shoots.order_id')
            .whereIn('shoots.status', ['completed', 'ready']);

        if (user.parent_client_id) {
            totalImagesQuery.where('shoots.consumer_client_id', user.client_id);
        }
        else {
            totalImagesQuery.where('orders.client_id', user.client_id);
        }

        // Get monthly content statistics
        const monthlyImagesQuery = totalImagesQuery.clone()
            .whereBetween('images.embedded_at', [startOfMonth, endOfMonth]);

        const lastMonthImagesQuery = totalImagesQuery.clone()
            .whereBetween('images.embedded_at', [startOfLastMonth, endOfLastMonth]);

        // Execute all queries in parallel
        const [
            upcoming,
            previousUpcoming,
            completed,
            previousCompleted,
            monthly,
            lastMonthly,
            totalImages,
            monthlyImages,
            lastMonthImages
        ] = await Promise.all([
            upcomingQuery,
            previousUpcomingQuery,
            completedQuery,
            previousCompletedQuery,
            monthlyQuery,
            lastMonthlyQuery,
            totalImagesQuery.count('* as count').first(),
            monthlyImagesQuery.count('* as count').first(),
            lastMonthImagesQuery.count('* as count').first()
        ]);

        // Calculate growth percentages
        const calculateGrowth = (current, previous) => {
            const currentValue = parseInt(current?.count || 0);
            const previousValue = parseInt(previous?.count || 0);
            if (previousValue === 0) {
                return currentValue > 0 ? 100 : 0;
            }

            return ((currentValue - previousValue) / previousValue) * 100;
        };

        return {
            upcoming_shoots: parseInt(upcoming?.count || 0),
            upcoming_shoots_growth: calculateGrowth(upcoming, previousUpcoming),
            completed_shoots: parseInt(completed?.count || 0),
            completed_shoots_growth: calculateGrowth(completed, previousCompleted),
            monthly_shoots: parseInt(monthly?.count || 0),
            monthly_shoots_growth: calculateGrowth(monthly, lastMonthly),
            total_images: parseInt(totalImages?.count || 0),
            monthly_images: parseInt(monthlyImages?.count || 0),
            monthly_images_growth: calculateGrowth(monthlyImages, lastMonthImages)
        };
    }

};
