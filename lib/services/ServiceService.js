'use strict';

const Schmervice = require('schmervice');
const { NotFoundError, ValidationError } = require('../helpers/errors');

module.exports = class ServiceService extends Schmervice.Service {

    /**
     * Get services (from the db) given filters.
     *
     * @param {array} purposes
     *  The array of purposes id
     *
     * @param names
     * @return {Object} services
     *  The db services data.
     */
    async getServices({ purposes, names }) {

        const { Service } = this.server.models();

        const query = Service.query();

        if (purposes) {
            query.whereIn('purpose_id', purposes);
        }

        if (names && names.length > 0) {
            query.whereIn('name', names);
        }

        const services = await query;
        return { services };
    }

    /**
     * Get a service (from the db) given its id.
     *
     * @param {string} id
     *  The id of the service.
     *
     * @return {Object} The db service data.
     */
    async getService({ id }) {

        const { Service } = this.server.models();

        const service = await Service.query().findById(id);

        if (!service) {
            throw new NotFoundError('Service not found');
        }

        return { service };
    }
    /**
 * Get services by names.
 *
 * This function fetches services from the database where the service name
 * matches any of the provided names.
 *
 * @param {array} names
 *  The array of service names.
 *
 * @return {Object} services
 *  The db services data matching the given names.
 */
    async getServicesByName({ names }) {

        const { Service } = this.server.models();

        const query = Service.query();

        if (names && names.length > 0) {
            query.whereIn('name', names);
        }

        const services = await query;
        return { services };
    }

    /**
     * Create a new service (in the db) given its data
     *
     * @param {Object} name
     *  The name of the service
     *
     * @param {number} purpose_id
     *  The purpose related to the service
     *
     * @param {Object} savedService
     *  The created service.
     */
    async createService({ name, purpose_id }) {

        const { Service } = this.server.models();

        const savedService = await Service.query().insertAndFetch({
            name,
            purpose_id
        });

        return { savedService };
    }

    /**
     * Delete a service (from the db) given its id.
     *
     * @param {string} id
     *  The id of the service.
     *
     * @return {Object} The db deleted service data.
     */
    async deleteService({ id }) {

        const { Service, Package } = this.server.models();
        const { storageService } = this.server.services();
        const {
            amazon: { packages_pictures_bucket_name: bucket }
        } = this.server.settings.app;
        const { server } = this;

        const logContext = {
            context: 'ServiceService.deleteService'
        };

        const packages = await Package.query().where('service_id', id);

        if (packages.length > 0) {
            throw new ValidationError('Service can not be deleted due to conflict with existing packages');
        }

        const deletedService = await Service.query().delete().findById(id).returning('*');

        if (!deletedService) {
            throw new NotFoundError('Service not found');
        }

        //delete image from S3
        if (deletedService.num_pictures > 0) {

            const buildFilepath = (i) => `service/${id}/${i + 1}`;

            const picturesToDelete = [];
            for (let i = 0; i < deletedService.num_pictures; ++i) {
                picturesToDelete.push(buildFilepath(i));
            }

            await storageService.deleteObjects({
                bucket,
                filepaths: picturesToDelete
            }).catch((err) => server.logger.warn({ ...logContext, picturesToDelete, err }, 'Unable to delete extra pictures'));
        }

        return { deletedService };
    }

    /**
     * Patch service (in the db) given id and data
     *
     * @param {number} id
     *  The id of the service.
     *
     * @param {Object} newValues
     *  The data to be updated
     *
     * @return {Object} service
     *  The updated service data.
     */
    async updateService({ id, newValues }) {

        const { Service } = this.server.models();

        await this.getService({ id });
        const service = await Service.query().patchAndFetchById(id, newValues);

        return { updatedService: service };
    }

    /**
     * Reassign packages (in the db) given starting service id and target service id
     *
     * @param {number} from_id
     *  The id of the starting service.
     *
     * @param {Object} to_id
     *  The id of the target service.
     */
    async reassignPackagesToService({ from_id, to_id }) {

        const { Package } = this.server.models();

        await Package.query()
            .patch({ service_id: to_id })
            .where('service_id', from_id);
    }
};
