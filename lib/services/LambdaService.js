'use strict';

const Schmervice = require('schmervice');
const AWS  = require('aws-sdk');

module.exports = class LambdaService extends Schmervice.Service {

    /**
     * This function initializes the AWS Lambda client.
     *
     */
    initialize() {

        this.lambda = new AWS.Lambda({
            accessKeyId: this.server.settings.app.amazon.aws_id,
            secretAccessKey: this.server.settings.app.amazon.aws_secret,
            region: this.server.settings.app.amazon.aws_region
        });
    }

    /**
     * Invoke a specific Lambda function.
     *
     * @param {string} functionName
     *  The name of the Lambda function.
     * @param {object} payload
     *  The event payload to send to the Lambda function.
     * @param {string} invocationType
     *  Either "RequestResponse" (default, synchronous) or "Event" (asynchronous).
     *
     * @returns {object}
     *  The Lambda function response.
     */
    async invokeFunction({ functionName, payload, invocationType = 'RequestResponse' }) {

        const params = {
            FunctionName: functionName,
            InvocationType: invocationType,
            Payload: JSON.stringify(payload)
        };

        const response = await this.lambda.invoke(params).promise();

        // If the invocation is synchronous, the payload is available
        if (invocationType === 'RequestResponse') {
            return JSON.parse(response.Payload);
        }
    }

    // Add any other methods as needed
};
