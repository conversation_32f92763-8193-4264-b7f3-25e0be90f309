'use strict';

const Schmervice = require('schmervice');

module.exports = class ReportService extends Schmervice.Service {

    /**
     * Create new report
     *
     * @return report which has just been created
     * The element which has just been created
     */
    async getExistingOrCreateReport() {

        const { Report } = this.server.models();

        const date = new Date().toISOString().slice(0, 10);
        const report = await Report.query().where('date', date).first();
        if (report) {
            return report;
        }

        return await Report.query().insertAndFetch({ date });
    }
};
