'use strict';

const Knexfile = require('../../knexfile.js');

const knex = require('knex')(Knexfile);
const { raw } = require('objection');
const Schmervice = require('schmervice');
const { InternalError, ValidationError } = require('../helpers/errors');

module.exports = class ReportUserService extends Schmervice.Service {

    /**
    * Create a new service (in the db) given its data
    *
    * @param {report_id}
    *
    * @param {shoot_id}
    *
    * @param {performer_user_id}
    *
    * @param {target_user_id}
    *
    * @param {target_photographer_user_id}
    *
    * @param {starting_status}
    *
    * @param {target_status}
    *
    * @returns ReportUser with all created report
    */
    async createReportUser( { shoot, performer_user_id, target_photographer_id,
        starting_status, target_status } ) {

        const { ReportUser } = this.server.models();
        const { userService, orderService, reportService } = this.server.services();

        try {
            const { user: photographer } = await userService.getPhotographerById({ photographer_id: target_photographer_id })
                .catch((_) => ({ user: null }));

            const target_client_id = await orderService.getClientByOrderId( { order_id: shoot.order_id });
            const { user: { id: target_client_user_id } } = await userService.getClientById({ client_id: target_client_id });

            const report = await reportService.getExistingOrCreateReport();

            await ReportUser.query().insertAndFetch(
                {
                    report_id: report.id,
                    shoot_id: shoot.id,
                    performer_user_id,
                    target_client_user_id,
                    target_photographer_user_id: photographer && photographer.id,
                    starting_status,
                    target_status
                }
            );
        }
        catch (err) {
            this.server.logger.warn({
                context: 'ReportUserService.createReportUser',
                shoot,
                performer_user_id,
                target_photographer_id,
                starting_status,
                target_status,
                err
            }, 'Unexpected error while creating user report');
        }
    }

    /**
    * Return the list of user-related reports (i.e., for each user, how many shoots have been
    * confirmed/canceled/completed/...), for a given time interval.
    *
    * @param {object} time
    *  The time interval, in the format `{ from, to }`.
    * @param {string} userType
    *  The type of users to fetch reports for (either 'client' or 'photographer').
    * @param {int} limit
    *  The number of reports to fetch (default: 20). Use for pagination.
    * @param {int} offset
    *  The number of reports to skip (default: 0). Use for pagination.
    * @param {string} sortBy
    *  The field to sort the reports by (supports all aggregated ones). Optional.
    * @param {boolean} sortDescending
    *  Whether to sort in descending order or not (default: true).
    * @param {object} filters
    *  An object containing key-value pairs used to filter the shoots.
    *  Supports both exact matches (`id`, `photographer_id`, `client_id`),
    *  and wildcard prefix/suffixed (`name`).
    *
    * @returns {array} the list of reports
    */
    async getReportUserInTimeRange({ time, userType, limit = 20, offset = 0, sortBy, sortDescending = true, filters = {} }) {

        const { Report, ReportUser, Shoot, User } = this.server.models();
        const { from, to } = time;
        const { statuses } = Shoot;
        const { logger } = this.server;

        let cloneResult;
        let totalCountResult = {};

        if (!['client', 'photographer'].includes(userType)) {
            throw new ValidationError(`Invalid user type ${userType}`);
        }

        const reports = await Report.query().select('id').whereBetween('date', [from, to]);
        const reportIds = reports.map((r) => r.id);

        const targetUserColumn = `target_${userType}_user_id`;

        const reportUsersQuery = ReportUser.query()
            .select(targetUserColumn)
            .whereIn('report_id', reportIds)
            .whereNotNull(targetUserColumn)
            .joinRelated('shoot')
            .join('users', targetUserColumn, '=', 'users.id' )
            .join(`${userType}s`, `users.${userType}_id`, '=', `${userType}s.id`)
            .groupBy(targetUserColumn);

        const childReportUsersQuery = ReportUser.query()
            .select('parent_client_id', 'parent_joined.id as user_id_ulti')
            .whereIn('report_id', reportIds)
            .whereNotNull(targetUserColumn)
            .joinRelated('shoot')
            //join to take data for children
            .join('users', targetUserColumn, '=', 'users.id' )
            .join('clients', `users.${userType}_id`, '=', `${userType}s.id`)
            //useful for taking parent id
            .join('users as parent_joined', 'parent_client_id', '=', 'parent_joined.client_id')
            .groupBy('parent_client_id', 'parent_joined.id');

        const buildTotalColumn = ({ query }) => {

            for (const column of sortableColumns) {
                query.select(knex.raw(`COALESCE(${column},0) + COALESCE(child_${column},0) as tot_${column}`));
            }
        };

        const sortableColumns = new Set();

        const buildCase = ({ filters = [], column = 'id' }) => {

            if (Object.keys(filters).length === 0) {
                throw new InternalError('Needs at least one filter to build an SQL case');
            }

            const clauses = Object.entries(filters).map(([column, value]) => `${column} IN (${value})`).join(' and ');
            return `case when ${clauses} then ${column} end`;
        };

        const countShoots = ({ query, filters, alias }) => {

            const countCase = buildCase({ filters, column: 'shoot_id' });

            // NOTE: the `distict` makes it so that if a shoot does the same transition twice in the same day it's still counted as a single one
            query.select(raw(`count(distinct ${countCase}) as ${alias}_count`));

            if (!alias.startsWith('child')) {
                sortableColumns.add(`${alias}_count`);
            }
        };

        const sumShootsRevenue = ({ query, filters, alias }) => {

            const revenueColumn = userType === 'photographer' ? 'photographer_revenue' : 'price';
            const sumCase = buildCase({ filters, column: revenueColumn });

            // TODO: find a smart way to sum duplicated shoots only once
            query.select(raw(`sum(${sumCase}) as ${alias}_revenue`));

            if (!alias.startsWith('child')) {
                sortableColumns.add(`${alias}_revenue`);
            }

        };

        const countAndSumShoots = ({ query, filters, alias }) => {

            countShoots({ query, filters, alias });
            sumShootsRevenue({ query, filters, alias });
        };

        if (userType === 'photographer') {
            // TOTAL CONFIRMED
            countAndSumShoots({
                query: reportUsersQuery,
                filters: {
                    starting_status: `'${statuses.photographerAssigned}'`,
                    target_status: `'${statuses.confirmed}'`,
                    performer_user_id: targetUserColumn
                },
                alias: 'confirmed'
            });

            // TOTAL DECLINED
            countAndSumShoots({
                query: reportUsersQuery,
                filters: {
                    starting_status: `'${statuses.photographerAssigned}'`,
                    target_status: `'${statuses.scheduled}'`,
                    performer_user_id: targetUserColumn
                },
                alias: 'declined'
            });

            // TOTAL CANCELED
            countAndSumShoots({
                query: reportUsersQuery,
                filters: {
                    target_status: `'${statuses.canceled}'`
                },
                alias: 'canceled'
            });

            // TOTAL UPLOADED
            countAndSumShoots({
                query: reportUsersQuery,
                filters: {
                    starting_status: `'${statuses.confirmed}'`,
                    target_status: `'${statuses.photosUploaded}'`,
                    performer_user_id: targetUserColumn
                },
                alias: 'uploaded'
            });

            // TOTAL COMPLETED
            countAndSumShoots({
                query: reportUsersQuery,
                filters: {
                    target_status: `'${statuses.completed}', '${statuses.photosReady}'`
                },
                alias: 'completed'
            });
        }

        if (userType === 'client') {
            // TOTAL CREATED  childReportUsersQuery
            countAndSumShoots({
                query: reportUsersQuery,
                filters: {
                    starting_status: `'${statuses.notCreated}'`
                },
                alias: 'created'
            });

            countAndSumShoots({
                query: childReportUsersQuery,
                filters: {
                    starting_status: `'${statuses.notCreated}'`
                },
                alias: 'child_created'
            });

            // TOTAL CANCELED
            countAndSumShoots({
                query: reportUsersQuery,
                filters: {
                    target_status: `'${statuses.canceled}'`
                },
                alias: 'canceled'
            });

            countAndSumShoots({
                query: childReportUsersQuery,
                filters: {
                    target_status: `'${statuses.canceled}'`
                },
                alias: 'child_canceled'
            });

            // TOTAL COMPLETED
            countAndSumShoots({
                query: reportUsersQuery,
                filters: {
                    starting_status: `'${statuses.photosReady}'`,
                    target_status: `'${statuses.completed}'`
                },
                alias: 'completed'
            });

            countAndSumShoots({
                query: childReportUsersQuery,
                filters: {
                    starting_status: `'${statuses.photosReady}'`,
                    target_status: `'${statuses.completed}'`
                },
                alias: 'child_completed'
            });

            const singleChildReportUsersQuery = reportUsersQuery.clone();

            //if client is searched we need to divide in parent and client
            if (userType === 'client') {
                reportUsersQuery.whereNull('parent_client_id');
                childReportUsersQuery.whereNotNull('parent_client_id');
                singleChildReportUsersQuery.whereNotNull('parent_client_id');
            }

            const totalCountQuery =  knex
                .select('*')
                .from( reportUsersQuery.as('parent_table').toKnexQuery())
                .fullOuterJoin(
                    childReportUsersQuery.as('child_table').toKnexQuery()
                    , function () {

                        this.on(`parent_table.${targetUserColumn}`, '=', 'user_id_ulti');
                    }).limit(limit).offset(offset);

            buildTotalColumn({ query: totalCountQuery });

            totalCountResult = await totalCountQuery;

            singleChildReportUsersQuery
                .whereIn('parent_client_id', totalCountResult.map((value) => value.parent_client_id))
                .select('parent_client_id')
                .groupBy('parent_client_id');
            cloneResult = await singleChildReportUsersQuery;

        }

        // Sorting
        if (sortBy && !sortableColumns.has(sortBy)) {
            throw new ValidationError(`Sorting by ${sortBy} is not supported`);
        }

        //TODO cambiare su totali!! --> va cambiato un attimo perche con la fullOuterJoin
        //mi trovo comunque roba che è solo a destra e non dovrbbe esserci. andrebbe fatto dopo
        if (sortBy) {
            reportUsersQuery.orderBy(sortBy, sortDescending ? 'DESC' : 'ASC');
        }

        // Filtering
        if (Object.keys(filters).length > 0) {
            const supportedExactFilters = ['id', 'client_id', 'photographer_id'];
            const supportedLikeFilters = ['name'];
            const userTableRelationName = `target_${userType}_user`;
            reportUsersQuery.joinRelated(userTableRelationName);

            for (const column of supportedExactFilters) {
                if (filters[column]) {
                    reportUsersQuery.where(`${userTableRelationName}.${column}`, filters[column]);
                }
            }

            for (const column of supportedLikeFilters) {
                if (filters[column]) {
                    reportUsersQuery.where(`${userTableRelationName}.${column}`, 'like', `%${filters[column]}%`);
                }
            }
        }

        const results = userType === 'client' ? await totalCountResult : await reportUsersQuery;

        const tryParseNumber = (n) => {

            const parsed = Number.parseFloat(n || 0);

            if (Number.isNaN(parsed)) {
                return n;
            }

            return parsed;
        };

        //retrieved user data and mapping
        const userData = await User.query().whereNotNull(`${userType}_id`).whereIn('id', results.map((item) => item[targetUserColumn]));
        const userIdToData = Object.fromEntries(userData.map((user) => [user.id, user]));

        return results.map((el) => Object.fromEntries(Object.entries(el).map(([key, value]) => [key, tryParseNumber(value)]))).map((item) => {

            const user = userIdToData[item[targetUserColumn]];

            if (!user) {
                logger.warn({ item }, 'Unable to map extra data for user in user reports');
                return null;
            }

            const totKeys = Object.keys(item).filter((key) => key.startsWith('tot_'));
            let tot_item = {};

            for (const key of totKeys) {
                tot_item[key.substring(4, key.length)] = item[key];
            }

            tot_item[targetUserColumn] = item[targetUserColumn];

            const children = [];

            if ( userType === 'client') {
                for (const a of cloneResult) {

                    if (a.parent_client_id === user[`${userType}_id`]) {
                        children.push(a);
                    }
                }
            }
            else {
                tot_item = item;
            }

            return {
                ...tot_item,
                user_id: user.id,
                user_name: user.name,
                [`user_${userType}_id`]: user[`${userType}_id`],
                ...(userType === 'client' && { subclients: children })
            };
        }).filter((el) => el !== null);

    }

    /**
     * Assigns subclients reports  to a provided main client
     *
     * @param {Object} affectedUser the main client
     * @param {Array} subclientsUserIds the array of subclient user ids
     * @returns {boolean} result of operation
     */
    async assignSubClientsReportUserToMainClient({ affectedUser, subclientsUserIds }) {

        const { ReportUser } = this.server.models();
        try {

            const reports = await ReportUser.query()
                .whereIn('target_client_user_id', subclientsUserIds);

            const reportsIds = reports.map(({ id }) => id);

            await ReportUser.query()
                .whereIn('id', reportsIds)
                .patch({
                    target_client_user_id: affectedUser.id,
                    performer_user_id: affectedUser.id
                });
        }
        catch (error) {
            this.server.logger.warn({ context: 'ReportUserService.assignSubClientsReportUserToMainClient', error });
            return false;
        }

        return true;

    }
};
