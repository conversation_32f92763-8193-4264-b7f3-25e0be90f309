'use strict';

const Schmervice = require('schmervice');
const { types } = require('../services/NotificationService.js');

module.exports = class AlgorithmService extends Schmervice.Service {

    /**
     * Get shoots to be processed by photographer matching algorithm.
     *
     * @return {Object} shoots
     *  The shoots data.
     */
    async getUnprocessedShoots() {

        const { Shoot, PhotographerShoot } = this.server.models();

        const {
            stale_matched_photographer_interval_in_minutes,
            max_number_shoot_processed
        } = this.server.settings.app.runner.algorithm;

        let shoots = await Shoot.query()
            .whereNull('photographer_id')
            .where('status', Shoot.statuses.scheduled)
            .where('datetime', '>', new Date());

        const shootsIds = shoots.map(({ id }) => id);
        const ph_shoots = await PhotographerShoot.query()
            .whereIn('shoot_id', shootsIds)
            .where('creation_date', '>=', new Date(Date.now() -  (stale_matched_photographer_interval_in_minutes * 60 * 1000)));

        const ph_shoots_unavailable = ph_shoots.map(({ shoot_id }) => shoot_id);
        shoots = shoots.filter(({ id }) => !ph_shoots_unavailable.includes(id));
        shoots.length = Math.min(shoots.length, max_number_shoot_processed);
        return shoots;
    }
    /**
     * Get photographer ids that matches a defined shoot.
     *
     * @param {number} shoot
     *  The shoot to be matched
     *
     * @return {Array} sortedPhotographersIdsByDescRate
     *  The array of matched photographers ids sorted by average rate.
     */
    async getMatchedPhotographers({ shoot }) {

        const { Shoot, Photographer, ShootService, PhotographerService, PhotographerShoot } = this.server.models();
        const { shootService } = this.server.services();

        const {
            photographer_rest_between_shoots_in_minutes: photographerIdleMinutes
        } = this.server.settings.app.photographers;

        //retrieving shoot with array of services ids
        const services = await ShootService.query().where('shoot_id', shoot.id);

        const shootServices = services.map(({ service_id }) => service_id).filter((el) => el);

        //adding flag if the all packages are plus (to be used in query with ph is_pro)
        const isProShoot = services.map(({ package: pack }) => pack?.is_plus || null).every((c) => c === true);

        //retrieving previously matched photographers ids
        const prevMatchedPhotographers = await PhotographerShoot.query().where('shoot_id', shoot.id);
        const prevMatchedPhotographersIds = prevMatchedPhotographers.map(({ photographer_id }) => photographer_id).filter((el) => el);

        //filtering on location for photographer and on is_pro feature
        const matchedLocationAndProPhotographers = await Photographer.query()
            .joinRelated('user')
            .where('user.status', 'active')
            .where('main_location', shoot.address.city || '')
            .where('is_pro', isProShoot)
            .whereNotIn('photographers.id', prevMatchedPhotographersIds)
            .select('photographers.*');

        const matchedLocationAndProPhotographersIds = matchedLocationAndProPhotographers.map(({ id }) => id);

        //returning all the photographers busy in interval of time of the shoot to be matched.
        const unavailabilityChecker = await shootService.getOverlappingShoots({ shoot, photographerIdleMinutes })
            .where('status', Shoot.statuses.confirmed);

        const unavailablePhotographersIds  = new Set(unavailabilityChecker.map(({ photographer_id }) => photographer_id).filter((el) => el));

        //merging conditions on location, pro and availability
        const matchedLocationProAndAvailablePhotographersIds = matchedLocationAndProPhotographersIds
            .filter((id) => !unavailablePhotographersIds.has(id));

        //querying on matched photographer that have the required skill on shoot services
        const matchedPhotographersServices = await PhotographerService.query()
            .whereIn('photographer_id', matchedLocationProAndAvailablePhotographersIds)
            .whereIn('service_id', shootServices);

        //formatting query result to be used grouped by photographer
        // matchedPhotographersServices = [
        //     {
        //         photographer_id: 1,
        //         service_id: 1,
        //         rate: 1
        //     },
        //     {
        //         photographer_id: 1,
        //         service_id: 2,
        //         rate: 3
        //     },
        //     {
        //         photographer_id: 2,
        //         service_id: 1,
        //         rate: 4
        //     }
        // ];
        //
        // photographerToServices = [
        //     1: [
        //         {service_id:1, rate:1},
        //         {service_id:2, rate:3}
        //     ],
        //     2: [
        //         {service_id:1, rate:4}
        //     ]
        // ]

        const photographerToServices = matchedPhotographersServices
            .reduce((accumulator, { photographer_id, service_id, rate }) => ({
                ...accumulator,
                [photographer_id]: [
                    ...(accumulator[photographer_id] || []),
                    { service_id, rate }
                ] }),
            {});

        //calculating average rate on shoot services for each photographer (excluding the ones that do not cover all the required services)
        const photographerToAverageRate = Object.entries(photographerToServices)
            .filter(([_, servicesData]) => servicesData.length >= shootServices.length)
            .map(([photographer_id, servicesData]) => ({
                photographer_id,
                avgRate: servicesData.reduce((acc, { rate }) => (acc + rate), 0) / servicesData.length
            }));

        //sorting photographers by average rate in descending order
        photographerToAverageRate.sort((a, b) => parseFloat(b.avgRate) - parseFloat(a.avgRate));

        //transforming previous object in array of ids
        const sortedPhotographersIdsByDescRate = photographerToAverageRate.map(({ photographer_id }) => photographer_id).filter((el) => el);

        return sortedPhotographersIdsByDescRate;
    }

    /**
     * Add matched photographers for a defined shoot to db
     *
     * @param {number} shootId
     *  The id of the shoot to be matched
     *
     * @param {Array} matchedPhotographerIds
     *  The array of matched photographers ids sorted by average rate.
     *
     */
    async addMatchedPhotographersToShoot({ shoot_id, matchedPhotographerIds }) {

        const { PhotographerShoot } = this.server.models();

        const { matchedPhotographersLimit } = this.server.settings.app.runner.algorithm;

        const matches = await PhotographerShoot.query().insert(
            matchedPhotographerIds
                .slice(0, matchedPhotographersLimit)
                .map((photographer_id) => ({
                    photographer_id,
                    shoot_id
                })));

        return matches;
    }

    /**
     * Notify matched photographers
     *
     * @param {Array} matches
     *  The array that contains PhotographerShoot entities
     *
     */
    async notifyMatchedPhotographers({ matches }) {

        const { User } = this.server.models();

        const { notificationService } = this.server.services();

        const photographerIds = matches.map(({ photographer_id }) => photographer_id);

        const photographers = await User.query().whereIn('photographer_id', photographerIds);
        for (const target of photographers) {
            notificationService.sendNotification({ type: types.phShootAssigned, target, metaData: {} });
        }
    }
};
