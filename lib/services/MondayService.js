'use strict';

const Schmervice = require('schmervice');
const axios = require('axios').default;
const { InternalError } = require('../helpers/errors');
const { formattedDateTime, stringifyJsonForGraphQL } = require('../helpers/utils');
const Shoot = require('../models/Shoot');

module.exports = class MondayService extends Schmervice.Service {

    /**
     * This function initializes Monday variables.
     *
     */
    initialize() {

        const {
            api_key,
            bespoke_board_id,
            flashyshoots_board_id,
            clients_board_id,
            columns_id,
            clients_columns_id,
            groups_id,
            clients_groups_id,
            endpoint
        } = this.server.settings.app.monday;

        const { NODE_ENV } = process.env;
        Object.assign(this, {
            api_key,
            bespoke_board_id,
            flashyshoots_board_id,
            clients_board_id,
            columns_id,
            clients_columns_id,
            groups_id,
            clients_groups_id,
            endpoint,
            NODE_ENV
        });

    }

    /**
     * This function creates the request structure needed by Monday.
     *
     * @param {Object} query
     *  The query to be sent to Monday.
     *
     * @returns {Object}
     *  The request structure needed by Monday.
     */
    populateRequestPayload({ query }) {

        return {
            method: 'post',
            url: this.endpoint,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': this.api_key
            },
            data: {
                query
            }
        };
    }

    /**
     *
     * @param {Object} shoot
     *  The shoot object to be converted.
     *
     * @param {Boolean} is_creation
     *  Whether the shoot is being created or not.
     *
     * @returns {Object} column_values
     *  The column values to be sent to Monday.
     */
    async getFixedColumnValuesForShoot({ shoot_id, is_creation = true }) {

        const { shootService, serviceService, orderService, shootStatusService } = this.server.services();
        const { shoot } = await shootService.getShoot({ id: shoot_id, user: {}, role: 'admin' });
        const { service } = await serviceService.getService({ id: shoot.services[0].service_id });
        const { order } = await orderService.getOrder({ id: shoot.order_id, user: {}, role: 'admin' });
        const { status } = await shootStatusService.updatePayedShootStatus({ shoot, user: {}, role: 'admin', forecast: true });

        let formattedDate;
        let time;
        if (shoot.datetime) {
            const { date, fromTime, toTime } = formattedDateTime({ duration: shoot.duration, fromDate: shoot.datetime.toISOString() });
            formattedDate = date;
            time = `${ fromTime } - ${ toTime}`;
        }

        const column_values = {
            [this.columns_id['Shoot Id']]: (shoot.id).toString(),
            [this.columns_id.Category]: { label: service.name },
            [this.columns_id['Client Name']]: shoot.customer_name,
            [this.columns_id.Quantity]: shoot.picture_number,
            [this.columns_id.Price]: shoot.price,
            [this.columns_id['Client ID']]: (order.client_id).toString(),
            [this.columns_id['Photographer Take']]: shoot.photographer_revenue,
            [this.columns_id['Photographer name']]: shoot.photographer_name,
            [this.columns_id['Photographer ID']]: (shoot.photographer_id) ? (shoot.photographer_id).toString() : null,
            [this.columns_id['Shoot notes']]: shoot.notes,
            [this.columns_id['Online Payments']]: (!shoot.is_payed) ? { checked: 'true' } : {},
            [this.columns_id.Redeemable]: (shoot.redeemable_shoot_id) ? { checked: 'true' } : {},
            [this.columns_id.Status]: (shoot.status === Shoot.statuses.toBePayed) ? status : shoot.status,
            [this.columns_id['Shoot Date']]: { date: formattedDate },
            [this.columns_id.Package]: (shoot.type === 'custom') ? 'custom' : shoot.services[0].package.name,
            [this.columns_id['Shoot Location']]: shoot.address.formatted_address,
            [this.columns_id.Time]: time,
            [this.columns_id['Outlet Code']]: shoot.outlet_code,
            [this.columns_id['Outlet Name']]: shoot.outlet_name,
            [this.columns_id['POC Name']]: shoot.poc_name,
            [this.columns_id['POC Email']]: shoot.poc_email,
            [this.columns_id['POC Phone']]: shoot.poc_phone,
            [this.columns_id['Video No']]: shoot.video_number,
            [this.columns_id['Video Duration']]: shoot.video_duration,
            [this.columns_id['Service Provider']]: shoot.content === 'videography' ? 'Videographer' : 'Photographer',
            ...(is_creation && { [this.columns_id.City]: { label: 'null' } })
        };

        return {
            column_values: stringifyJsonForGraphQL({ object: column_values }),
            shoot
        };
    }
    /**
     * Creates a shoot in Monday.
     *
     * @param {Object} shoot
     *  The id of the shoot to be sent to Monday.
     *
     * @returns {string} id
     *  Monday item id.
     */
    async createShoot({ shoot_id, boardType = 'BESPOKE' }) {

        try {
            //do nothing if in test mode
            if (this.NODE_ENV === 'test' || this.NODE_ENV === 'development') {
                return;
            }

            const { column_values, shoot } = await this.getFixedColumnValuesForShoot({ shoot_id });
            const query = `mutation { 
                create_item (
                    board_id: ${boardType === 'BESPOKE' ? this.bespoke_board_id : this.flashyshoots_board_id},
                    group_id: "${String(this.groups_id.scheduled)}", 
                    item_name: "${shoot.customer_name} - ${shoot.name}",
                    column_values: ${column_values},
                    create_labels_if_missing: true
                ) {id}
            }`;

            const id = await axios(this.populateRequestPayload({ query }))
                .then((response) => {

                    return response.data.data.create_item.id;
                }).catch((error) => {

                    throw new InternalError(`Error from MondayService.createShoot() : ${error}`);
                });
            return id;
        }
        catch (error) {
            // throw new InternalError(`Error from MondayService.createShoot() : ${error}`);
            console.error(`Error from MondayService.createShoot() : ${error}`);
        }
    }

    /**
 * Retrieve a monday item id by using (db) shoot ids.
 *
 * @param {Array} shoot_ids
 *  The shoot ids to be queried.
 * @param {String} boardType
 *  The type of board to query from.
 *
 * @returns {Array} item_ids
 *  Monday array of item id.
 */
    async getItemsIdByShootId({ shoot_ids, boardType }) {

        try {
        // Prepare the columns condition according to the new query format
            const columnsCondition = shoot_ids.map((id) => ({
                column_id: this.columns_id['Shoot Id'],
                column_values: String(id)
            }));

            // Update the query to match the new items_page_by_column_values format
            const query = `query {
            items_page_by_column_values(
                board_id: ${boardType === 'BESPOKE' ? String(this.bespoke_board_id) : String(this.flashyshoots_board_id)},
                columns: ${JSON.stringify(columnsCondition).replace(/"([^"]+)":/g, '$1:')}
            ) {
                items {
                    id
                }
            }
        }`;

            const response = await axios(this.populateRequestPayload({ query }));
            const item_ids = response.data.data.items_page_by_column_values.items.map((item) => item.id);
            return item_ids;
        }
        catch (error) {
            // throw new InternalError(`Error from MondayService.getItemsIdByShootId() : ${error}`);
            console.error(`Error from MondayService.getItemsIdByShootId() : ${error}`);
        }
    }

    /**
     * Cancel item in Monday by shifting it into canceled group.
     *
     * @param {integer} shoot_id
     *  The shoot id to be canceled.
     *
     * @returns {string} id
     *  Monday item id.
     */
    async cancelShoot({ shoot_id, boardType = 'BESPOKE' }) {

        try {
            //do nothing if in test mode
            if (this.NODE_ENV === 'test' || this.NODE_ENV === 'development') {
                return;
            }

            const id = (await this.getItemsIdByShootId({ shoot_ids: [shoot_id], boardType }))[0] || { id: null };

            if (!id) {
                throw new InternalError('There was an error while getting the item.');
            }

            const query = `mutation {
            move_item_to_group (
                item_id: ${id}, 
                group_id: "${String(this.groups_id.canceled)}" 
            ) {id}
        }`;

            const returnedId = await axios(this.populateRequestPayload({ query }))
                .then((response) => {

                    return response.data.data.move_item_to_group.id;
                }).catch((error) => {

                    throw new InternalError(`Error from MondayService.cancelShoot() : ${error}`);
                });

            return returnedId;
        }
        catch (error) {
            // throw new InternalError(`Error from MondayService.cancelShoot() : ${error}`);
            console.error(`Error from MondayService.cancelShoot() : ${error}`);
        }
    }

    /**
     * Updates an item in Monday.
     *
     * @param {Object} shoot_id
     *  The id of the shoot to be sent to Monday.
     *
     * @returns {string} id
     *  Monday item id.
     */
    async updateShoot({ shoot_id, boardType = 'BESPOKE' }) {

        try {
            //do nothing if in test mode
            if (this.NODE_ENV === 'test' || this.NODE_ENV === 'development') {
                return;
            }

            const { column_values, shoot } = await this.getFixedColumnValuesForShoot({ shoot_id, is_creation: false });
            const id = (await this.getItemsIdByShootId({ shoot_ids: [shoot.id], boardType }))[0] || { id: null };

            if (!id) {
                throw new InternalError('There was an error while getting the item.');
            }

            const query = `mutation { 
            change_multiple_column_values(
                item_id: ${id},
                    board_id: ${boardType === 'BESPOKE' ? this.bespoke_board_id : this.flashyshoots_board_id},
                column_values: ${column_values},
                create_labels_if_missing: true
                ) {id}
            }`;

            const  returnedId  = await axios(this.populateRequestPayload({ query }))
                .then((response) => {

                    return response.data.data.change_multiple_column_values.id;
                }).catch((error) => {

                    throw new InternalError(`Error from MondayService.updateShoot() : ${error}`);
                });

            return returnedId;
        }
        catch (error) {
            // throw new InternalError(`Error from MondayService.updateShoot() : ${error}`);
            console.error(`Error from MondayService.updateShoot() : ${error}`);
        }
    }

    /**
     * @param {Array} shoot_ids
     * The ids of the shoots to be deleted from Monday.
     *
     */
    async deleteShoots({ shoot_ids }) {

        try {
            const item_ids = await this.getItemsIdByShootId({ shoot_ids });

            for (const { id } of item_ids) {

                const query = `mutation {
                    delete_item (item_id: ${id}) {id}
                }`;

                await axios(this.populateRequestPayload({ query }))
                    .then((response) => {

                        return response.data.data.change_multiple_column_values.id;
                    }).catch((error) => {

                        throw new InternalError(`Error from MondayService.deleteShoots() : ${error}`);
                    });
            }
        }
        catch (error) {
            // throw new InternalError(`Error from MondayService.deleteShoots() : ${error}`);
            console.error(`Error from MondayService.deleteShoots() : ${error}`);
        }
    }

    /* B2C funnel */
    getFixedColumnValuesForClient(client) {
        // Assuming 'client' is an object with all required properties
        // Mapping client properties to the Monday.com column IDs

        const column_values = {
            [this.clients_columns_id.Name]: client.firstName || '',
            [this.clients_columns_id.Surname]: client.lastName || '',
            [this.clients_columns_id.Email]: client.email || '',
            [this.clients_columns_id.Phone]: client.phone || '',
            [this.clients_columns_id.City]: client.city || '',
            [this.clients_columns_id.Status]: client.status || '',
            [this.clients_columns_id['Campaign Source']]: client.campaignSource || '',
            [this.clients_columns_id['Campaign Medium']]: client.campaignMedium || '',
            [this.clients_columns_id['Campaign Name']]: client.campaignName || '',
            [this.clients_columns_id['Client ID']]: client.id ? client.id.toString() : '',
            [this.clients_columns_id['Order Count']]: client.orderCount ? client.orderCount.toString() : '',
            [this.clients_columns_id['Order Date']]: client.orderDate ? client.orderDate : ''
        };

        return {
            column_values: stringifyJsonForGraphQL({ object: column_values })
        };
    }
    /**
 * Retrieve a Monday item by using a client id.
 *
 * @param {integer} client_id
 *  The client id to be queried.
 *
 * @returns {Object} clientItem
 *  The Monday item of the client.
 */
    async getClientById(client_id) {

        try {
            // Adjusted query according to the documentation
            const query = `query {
                items_page_by_column_values (
                    board_id: ${this.clients_board_id},
                    limit: 1,
                    columns: [
                        {
                            column_id: "${this.clients_columns_id['Client ID']}", 
                            column_values: ["${client_id}"]
                        }
                    ]
                ) {
                    items {
                        id
                        name
                        column_values {
                            id
                            value
                        }
                    }
                }
            }`;

            // Using axios to send the query in the request payload
            const response = await axios(this.populateRequestPayload({ query }));
            const { items } = response.data.data.items_page_by_column_values;

            // Since we are querying by a unique client ID, we expect at most one item
            // Check if we have at least one item returned
            return items.length > 0 ? items[0] : null;
        }
        catch (error) {
            // It's a good practice to log the error or handle it as per your application's error handling policy
            // throw new Error(`Error from MondayService.getClientById(): ${error}`);
            console.error(`Error from MondayService.getClientById(): ${error}`);
        }
    }

    /**
 * Creates a client in Monday.
 *
 * @param {Object} client
 *  The client object to be sent to Monday.
 *
 * @returns {string} id
 *  Monday item id.
 */
    async createClient(client, type = 'COMPLETED') {

        try {
        // Do nothing if in test mode
            if (this.NODE_ENV === 'test' || this.NODE_ENV === 'development') {
                return;
            }

            // Set default values for missing data
            client.firstName = client.firstName || 'Unknown';
            client.lastName = client.lastName || 'Unknown';
            client.email = client.email || '';
            client.phone = client.phone || '';
            client.city = client.city || '';
            client.campaignName = client.campaignName || '';
            client.campaignMedium = client.campaignMedium || '';
            client.campaignSource = client.campaignSource || '';

            const { column_values } = await this.getFixedColumnValuesForClient(client);
            const groupId = type === 'COMPLETED'
                ? String(this.clients_groups_id.completed)
                : String(this.clients_groups_id.canceled);
            const query = `mutation { 
            create_item (
                board_id: ${this.clients_board_id},
                group_id: "${groupId}", 
                item_name: "${client.firstName} - ${client.lastName}",
                column_values: ${column_values},
                create_labels_if_missing: true
            ) {id}
        }`;

            const id = await axios(this.populateRequestPayload({ query }))
                .then((response) => {

                    return response.data.data.create_item.id;
                }).catch((error) => {

                    throw new InternalError(`Error from MondayService.createClient() : ${error}`);
                });
            return id;
        }
        catch (error) {
            // throw new InternalError(`Error from MondayService.createClient() : ${error}`);
            console.error(`Error from MondayService.createClient() : ${error}`);
        }
    }

    /**
 * Updates a client's date and increments the order count in Monday.
 *
 * @param {Object} client - The client object with at least the id and new date.
 * @param {String} newDate - The new date to set for the client.
 * @returns {string} id - Monday item id of the updated client.
 */
    async updateClient(client_id, newDate) {

        try {
        // Do nothing if in test mode
            if (this.NODE_ENV === 'test' || this.NODE_ENV === 'development') {
                return;
            }

            // First, get the Monday item ID of the client
            const clientItem = await this.getClientById(client_id);
            if (!clientItem) {
                throw new InternalError('There was an error while getting the client item.');
            }

            // Fetch the current order count and increment it
            const currentOrderCount = clientItem.column_values.find((col) => col.id === this.clients_columns_id['Order Count']);
            const updatedOrderCount = (currentOrderCount ? parseInt(currentOrderCount.value) : 0) + 1;

            // Prepare the column values for updating
            const column_values = JSON.stringify({
                [this.clients_columns_id['Order Date']]: newDate,
                [this.clients_columns_id['Order Count']]: updatedOrderCount.toString()
            });

            // GraphQL mutation query
            const query = `mutation {
            change_multiple_column_values(
                item_id: ${clientItem.id},
                board_id: ${this.clients_board_id},
                column_values: ${column_values},
                create_labels_if_missing: true
            ) { id }
        }`;

            // Execute the query
            const returnedId = await axios(this.populateRequestPayload({ query }))

                .then((response) => {

                    return response.data.data.change_multiple_column_values.id;
                }).catch((error) => {

                    throw new InternalError(`Error from MondayService.updateClient() : ${error}`);
                });

            return returnedId;
        }
        catch (error) {
            // throw new InternalError(`Error from MondayService.updateClient() : ${error}`);
            console.error(`Error from MondayService.updateClient() : ${error}`);
        }
    }

};
