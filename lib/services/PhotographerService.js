'use strict';

const Schmervice = require('schmervice');
const { NotFoundError } = require('../helpers/errors');

module.exports = class PhotographerService extends Schmervice.Service {

    /**
     * Get a photographer (from the db) given its id.
     *
     * @param {string} id
     *  The id of the photographer.
     *
     * @return {Object} The db photographer data.
     */
    async getPhotographer({ id }) {

        const { Photographer } = this.server.models();

        const photographer = await Photographer.query().findById(id);

        if (!photographer) {
            throw new NotFoundError('Photographer not found');
        }

        return photographer;
    }
};
