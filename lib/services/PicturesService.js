'use strict';

const Schmervice = require('schmervice');
const StreamZip = require('node-stream-zip');
const Path = require('path');
const Fs = require('fs').promises;
const { NotFoundError, ValidationError } = require('../helpers/errors');

module.exports = class PicturesService extends Schmervice.Service {

    /**
     * Upload a set of (publicly readable) pictures for a given
     * resource (e.g., packages, services).
     *
     * @param {object} zipFile
     *  An object of the form `{ path, bytes }`, which represents the
     *  temporary file created by hapi containing the uploaded zip.
     * @param {string} resourceType
     *  The type of resource the upload is for (currently supported: package, service).
     * @param {int} resourceId
     *  The database id of the resource to perform the upload for.
     */
    async uploadPictures({ zipFile, resourceType, resourceId }) {

        const { Package, Service } = this.server.models();
        const { storageService, stripeService } = this.server.services();
        const {
            amazon: { packages_pictures_bucket_name: bucket },
            uploads: { pictures: { max_per_resource: maxNumPictures, allowed_extensions } }
        } = this.server.settings.app;
        const { server } = this;

        const logContext = {
            context: 'PicturesService.uploadPictures',
            resourceType,
            resourceId
        };

        const Model = {
            'package': Package,
            'service': Service
        }[resourceType];

        try {

            if (!Model) {
                throw new ValidationError('Invalid resource type');
            }

            const resource = await Model.query().findById(resourceId);

            if (!resource) {
                throw new NotFoundError(`Unable to find ${resourceType}`);
            }

            // Unzip
            const zipStream = new StreamZip.async({ file: zipFile.path });

            // Validate number of pictures and extensions
            const entries = await zipStream.entries();
            // iOS system in a zip folder creates a subfolder with image duplicates. We don't need it
            const entriesFilenames = Object.keys(entries).filter((key) => !key.startsWith('__MACOSX/'));
            const numUploadedPictures = entriesFilenames.length;

            if (numUploadedPictures > maxNumPictures) {
                throw new ValidationError(`Cannot upload more than ${maxNumPictures} pictures (uploaded ${numUploadedPictures})`);
            }

            const allowedFileExtensions = new Set(allowed_extensions);
            const fileExtensions = [...new Set(Object.keys(entries).map(Path.extname))];
            const invalidFileExtensions = fileExtensions.filter((ext) => !allowedFileExtensions.has(ext));
            if (invalidFileExtensions.length > 0) {
                throw new ValidationError(`Invalid file extensions (${invalidFileExtensions.join(', ')})`);
            }

            const buildFilepath = (i) => `${resourceType}/${resourceId}/${i + 1}`;

            // Delete old pictures
            const oldPictures = await storageService.listObjects({
                bucket,
                prefix: `${resourceType}/${resourceId}/`
            });

            const oldPaths = await oldPictures.map((img) => img.Key);

            if (oldPictures.length > 0) {
                await storageService.deleteObjects({
                    bucket,
                    filepaths: oldPaths
                }).catch((err) => {

                    throw new Error(`Enable to delete old images ERROR : ${err}`);
                });
            }

            // Load new pictures to S3 (concurrently)
            await Promise.all(entriesFilenames.map(async (entryFilename, i) => {

                return await storageService.uploadObject({
                    bucket,
                    filepath: buildFilepath(i),
                    object: await zipStream.stream(entryFilename),
                    options: {
                        ACL: 'public-read',
                        ContentType: 'image/jpeg',
                        ContentDisposition: 'inline'
                    }
                });
            }));
            await zipStream.close();

            if (resourceType === 'package' && resource.package_type === 'b2c') {
                const images = [];
                // eslint-disable-next-line @hapi/for-loop
                for (let i = 0; i < numUploadedPictures; i++) {
                    images.push(`https://${bucket}.s3.eu-central-1.amazonaws.com/package/${resourceId}/${i + 1}`);
                }

                stripeService.updateProduct({ productId: resource.stripe_product_id, updates: { images } });
            }

            // Store updated number of pictures
            return await Model.query().patchAndFetchById(resourceId, { num_pictures: numUploadedPictures });
        }
        finally {
            // Cleanup temporary file in all cases (both failures and successes)
            Fs.unlink(zipFile.path).catch((err) => server.logger.warn({ ...logContext, err }, 'Unable to remove temporary file'));
        }
    }

};
