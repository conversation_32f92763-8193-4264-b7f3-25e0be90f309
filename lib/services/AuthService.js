'use strict';

const Schmervice = require('schmervice');
const { UnauthorizedError } = require('../helpers/errors');

const unauthorizedError = new UnauthorizedError('Action can not be performed');

const routeAuthorizationDict = {
    client: new Set(['create-shoot', 'checkout-shoot', 'synchronize-client-payment-data', 'user-search',
        'delete-sub-user', 'update-sub-role', 'enqueue-download-job', 'get-presigned-urls',
        'add-content', 'get-download-job-status', 'search-content', 'format-images', 'get-facebook-status', 'view-dashboard',
        // eslint-disable-next-line max-len
        'manage-collections', 'update-user', 'image-add-label', 'image-remove-label', 'image-add-category', 'image-remove-category', 'image-add-to-stack',
        'image-remove-from-stack', 'image-get-properties', 'payment-method-management', 'subscription-management', 'billing-management']),
    // Note: Added 'image-add-category' and 'image-remove-category' for client role
    photographer: new Set([]),
    admin: new Set(['user-search', 'create-shoot',
        'create-service', 'delete-service', 'update-service', 'reassign-service',
        'create-package', 'delete-package', 'update-package', 'upload-pictures',
        'shoots', 'reports-shoots', 'reports-users', 'show-notification-settings', 'update-notification-settings', 'update-user',
        'manage-collections', 'billing-management', 'subscription-management', 'payment-method-management',
        // eslint-disable-next-line max-len
        'delete-user-shoots', 'get-presigned-urls', 'add-content', 'enqueue-download-job', 'get-download-job-status', 'search-content', 'format-images', 'get-facebook-status', 'view-dashboard',
        // eslint-disable-next-line max-len
        'image-add-label', 'image-remove-label', 'image-add-category', 'image-remove-category', 'image-add-to-stack', 'image-remove-from-stack', 'image-get-properties'
    ])
};

module.exports = class AuthService extends Schmervice.Service {

    /**
     * Check if action can be performed on shoot
     *
     * @param {Object} shoot
     *  The shoot related to the download
     *
     * @param {string} role
     *  The role of the user
     *
     * @param {string} photographer_id
     *  The id of the photographer
     *
     * @param {string} action
     *  The action to be performed
     *
     * @param {Array} allowedRoles
     *  The roles allowed to perform the action
     *
     * @param {Array} allowedStatuses
     *  The statuses allowed to perform the action
     *
     */
    // TODO: refactor to reduce duplicated and hardcoded checks and to move the definition of allowed
    //`      roles/statuses into this service
    checkShootAuthorization({ shoot, role, photographer_id = null, action, allowedRoles, allowedStatuses }) {

        if (action === 'download-processed' || action === 'download-raw' || action === 'upload-brief') {
            if (!allowedRoles.includes(role) || !allowedStatuses.includes(shoot.status)) {
                throw unauthorizedError;
            }
        }

        else if (action === 'upload-raw') {
            if (role === 'photographer') {
                // Photographer can only upload if they are assigned to the shoot
                if (shoot.photographer_id !== photographer_id) {
                    throw unauthorizedError;
                }

                // Check if shoot is in allowed status
                if (!allowedStatuses.includes(shoot.status)) {
                    throw unauthorizedError;
                }
            }
            else if (role === 'admin') {
                // Admin can upload if shoot is in allowed status
                if (!allowedStatuses.includes(shoot.status)) {
                    throw unauthorizedError;
                }
            }
            else {
                throw unauthorizedError;
            }
        }
        else if (action === 'upload-processed') {
            if (role !== 'admin' && role !== 'photographer' && role !== 'client') {
                throw unauthorizedError;
            }
        }
        else if (action === 'assign-photographer') {
            if (!allowedRoles.includes(role) || !allowedStatuses.includes(shoot.status)) {
                throw unauthorizedError;
            }
        }
        else if (action === 'unassign-photographer') {
            if (!allowedRoles.includes(role) || !allowedStatuses.includes(shoot.status)) {
                throw unauthorizedError;
            }

            // Non-admin users can only disassign themselves
            if (role !== 'admin' && shoot.photographer_id !== photographer_id) {
                throw unauthorizedError;
            }
        }
        else if (action === 'schedule-shoot' || action === 'edit-shoot') {
            if (!allowedRoles.includes(role) || !allowedStatuses.includes(shoot.status)) {
                throw unauthorizedError;
            }

            if (role !== 'admin' && shoot.datetime && shoot.datetime < new Date().setHours(0, 0, 0, 0)) {
                throw unauthorizedError;
            }
        }
    }

    /**
     * Check if route can be accessible by role
     *
     * @param {string} role
     *  The role of the user
     *
     * @param {string} route
     *  The route to be accessed
     *
     */
    checkRouteAuthorization({ role, route }) {

        if (!routeAuthorizationDict[role].has(route)) {
            throw new UnauthorizedError('Route can not be accessed');
        }
    }

    /**
     * Check if action can be performed on user
     *
     * @param {Object} affectedUser
     *  The user to be affected
     *
     * @param {Object} user
     *  The user to be authorize
     *
     * @param {string} role
     *  The role of the user to be authorized
     *
     * @param {string} action
     *  The action to be performed
     *
     */
    checkUserAuthorization({ affectedUser, user, role, action }) {

        if (action === 'upload-raw') {
            if ((role === 'client' || role === 'photographer') && user.id !== affectedUser.id) {
                throw unauthorizedError;
            }
        }
    }

    /**
     * Check if action can be performed on by user on its data
     *
     * @param {Object} affectedUser
     *  The affected user
     *
     * @param {Object} user
     *  The user to be authorizes
     *
     * @param {string} role
     *  The role of the user to be authorized
     *
     * @return {Boolean} true if action is authorized
     */
    async checkUserOwnership({ affectedUser, user, role }) {

        const { userService } = this.server.services();
        if (role !== 'admin') {
            if (role === 'client' ) {
                const subClientIds = await userService.getSubClientIdsByClient({ user });

                if (subClientIds.length > 0 && !subClientIds.includes(Number(affectedUser.id)) && Number(affectedUser.id) !== user.id) {
                    throw unauthorizedError;
                }
            }
            else if (Number(affectedUser.id) !== user.id) {
                throw unauthorizedError;
            }
        }
    }

    /**
     * Check if action can be performed by subclient on create shoot
     *
     * @param {number} redeemable_shoot_id
     *  The redeemable shoot id
     *
     * @param {Object} user
     *  The user to be authorized
     *
     * @param {string} role
     *  The role of the user to be authorized
     *
     * @return {Boolean} true if action is authorized
     */
    checkSubClientWritePermission({ role, redeemable_shoot_id, user }) {

        if (role === 'client' && !redeemable_shoot_id && user.parent_client_id) {
            throw unauthorizedError;
        }
    }

    /**
     * Check if update user status can be performed
     *
     * @param {String} userToBeUpdatedRole
     *  The role of the affected user to be updated
     *
     * @param {Object} user
     *  The user to be authorized
     *
     * @param {Object} affectedUser
     *  The affected user
     *
     * @param {String} role
     *  The role of the user to be authorized
     *
     * @return {Boolean} true if action is authorized
     */
    checkUpdateUserStatus({ userToBeUpdatedRole, role, affectedUser, user }) {

        if ((userToBeUpdatedRole === 'photographer' || userToBeUpdatedRole === 'client') && role === 'admin') {
            return true;
        }
        else if (userToBeUpdatedRole === 'client' && Number(affectedUser.id) !== user.id) {
            return true;
        }

        return false;
    }

    async preparePayloadForAuth0FromToken( { id_token, additionalInformations = {} } ) {

        const { jwtService, userService, authService } = this.server.services();
        const token = await jwtService.decodeToken({ token: id_token });
        const profile = await userService.getAuth0UserById({ id: token.decoded.payload.sub });
        let user = await userService.getUserByEmail({ email: profile.email });

        if (!user && profile?.user_id?.startsWith('google-oauth2|')) {
            const paymentDetail = { type: 'test', details: {} };
            const newUser = {
                email: profile.email,
                name: profile.name || `${profile.given_name || ''} ${profile.family_name || ''}`.trim(),
                status: 'active',
                account_verified: true,
                auth_provider_reference: profile.user_id
            };

            additionalInformations.company_name = `${newUser.name} B2C`;
            additionalInformations.industry = 'Other';
            additionalInformations.contact_name = newUser.name;
            additionalInformations.contact_email = newUser.email;
            additionalInformations.contact_phone = null;
            user = await userService.createUser({ user: newUser, paymentDetail,
                additionalInformations, role: 'client', isVerified: true, syncAuth0: false });
            this.server.log(['info'], { userId: user.id, email: profile.email }, 'New user created successfully for Google OAuth2');
        }

        const sessionId = await authService.createUserActiveSession(user.id);
        const payload = { role: user.role, email: user.email, name: user.name, id: user.id, sid: sessionId };

        if (user.role === 'client') {

            const client = await userService.getFormattedUser({ id: user.id, role: user.role });
            if (client.registration_guid === null) {
                // For Google OAuth2 users, we may not have auth0 metadata
                try {
                    const authUser = await userService.getAuth0UserByEmail({ email: client.email });
                    if (authUser && authUser.user_metadata && authUser.user_metadata.subClientRole) {
                        payload.subClientRole = authUser.user_metadata.subClientRole;
                    }
                    else if (user.sub && user.sub.startsWith('google-oauth2|')) {
                        // Set a default subClientRole for Google users if needed
                        payload.subClientRole = 'standard';
                    }
                }
                catch (error) {
                    this.server.log(['error'], { error, email: user.email }, 'Error getting Auth0 user metadata');
                }
            }

            payload.company_name = client.company_name;
            payload.parent_client_id = client.parent_client_id;
            payload.client_id = client.client_id;
        }
        else if (user.role === 'photographer') {
            const photographer = await userService.getFormattedUser({ id: user.id, role: user.role });
            payload.is_internal = photographer.is_internal;
        }

        const jwtToken = await jwtService.generateToken({
            payload
        });

        return {
            user,
            token: jwtToken
        };
    }

    async getSessionByUserId(userId) {

        try {
            const { UserAccessHistory } = this.server.models();
            const record = await UserAccessHistory.query().findOne({ user_id: userId });

            if (record) {
                return record.active_session_id;
            }

            return null;
        }
        catch (error) {
            throw new Error(`Failed to get user session: ${error.message}`);
        }
    }

    async createUserActiveSession(userId) {

        try {
            const { UserAccessHistory } = this.server.models();
            const sessionId = this.generateSessionId();

            // Find the record first
            const record = await UserAccessHistory.query().findOne({ user_id: userId });

            if (record) {
                console.log('user_id', userId, 'exists');
                // Update the existing record with a new session ID
                await UserAccessHistory.query()
                    .patch({ active_session_id: sessionId, updated_at: new Date() })
                    .where('user_id', userId);

                return sessionId;
            }

            // Create new record if it doesn't exist
            await UserAccessHistory.query().insert({
                user_id: userId,
                active_session_id: sessionId,
                created_at: new Date(),
                updated_at: new Date()
            });

            return sessionId;
        }
        catch (error) {
            throw new Error(`Failed to update user session: ${error.message}`);
        }
    }

    generateSessionId() {

        return require('crypto').randomBytes(32).toString('hex');
    }

    async logoutUserSession(userId) {

        try {
            const { UserAccessHistory } = this.server.models();
            return await UserAccessHistory.query().where('user_id', userId).delete();
        }
        catch (error) {
            console.error(`Failed to logout user session: ${error.message}`);
        }
    }
};
