'use strict';

const Schmervice = require('schmervice');
const Stripe = require('stripe');

/**
 * Service class for handling Stripe related operations.
 */
module.exports = class StripeService extends Schmervice.Service {

    /**
     * Initializes the Stripe API.
     */
    initialize() {

        this.stripe = Stripe(this.server.settings.app.stripe.stripe_secret);
    }

    /**
     * Creates a Stripe product.
     * @param {Object} params - Parameters for creating a product.
     * @returns {Object} The created product and price.
     */
    async createProduct({ name, description, images = [], amount, currency = 'aed' }) {

        try {
            const product = await this.stripe.products.create({ name, description, images });
            const price = await this.createPrice({
                product: product.id,
                unit_amount: amount
            });
            return { product, price };
        }
        catch (error) {
            throw new Error(`Stripe Error : Failed to create a product | ${error}`);
        }
    }

    /**
     * Updates a Stripe product.
     * @param {string} productId - The ID of the product to update.
     * @param {Object} updates - The updates to apply.
     * @returns {Object} The updated product.
     */

    async updateProduct({ productId, updates }) {

        try {
            return await this.stripe.products.update(productId, updates);
        }
        catch (error) {
            throw new Error(`Stripe Error : Failed to update a product | ${error}`);
        }
    }

    /**
    * Deactivates a Stripe product by setting its 'active' status to false.
    * @param {string} productId - The ID of the product to deactivate.
    * @returns {Object} The response after updating the product.
     */
    async deleteProduct({ productId }) {

        try {
        // Update the product's 'active' status to false
            const updatedProduct = await this.stripe.products.update(productId, {
                active: false
            });
            return updatedProduct;
        }
        catch (error) {
            throw new Error(`Stripe Error : Failed to delete a product | ${error}`);
        }
    }

    /**
     * Creates a Stripe price.
     * @param {Object} params - Parameters for creating a price.
     * @returns {Object} The created price.
     */
    async createPrice({ product, unit_amount  }) {

        try {
            return await this.stripe.prices.create({
                product,
                unit_amount,
                currency: 'aed',
                tax_behavior: 'exclusive'
            });
        }
        catch (error) {
            throw new Error(`Stripe Error : Failed to create a price | ${error}`);
        }
    }

    async createSubscriptionPrice({ name, description, currency, unit_amount, interval, interval_count }) {

        try {
            const product = await this.stripe.products.create({ name, description });
            return await this.stripe.prices.create({
                product: product.id,
                unit_amount,
                currency,
                recurring: {
                    interval,
                    interval_count
                }
            });
        }
        catch (error) {
            throw new Error(`Stripe Error : Failed to create a subscription price | ${error}`);
        }
    }

    /**
     * Updates a Stripe price.
     * @param {string} priceId - The ID of the price to update.
     * @param {Object} updates - The updates to apply.
     * @returns {Object} The updated price.
     */

    async updatePrice({ priceId, updates }) {

        try {
            return await this.stripe.prices.update(priceId, updates);
        }
        catch (error) {
            throw new Error(`Stripe Error : Failed to update a price | ${error}`);
        }
    }

    /**
    * Marks a Stripe price as inactive.
    * Note: Stripe API does not allow deletion of price objects.
    * This method sets the price to inactive, rendering it unusable for new purchases.
    * @param {string} priceId - The ID of the price to set inactive.
    * @returns {Object} The updated price object with active set to false.
    */
    async deletePrice({ priceId }) {

        try {
            return await this.stripe.prices.update(priceId, { active: false });
        }
        catch (error) {
            throw new Error(`Stripe Error : Failed to delete a price | ${error}`);
        }
    }

    /**
     * Lists all prices for a given Stripe product.
     * @param {Object} params - Parameters for listing prices.
     * @param {string} params.productId - The ID of the product to list prices for.
     * @returns {Array} An array of Stripe price objects.
     */
    async listPrices({ productId }) {

        try {
            const prices = await this.stripe.prices.list({
                product: productId,
                active: true // Optional: Set to false to include inactive prices
            });
            return prices;
        }
        catch (error) {
            throw new Error(`Stripe Error : Failed to list prices| ${error}`);
        }
    }

    /**
     * Creates a Stripe customer.
     * @param {Object} params - Parameters for creating a customer.
     * @returns {Object} The created customer.
     */
    async createCustomer({ payload }) {

        try {
            return await this.stripe.customers.create({
                name: payload.name,
                email: payload.email,
                phone: payload.phone,
                address: {
                    city: payload.city,
                    country: payload.country,
                    line1: payload.formatted_address,
                    state: payload.state
                }
            });

        }
        catch (error) {
            throw new Error(`Stripe Error : Failed to create a customer | ${error}`);
        }
    }
    /**
 * Fetches a Stripe customer by their email.
 * @param {Object} params - Parameters for fetching a customer.
 * @param {string} params.email - The email of the customer to fetch.
 * @returns {Object|null} The Stripe customer object if found, otherwise null.
 */
    async getCustomerByEmail({ email }) {

        try {
        // Search for customers by email
            const customers = await this.stripe.customers.list({ email });

            // If one or more customers are found, return the first one
            if (customers.data.length > 0) {
                return customers.data[0];
            }

            // If no customers are found with that email, return null
            return null;

        }
        catch (error) {
            throw new Error(`Stripe Error : Failed to fetch a customer | ${error}`);
        }
    }

    /**
     * Construct a Stripe event from a payload, signature, and webhook secret.
     * @param {Object} payload - The raw payload received from the webhook.
     * @param {string} sig - The webhook signature received from the webhook.
     * @param {string} whsec - Your webhook secret.
     * @returns {Object} The constructed Stripe event.
     */
    constructEvent(payload, sig, whsec) {

        try {
            return this.stripe.webhooks.constructEvent(payload, sig, whsec);
        }
        catch (error) {
            throw new Error(`Stripe Error : Failed to construct event | ${error}`);
        }
    }
    async getCouponByName(couponName) {

        try {
            const coupons = await this.stripe.coupons.list();

            // Search for the coupon with the specified name
            const coupon = coupons.data.find((c) => c.name === couponName);

            if (coupon) {
                return coupon;
            }

            throw new Error(`Coupon ${couponName} not found.`);

        }
        catch (error) {
            throw new Error(`Stripe Error: Failed to retrieve coupon by name | ${error}`);
        }
    }

    async createSetupIntent({ user_id, reference }) {

        return await this.stripe.setupIntents.create({
            customer: reference,
            payment_method_types: ['card'],
            metadata: { user_id },
            usage: 'off_session'
        });
    }

    /**
     * @param payment_method_id
     * @returns {Promise<Stripe.PaymentMethod & {lastResponse: {headers: {[p: string]: string},
     * requestId: string, statusCode: number, apiVersion?: string, idempotencyKey?: string, stripeAccount?: string}}>}
     */
    async retrievePaymentMethod({ payment_method_id }) {

        try {
            return await this.stripe.paymentMethods.retrieve(payment_method_id);
        }
        catch (error) {
            throw new Error(`Stripe Error : Failed to get a payment method | ${error}`);
        }
    }

    async getCustomerByReference({ reference }) {

        try {
            return await this.stripe.customers.retrieve(reference);
        }
        catch (error) {
            throw new Error(`Stripe Error : Failed to get a customer | ${error}`);
        }
    }

    async createSubscription({ customer, price, metadata }) {

        try {
            return await this.stripe.subscriptions.create({
                customer,
                items: [{ price }],
                metadata,
                payment_behavior: 'default_incomplete',
                payment_settings: {
                    payment_method_types: ['card'],
                    save_default_payment_method: 'off'
                },
                expand: ['latest_invoice.payment_intent']
            });
        }
        catch (error) {
            throw new Error(`Stripe Error : Failed to create a subscription | ${error}`);
        }
    }

    async createSubscriptionPlan({ product, nickname, interval, interval_count, amount, weight }) {

        try {
            return await this.stripe.plans.create({
                product,
                nickname,
                interval,
                interval_count,
                currency: 'usd',
                amount: amount * 100,
                weight
            });
        }
        catch (error) {
            throw new Error(`Stripe Error : Failed to create a subscription plan | ${error}`);
        }
    }

    async setDefaultPaymentMethod({ customer, payment_method }) {

        try {
            // Step 1: Verify and attach the payment method
            const paymentMethod = await this.stripe.paymentMethods.retrieve(payment_method);
            if (!paymentMethod.customer || paymentMethod.customer !== customer) {
                await this.stripe.paymentMethods.attach(payment_method, {
                    customer
                });
            }

            // Step 2: Update the customer's default payment method
            const updatedCustomer = await this.stripe.customers.update(customer, {
                invoice_settings: {
                    default_payment_method: payment_method
                }
            });

            // Verify customer update
            if (updatedCustomer.invoice_settings.default_payment_method !== payment_method) {
                throw new Error('Failed to set default payment method on customer');
            }

            // Step 3: Get all active subscriptions for this customer
            const subscriptions = await this.stripe.subscriptions.list({
                customer,
                status: 'active'
            });

            // Step 4: Update each subscription with the new default payment method
            const updatedSubscriptions = [];
            const errors = [];

            for (const subscription of subscriptions.data) {
                try {
                    const updatedSubscription = await this.stripe.subscriptions.update(subscription.id, {
                        default_payment_method: payment_method
                    });

                    // Verify subscription update
                    if (updatedSubscription.default_payment_method !== payment_method) {
                        throw new Error(`Failed to set default payment method on subscription ${subscription.id}`);
                    }

                    updatedSubscriptions.push(updatedSubscription);
                }
                catch (subError) {
                    errors.push(`Subscription ${subscription.id}: ${subError.message}`);
                }
            }

            // Step 5: Return results and any errors
            const result = {
                customer: updatedCustomer,
                updatedSubscriptions
            };

            if (errors.length > 0) {
                result.warnings = errors;
                console.warn('Some subscriptions failed to update:', errors);
            }

            return result;
        }
        catch (error) {
            console.error('Stripe Error Details:', {
                message: error.message,
                code: error.code,
                type: error.type,
                raw: error.raw
            });
            throw new Error(`Stripe Error: Failed to set default payment method | ${error.message}`);
        }
    }

    async getCurrentActiveSubscription({ customer }) {

        try {
            const subscriptions = await this.stripe.subscriptions.list({
                customer,
                status: 'active',
                limit: 1,
                expand: ['data.plan'],
                created: {
                    gt: 0  // Retrieve all subscriptions created after timestamp 0
                }
            });

            return subscriptions.data[0] || null;
        }
        catch (error) {
            throw new Error(`Stripe Error : Failed to get current active subscription | ${error}`);
        }
    }

    async updateSubscription({ subscriptionId, newPriceId, paymentMethodId = null }) {

        try {
            // Get the subscription to find the item ID
            const subscription = await this.stripe.subscriptions.retrieve(subscriptionId);
            const itemId = subscription.items.data[0].id;
            const payload = {
                items: [{
                    id: itemId,
                    price: newPriceId
                }],
                proration_behavior: 'always_invoice', // Create a prorated invoice immediately
                payment_behavior: 'pending_if_incomplete', // Try to pay automatically,
                expand: ['latest_invoice.payment_intent']
            };

            if (paymentMethodId) {
                payload.default_payment_method = paymentMethodId;
            }

            // Update the subscription with the new price
            const updatedStripeSubscription = await this.stripe.subscriptions.update(subscriptionId, payload);

            // Check if 3DS authentication is required
            const latestInvoice = updatedStripeSubscription.latest_invoice;
            const paymentIntent = latestInvoice.payment_intent;

            let requires3DS = false;
            let clientSecret = null;

            if (paymentIntent) {
                if (
                    (paymentIntent.status === 'requires_action' ||
                    paymentIntent.status === 'requires_confirmation')) {
                    requires3DS = true;
                    clientSecret = paymentIntent.client_secret;
                }

                if (paymentIntent.last_payment_error?.code) {
                    throw new Error(`Payment failed: ${paymentIntent?.last_payment_error?.message}`);
                }

                if (paymentIntent.last_payment_error?.decline_code) {
                    throw new Error(`Payment failed: ${paymentIntent?.last_payment_error?.message}`);
                }
            }

            return {
                updatedStripeSubscription,
                requires3DS,
                clientSecret
            };
        }
        catch (error) {
            throw new Error(`Stripe Error: Failed to update subscription | ${error}`);
        }
    }

    async retrieveUpcomingInvoice({ subscription, subscription_items }) {

        try {
            return await this.stripe.invoices.retrieveUpcoming({
                customer: subscription.customer,
                subscription,
                subscription_items
            });
        }
        catch (error) {
            throw new Error(`Stripe Error: Failed to retrieve upcoming invoice | ${error}`);
        }
    }

    async getSubscription(subscriptionId) {

        try {
            return await this.stripe.subscriptions.retrieve(subscriptionId);
        }
        catch (error) {
            throw new Error(`Stripe Error: Failed to get subscription | ${error}`);
        }
    }

    async cancelSubscription({ subscriptionId, cancel_at_period_end = false }) {

        try {
            if (cancel_at_period_end) {
                // Cancel at period end
                return await this.stripe.subscriptions.update(
                    subscriptionId,
                    { cancel_at_period_end: true }
                );
            }

            // Cancel immediately
            return await this.stripe.subscriptions.cancel(subscriptionId);

        }
        catch (error) {
            throw new Error(`Stripe Error: Failed to cancel subscription | ${error}`);
        }
    }

    async confirmSubscriptionPayment(paymentIntentId) {

        try {
            // Retrieve the payment intent
            const paymentIntent = await this.stripe.paymentIntents.retrieve(
                paymentIntentId,
                { expand: ['invoice'] }
            );

            if (paymentIntent.status === 'succeeded') {
                // If the payment intent has an attached invoice, get the subscription from it
                if (paymentIntent.invoice) {
                    // Retrieve the invoice to get the subscription ID
                    const invoiceId = typeof paymentIntent.invoice === 'string'
                        ? paymentIntent.invoice
                        : paymentIntent.invoice.id;

                    // Retrieve the invoice to get the subscription ID
                    const invoice = await this.stripe.invoices.retrieve(invoiceId);

                    if (invoice.subscription) {
                        const subscriptionId = typeof invoice.subscription === 'string'
                            ? invoice.subscription
                            : invoice.subscription.id;

                        const subscription = await this.getSubscription(subscriptionId);
                        return {
                            success: true,
                            status: paymentIntent.status,
                            subscription: subscription.id
                        };
                    }
                }

                // Payment succeeded but no subscription found
                return {
                    success: true,
                    status: paymentIntent.status,
                    subscription: null
                };
            }

            // Payment didn't succeed
            return {
                success: false,
                status: paymentIntent.status,
                subscription: null
            };
        }
        catch (error) {
            throw new Error(`Stripe Error: Failed to confirm subscription payment | ${error}`);
        }
    }

    async getLatestInvoiceBySubscription(subscriptionId) {

        try {
            const invoices = await this.stripe.invoices.list({
                subscription: subscriptionId,
                limit: 1,
                // Sort by created date in descending order (newest first)
                created: { gt: 0 }
            });

            if (invoices.data.length === 0) {
                return null;
            }

            const invoice = invoices.data[0];
            return {
                id: invoice.id,
                created: invoice.created,
                amount_paid: invoice.amount_paid / 100,
                pdf_url: invoice.invoice_pdf,
                status: invoice.status,
                period_start: invoice.period_start,
                period_end: invoice.period_end
            };
        }
        catch (error) {
            throw new Error(`Stripe Error: Failed to get latest invoice for subscription | ${error}`);
        }
    }
};
