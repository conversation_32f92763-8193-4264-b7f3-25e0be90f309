'use strict';

const Schmervice = require('schmervice');
const Jwt = require('@hapi/jwt');
const { requireArguments } = require('../helpers/utils');

module.exports = class JwtService extends Schmervice.Service {

    /**
     * This function generates a valid (i.e., signed) JWT token
     * containing given payload data.
     *
     * @param {object} payload
     *  The payload data to include in the token.
     *
     * @return {string}
     */
    async generateToken({ payload }) {

        requireArguments({ payload });

        const { issuer, secret: key, algorithm, exptime } = this.server.settings.app.auth.jwt;

        const secret = { key, algorithm };
        const options = { ttlSec: exptime };

        return await Jwt.token.generate({ iss: issuer, ...payload }, secret, options);
    }

    /**
     * Decode a given JWT token.
     *
     * @param {String} token
     *  The token to be validated.
     */
    async decodeToken({ token }) {

        return await Jwt.token.decode(token);
    }

    /**
     * Validate a given JWT token.
     *
     * @param {String} token
     *  The token to be validated.
     */
    async validateToken({ token }) {

        return await Jwt.token.verify(await this.decodeToken({ token }), this.server.settings.app.auth.jwt.secret);
    }
};
