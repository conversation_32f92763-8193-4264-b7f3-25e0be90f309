'use strict';

const Wreck = require('@hapi/wreck');
const { ArgumentError } = require('./errors');
const { raw } = require('objection');
const Moment = require('moment');

/**
 * Returns a slug from a string
 * @param {string} str
 *  The string to slug
 */
module.exports.slugify =  (str) => {

    return str.toString().toLowerCase()
        .replace(/\s+/g, '-')           // Replace spaces with -
        .replace(/[^\w-]+/g, '')       // Remove all non-word chars
        .replace(/--+/g, '-')         // Replace multiple - with single -
        .replace(/^-+/, '')             // Trim - from start of text
        .replace(/-+$/, '');            // Trim - from end of text
};

/**
 * Gets a boolean value from `process.env`, parsing also
 * the string form (i.e., 'true').
 *
 * @param {string} varname
 *  The name of the environment variable.
 * @param {boolean} defaultValue
 *  The default value (to be used when the environment
 *  variable is not defined).
 */
module.exports.getEnvBoolean = (varname, defaultValue) => {

    const value = process.env[varname] || defaultValue;
    return value === true || value === 'true';
};

/**
 * Convert a `Date` object to a string representation
 * that can be used for mysql db timestamp columns.
 *
 * @param {Date} date
 *  The date to convert.
 */
module.exports.dateToMysqlTimestamp = (date) => date.toISOString().replace(/T/, ' ').replace(/\..+/, '');

/**
 * Asynchronously sleep for a given time.
 *
 * @param {int} milliseconds
 *  The time to sleep (in milliseconds).
 */
module.exports.sleep = async (milliseconds = 0) => await new Promise((resolve) => setTimeout(resolve, milliseconds));

/**
 * Performs an (async) action with a timeout, and raise an error if it
 * does not complete within the allowed time.
 *
 * @param {Function} action
 *  The action to perform, as a function with no parameters (potentially async).
 * @param {int} timeout
 *  The timeout (in milliseconds).
 * @param {string} errorMessage
 *  A custom message for the error in the case the timeout is reached. Optional.
 *
 * @returns the return value of `action` in case of success, otherwise an Error is raised.
 */
module.exports.doWithTimeout = ({ action, timeout = 0, errorMessage }) => {

    return Promise.race([
        action(),
        module.exports.sleep(timeout).then(() => {

            throw new Error(errorMessage || `Action exceeded timeout (${timeout} milliseconds)`);
        })
    ]);
};

/**
 * Poll an URL until a given callback is satisfied, with a given frequency
 * and limited amount of retries.
 * NOTE: exceptions raised when trying to connect to the URL are ignored.
 *
 * @param {string} url
 *  The remote URL to poll.
 * @param {Function} untilCallback
 *  The callback to determine whether the polling is finished or not, taking as an
 *  input a { res, payload } object as returned by `Wreck`. The polling stops when
 *  this callback returns true.
 * @param {int} frequency
 *  The polling frequency (in milliseconds).
 * @param {int} maxRetries
 *  The maximum number of attempts to do. Optional.
 */
module.exports.pollUrlUntil = async ({ url, untilCallback, frequency, maxRetries = -1 }) => {

    let retries = 0;

    while (await Wreck.get(url)
        .then(({ res, payload }) => untilCallback({ res, payload }))
        .catch(() => false) !== true) {

        if (maxRetries >= 0 && ++retries >= maxRetries) {
            throw new Error(`Polling for ${url} failed after reaching the maximum of ${maxRetries} attempts`);
        }

        await this.sleep(frequency);
    }
};

/**
 * Remove a given set of keys from one or more objects.
 *
 * @param {object|array} data
 *  The data to be filtered, either as a single object or as an
 *  array of objects (all with the same format).
 * @param {array} keys
 *  The array of keys to be removed.
 */
module.exports.removeKeys = ({ data, keys }) => {

    const keySet = new Set(keys);
    const remover = (obj) => Object.fromEntries(Object.entries(obj).filter(([key, _]) => !keySet.has(key)));
    return (data instanceof Array) ? data.map(remover) : remover(data);
};

/**
 * Fetch the original domain of the request, as sent by the requester.
 * NOTE: we support the parse of "x-forwarded-host" header to get the
 * original host also in the case of multiple reverse proxy in the middle.
 *
 * @param {Request} request
 *  The request object.
 */
module.exports.getDomainFromRequest = (request) => {

    const xForwardedHeaders = request.headers['x-forwarded-host'];
    const fullHost = xForwardedHeaders ? xForwardedHeaders.split(',')[0] : request.info.host;
    const [host] = fullHost.split(':');
    return host;
};

/**
 * Generate a pseudo-random string of len characters.
 *
 * @param {int} len
 *  The length of the string to be generated.
 */
module.exports.generateRandString = (len) => {

    let randomString = '';
    for ( ; randomString.length < len; randomString  += Math.random().toString(36).substr(2)) {}
    return  randomString.substr(0, len);
};

/** Concatenate a set of regular expressions into a single one.
 *
 * @param {Array} regExps
 *  The regular expressions to concatenate (either as strings or RegExp objects).
 */
module.exports.concatenateRegExps = (regExps) => RegExp(regExps.map((re) => re.source || re).join(''));

/**
 * Check that all required arguments of a function have been provided.
 *
 * @param {object} argumentObj
 *  An object containing the arguments to be checked.
 */
module.exports.requireArguments = (argumentObj) => {

    Object.entries(argumentObj).forEach(([key, value]) => {

        if (!value) {
            throw new ArgumentError(`Missing required parameter ${key}`);
        }
    });
};

/**
 * Filter one or more objects' keys.
 *
 * @param {object|array} data
 *  The data to be filtered, either as a single object or as an
 *  array of objects (all with the same format).
 * @param {array} keys
 *  The array of keys to be kept.
 */
module.exports.filterKeys = ({ data, keys }) => {

    const keySet = new Set(keys);
    const mapFunc = (element) => Object.fromEntries(Object.entries(element).filter(([key, _]) => keySet.has(key)));
    return (data instanceof Array) ? data.map(mapFunc) : mapFunc(data);
};

/**
 * Decorate a database query with a count statement and unwrap the
 * results in a convenient way.
 *
 * @param {QueryBuilder} baseQuery
 *  The objection query builder object representing the base query.
 *
 * @return {int} the result of the count query.
 */
module.exports.countQuery = (baseQuery) => {

    return baseQuery.count('* as n').then((res) => {

        if (res instanceof Array && res.length > 0) {
            return res[0].n || 0;
        }

        throw new Error('Knex count query gave inconsistent results');
    });
};

/**
 * check if element into objects are all undefined or null.
 *
 *  @param {object|array} data
 *  The data to be check, either as a single object or more objects (all with the same format).
 *
 * @return {boolean} true if all data are undefined or null, otherwise false.
 */
module.exports.checkUndefined = ({ data }) => {

    const valueChecker = (currentValue) =>  currentValue === undefined || currentValue === null;
    const dataArray = new Array(data);

    return dataArray.every(valueChecker);
};

/**
 * Perform an actions until it succeeds with no errors, or until a specified
 * maximum amount of retries.
 *
 * @param {Function} action
 *  The action to perform.
 * @param {int} timeout
 *  The time (in milliseconds) to wait between successive retries.
 * @param {Function} timeoutUpdateCallback
 *  A function taking the old timeout and returning the new one (can
 *  be used to increase/decrese it at each retry). Optional.
 * @param {int} maxRetries
 *  The maximum number of retries (default: 0).
 * @param {Function} onRetryCallback
 *  A callback to be invoked each time a new retry is scheduled, taking
 *  as input an object with the format `{ err, timeout }`. Optional.
 */
module.exports.doWithRetries = async ({ action, timeout, timeoutUpdateCallback = (t) => t, maxRetries = 0, onRetryCallback = (_) => {} }) => {

    try {
        return await action();
    }
    catch (err) {
        if (maxRetries > 0) {
            timeout = timeoutUpdateCallback(timeout);
            await onRetryCallback({ err, timeout });
            await this.sleep(timeout);
            await this.doWithRetries({ action, timeout, timeoutUpdateCallback, maxRetries: maxRetries - 1, onRetryCallback });
        }
        else {
            throw err;
        }
    }
};

/**
 * Groups an array of objects by one or more keys
 * See {@link https://gist.github.com/robmathers/1830ce09695f759bf2c4df15c29dd22d}
 * or {@link https://stackoverflow.com/questions/14446511/most-efficient-method-to-groupby-on-an-array-of-objects}
 * for further info.
 *
 * @param {array} arr
 *  The array of objects to be grouped.
 * @param {array} keys
 *  The array of string keys.
 */
module.exports.groupObjectsByKeys = (arr, keys) => {

    return arr.reduce((storage, item) => {

        const objKey = keys.map((key) => item[key]).join(':');
        if (storage[objKey]) {
            storage[objKey].push(item);
        }
        else {
            storage[objKey] = [item];
        }

        return storage;
    }, {});
};

/**
 * Decorate a database query with a sum statement and unwrap the
 * results in a convenient way.
 *
 * @param {QueryBuilder} baseQuery
 *  The objection query builder object representing the base query.
 * @param {String} sumValue
 *  The value to use in the sum query
 *
 * @return {int} the result of the sum query.
 */
module.exports.sumQuery = (baseQuery, sumValue) => {

    return baseQuery.sum(sumValue, { as: 'tot' }).then((res) => {

        if (res instanceof Array && res.length > 0) {
            return res[0].tot || 0;
        }

        throw new Error('Knex sum query gave inconsistent results');
    });
};

/**
 * Remove duplicate entries from an array of strings
 *
 * @param {array} arr
 *  The array to scan for duplicates.
 */
module.exports.removeDuplicates = (arr) => Array.from(new Set(arr));

/**
  * Consumes a readable stream into a string.
  *
  * @param {ReadableStream} stream
  *  The readable stream to consume.
  * @param {string} encoding
  *  The encoding to use to build the string (default: 'utf8').
  */
module.exports.streamToString = async ({ stream, encoding = 'utf8' }) => {

    const chunks = [];

    for await (const chunk of stream) {
        chunks.push(chunk);
    }

    return Buffer.concat(chunks).toString(encoding);
};

/**
 * Check whether or not we are in a development environment.
 * NOTE: this is based on the `NODE_ENV` environment variable.
 */
module.exports.isDev = () => process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'testing';

/**
 * Check whether or not the password is compliant to Auth0 password policies.
 */
module.exports.passwordChecker = (password) => {

    const passwordChecker = new RegExp('(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[^A-Za-z0-9])(?=.{8,})');
    return passwordChecker.test(password);
};

/**
 * Shuffles an array (not in-place) using the Fisher-Yates
 * algorithm (https://en.wikipedia.org/wiki/Fisher%E2%80%93Yates_shuffle)
 *
 * @param {Array} array
 *  The array to be shuffled
 */
module.exports.shuffle = (array) => {

    const result = Array.from(array);

    for (let i = result.length - 1; i > 0; --i) {
        const j = Math.floor(Math.random() * (i + 1));
        const tmp = result[i];
        result[i] = result[j];
        result[j] = tmp;
    }

    return result;
};

/**
 * Converts a number to centesimal
 *
 * @param {number} number
 *  The number to be converted in centesimal
 *
 * @return {string} the result of the conversion.
 */
module.exports.numberToCentesimal = (number) => {

    return parseFloat(number).toFixed(2).replace('.', '');
};

/**
 * Filters an array giving field and interval
 *
 * @param {array} array
 *  The array to be filtered
 *
 * @param {string} field
 *  The name of the field to be filtered
 *
 * @param {Object} interval
 *  The object related to the interval
 *
 * @return {array} the result of the filtering.
 */
module.exports.filterMaxMin = ({ array, field, interval = {} }) => {

    return array.filter((el) => (!interval.max || el[field] <= interval.max) && (!interval.min || el[field] >= interval.min));
};

/**
 * Case insensitive where clause for query builder with like operator
 *
 * @param {string} fieldName
 *  The name of the field in the table
 *
 * @param {string} value
 *  The value to be searched
 *
 * @return {rawBuilder} the rawBuilder object containing the filtering.
 */
module.exports.caseInsensitiveFiltering = ({ fieldName, value }) => {

    return raw(`LOWER(${fieldName}) like '%${value.toLowerCase()}%'`);
};

/**
 * Build an array of numbers from `from` (inclusive) to
 * `to` (exclusive), in increasing order.
 *
 * @param {int} from
 *  The start point of the range (defaults to 0).
 * @param {int} to
 *  The end point of the range (not included in the result).
 *
 * @return {array} the array reprensenting the range.
 */
module.exports.range = ({ from = 0, to }) => {

    return [...Array(to).keys()].filter((i) => i >= from);
};

/**
 * Offset a date by a given amount of minutes with UTC offset in minutes.
 *
 * @param {Date} Date
 *  The date to be offset.
 * @param {int} duration
 *  The amount of minutes to offset the date (in case of retrieval of shoot ending time).
 * @param {int} minutes
 *  The number of minutes to offset the final date by.
 * @param {int} utc_offset_minutes
 *  The UTC offset in minutes.
 *
 * @returns {Date} a new date which is `minutes` minutes in the future
 */
module.exports.addMinutesToDate = ({ date, duration = null, minutes, utc_offset_minutes }) => {

    const result = new Date(date);
    result.setMinutes(result.getMinutes() + duration + minutes - utc_offset_minutes);
    return result;
};

module.exports.stringifyLocation = ({ address }) => {

    return (address.formatted_address) ? address.formatted_address : 'formatted_address';
};

/**
 * Returns time in 'hh:mm A' format starting by isoString date
 *
 * @param {string} isoString
 *  The isoString date to be converted
 *
 * @returns {string} the time in 'hh:mm A' format
 */
const getTime = (isoString) => {

    const dateIndex = isoString.indexOf('T') + 1;
    const timeIndex = isoString.indexOf('Z');
    const time = isoString.slice(dateIndex, timeIndex).slice(0, 5);

    return Moment(time, ['HH:mm']).format('hh:mm A');
};

/**
 * Returns formattedDate, fromTime and toTime in 'YYYY-MM-DD, hh:mm A, hh:mm A' format
 *
 * @param {integer} duration
 *  The duration of the date
 *
 * @param {string} fromDate
 * The from date
 *
 * @returns {object} the formattedDate, fromTime and toTime in 'YYYY-MM-DD, hh:mm A, hh:mm A' format
 */
module.exports.formattedDateTime = ({ duration = 0, fromDate = '' }) => {

    if (!fromDate) {
        return '';
    }

    const [date] = fromDate.split('T');
    const [year, month, day] = date.split('-');
    const formattedDate = `${year}-${month}-${day}`;
    const fromTime = getTime(fromDate);
    let toTime;

    if (duration >= 0) {
        const startTime = Moment(fromTime, 'hh:mm a');
        toTime = startTime.add(duration, 'minutes').format('hh:mm A');
    }

    return {
        date: formattedDate,
        fromTime,
        toTime
    };

};

module.exports.convertIsoToTimestamp = (isoString) => {
    // Convert ISO string to Date object
    const date = new Date(isoString);

    // Get UNIX timestamp in milliseconds
    const unixTimestamp = date.getTime();

    return unixTimestamp;
};

module.exports.convertIsoToDate = (isoString)  => {

    const date = new Date(isoString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // January is 0
    const year = date.getFullYear();
    // 2023-12-20
    return `${year}-${month}-${day}`;
};

/**
 * Returns stringified object as expected by GraphQL (explained here https://stackoverflow.com/a/48577224)
 * @param {Object} object
 *  The object to be stringified
 *
 * @returns {string} the stringified object
 */
module.exports.stringifyJsonForGraphQL = ({ object }) => {

    return JSON.stringify(JSON.stringify(object));
};

/**
 * Returns formattedDate for email in 'DD MMMM YYYY' format
 *
 * @param {string} fromDate
 * The from date
 *
 * @returns {object} the formattedDate
 */
module.exports.formattedDateForEmail = ({ fromDate = '' }) => {

    if (!fromDate) {
        return '';
    }

    return Moment(fromDate, ['YYYY-MM-DD']).format('DD MMMM YYYY');
};

/**
* Check what fields are different in shoot excluding photographer_revenue
*
* @param {object} shoot
*  The shoot before editing.

* @param {object} newShoot
*  The edited shoot.
*
**/
module.exports.checkShootDifferences = ({ shoot, newShoot }) => {

    const fields = [];

    for (const key of Object.keys(shoot)) {

        if ( key !== 'photographer_revenue' && key !== 'last_update' && JSON.stringify(shoot[key]) !== JSON.stringify(newShoot[key])) {
            fields.push(key);
        }
    }

    return fields;
};

/**
* Returns an object containing arrays grouped by defined key
*
* @param {array} array
*  The array to be grouped

* @param {object} key
*  The key used for grouping.
*
* @returns {object} the object containing grouped arrays
**/
module.exports.groupBy = ( array, key ) => {
    // Return the end result
    return array.reduce((result, currentValue) => {
        // If an array already present for key, push it to the array. Else create an array and push the object
        (result[currentValue[key]] = result[currentValue[key]] || []).push(
            currentValue
        );
        // Return the current iteration `result` value, this will be taken as next iteration `result` value and accumulate
        return result;
    }, {}); // empty object is the initial value for result object
};

/**
 * Convert a storage size string (e.g., "1GB", "500MB") to bytes.
 *
 * @param sizeString
 */
module.exports.convertStorageSizeStringToBytes = (sizeString) => {

    const sizeRegex = /^(\d+)([KMGT]B)?$/;
    const match = sizeString?.replace(/\s+/g, '').trim().match(sizeRegex);

    if (!match) {
        throw new Error(`Invalid storage size string: ${sizeString}`);
    }

    const size = parseInt(match[1], 10);
    const unit = match[2];

    switch (unit) {
        case 'KB':
            return size * 1024;
        case 'MB':
            return size * 1024 * 1024;
        case 'GB':
            return size * 1024 * 1024 * 1024;
        case 'TB':
            return size * 1024 * 1024 * 1024 * 1024;
        default:
            return size; // bytes
    }
};
