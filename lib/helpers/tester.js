'use strict';

const { expect } = require('@hapi/code');

const Server = require('../../server');
const Joi = require('joi');
const Fs = require('fs');
const { doWithTimeout } = require('./utils');

const defaultTestTimeout = 5000;            // 5s (wraps the whole Lab test case)
const defaultRouteTimeout = 300;            // 300ms (wraps only the route call, for route-based tests)
const defaultSetupCallbackTimeout = 200;    // 200ms (wraps only the `setupCallback`, for route-based tests)
const defaultTestCallbackTimeout = 200;     // 200ms (wraps only the `testCallback`, for route-based tests)

/**
 * This wrapper object is needed to expose the server
 * object in a convenient way to test code.
 */
const serverWrapper = function () {

    this._server = null;
    this.deploy = async () => {

        this._server = await Server.deployment();
    };

    this.stop = async () => {

        if (this._server) {
            await this._server.stop();
        }
    };

    this.models = () => (this._server ? this._server.models() : []);
    this.services = () => (this._server ? this._server.services() : []);
    this.inject = async (data) => await this._server.inject(data);
};

/**
 * This is an internal method, used to setup various kind of experiment suites,
 * which can be customized by providing a `testerFunctionGenerator`.
 * NOTE: this method is not intended to be exposed as part of the module interface.
 */
const setupExperiment = ({ testerFunctionGenerator }) => {

    return (experimentName, options, experimentCallback) => {

        // Fix order of arguments
        if (typeof (options) === 'function') {
            experimentCallback = options;
            options = {};
        }

        const defaultOptions = {
            timeout: defaultTestTimeout
        };
        options = Object.assign({}, defaultOptions, options);

        const {
            beforeCallback,
            afterCallback,
            beforeEachCallback,
            afterEachCallback
        } = options;

        const { before, after, beforeEach, afterEach, experiment, test } = exports.lab = require('@hapi/lab').script();

        const server = new serverWrapper();

        const experimentOptions = {
            timeout: options.timeout
        };

        experiment(experimentName, experimentOptions, () => {

            before(async () => {

                await server.deploy();

                if (typeof beforeCallback === 'function') {
                    await beforeCallback(server);
                }
            });

            after(async () => {

                if (typeof afterCallback === 'function') {
                    await afterCallback(server);
                }

                await server.stop();
            });

            beforeEach(async () => {

                if (typeof beforeEachCallback === 'function') {
                    await beforeEachCallback(server);
                }
            });

            afterEach(async () => {

                if (typeof afterEachCallback === 'function') {
                    await afterEachCallback(server);
                }
            });

            experimentCallback({ test: testerFunctionGenerator({ experimentName, test, server }), lab: this.lab });
        });

        return this.lab;
    };
};

/**
 * Run a generic lab experiment. The server is automatically deployed before each
 * test, saved in the `server` variable and stopped after the test is completed.
 *
 * @param {string} experimentName
 *  The name of the experiment.
 @param {object} options
 *  The options for the experiment.
 *  Allowed options:
 *      timeout: max time for the each individual test in milliseconds (default 200ms).
 *      beforeCallback: a callback to be executed before experiment evaluation. Optional.
 *      afterCallback: a callback to be executed after experiment evaluation. Optional.
 *      beforeEachCallback: a callback to be executed before each test evaluation. Optional.
 *      afterEachCallback: a callback to be executed after each test evaluation. Optional.
 * @param {function} experimentCallback
 *  A callback containing the tests to be executed.
 *  It is called with a `test` parameter which is a function that can be used to run tests, and
 *  with a `lab` parameter which contains the lab instance which can be used for additional test
 *  setup (e.g., by using `lab.beforeEach` or similar).
 * @return {object} lab
 *  Retuns the lab object that needs to be exported from the test to register it.
 *
 * Tests can be run by calling test({ testName, options, testCallback}) from the experimentCallback.
 * @param {string} testName
 *  The name of the test.
 * @param {object} options
 *  An optional options object that will be directly passed to lab. Possible fields are { skip, only, timeout},
 *  where skip and only are booleans (default to false) and timeout is a nonnegative integer. Optional.
 * @param {function} testCallback
 *  A function taking the lab context and the server object and performing the actual test. If no exceptions are raised,
 *  then the test is considered successful; otherwise, it's considered an error.
 *
 * Example:
 * ```
 * const { experiment } = require('../../../lib/helpers/tester');
 * const { expect } = require('@hapi/code');
 *
 * exports.lab = experiment('experiment', ({ test, lab }) => {
 *
 *      lab.beforeEach(({ context }) => { context.someValue = 42 });
 *
 *      test({
 *          testName: 'test',
 *          testCallback: async ({ server, context }) => {
 *
 *              const { someService } = server.services();
 *
 *              expect(someService.someMethod()).to.be.equal(context.someValue);
 *          }
 *      });
 * })
 * ```
 */
const experiment = setupExperiment({
    testerFunctionGenerator: ({ test, server }) => {

        return ({ testName, options = {}, testCallback }) => {

            test(testName, options, async ({ context }) => {

                await testCallback({ context, server });
            });
        };
    }
});

/**
 * Run a lab experiment over a route. The server is automatically deployed before each
 * test, saved in the `server` variable and stopped after the test is completed.
 *
 * @param {string} experimentName
 *  The name of the experiment.
 * @param {object} options
 *  The options for the experiment.
 *  Allowed options:
 *      timeout: max time for the each individual test in milliseconds (default 200ms)
 * @param {function} experimentCallback
 *  A callback containing the tests to be executed.
 *  It is called with a `test` parameter which is a function that can be used to run tests, and
 *  with a `lab` parameter which contains the lab instance which can be used for additional test
 *  setup (e.g., by using `lab.beforeEach` or similar).
 * @return {object} lab
 *  Retuns the lab object that needs to be exported from the test to register it.
 *
 * Tests can be run by calling test({ testName, setupCallback, expectedStatusCode, testCallback}) from the experimentCallback.
 * @param {string} testName
 *  The name of the test.
 * @param {function} setupCallback
 *  A function taking the context and the server object and returning an object containing method, url
 *  and optionally auth parameters to be injected in the server.
 * @param {int} expectedStatusCode
 *  The status code that is expected to be returned when calling the route.
 * @param {object} dataSchema
 *  The Joi schema with which to validate the `data` field of the response (defaults to empty array).
 * @param {function} testCallback
 *  A function taking the context, the server object, the data returned by the route and eventual errors
 *  returned by the routes to perform additional experiments.
 * @param {object} options
 *  An optional options object that will be directly passed to lab. Possible fields are { skip, only, timeout},
 *  where skip and only are booleans (default to false) and timeout is a nonnegative integer.
 *
 * Example:
 * ```
 * const { experimentRoute } = require('../../../lib/helpers/tester');
 * const { expect } = require('@hapi/code');
 *
 * exports.lab = experimentRoute('experiment', ({ test }) => {
 *
 *      test({
 *          testName: 'test',
 *          expectedStatusCode: 200,
 *          setupCallback: async ({ server }) => {
 *
 *              return {
 *                  method: 'GET',
 *                  url: '/users'
 *              };
 *          },
 *          testCallback: async ({ server, data }) => {
 *
 *              expect(data.users).to.have.length(2);
 *          }
 *      });
 * })
 * ```
 */
const experimentRoute = setupExperiment({
    testerFunctionGenerator: ({ experimentName, test, server }) => {

        return ({ testName, setupCallback, expectedStatusCode, dataSchema, testCallback, options = {} }) => {

            const {
                setupCallbackTimeout = defaultSetupCallbackTimeout,
                routeTimeout = defaultRouteTimeout,
                testCallbackTimeout = defaultTestCallbackTimeout
            } = options;

            test(testName, options, async ({ context, note }) => {

                let injectData;
                try {
                    injectData = await doWithTimeout({
                        action: () => setupCallback({ server, context }),
                        timeout: setupCallbackTimeout,
                        errorMessage: `Setup callback exceeded timeout (${setupCallbackTimeout} milliseconds)`
                    });
                }
                catch (err) {
                    // If the setupCallback raised an exception, bail out of the test (it will be considered completed, as
                    // it did not explicitly fail but we were unable to set it up) and leave a note about it.
                    note(`The test "${experimentName} - ${testName}" was not run due to a failure (${err.message}) in the setupCallback.`);
                    note('Make sure you have run the test seeder and that it creates all the objects required for this test to succeed.');
                    return;
                }

                if (injectData.auth) {
                    const defaultAuth = {
                        strategy: 'jwt',
                        credentials: {
                            user: 1,
                            tenant: 1,
                            role: 'owner'
                        }
                    };

                    injectData.auth.credentials = Object.assign({}, defaultAuth.credentials, injectData.auth.credentials);
                    injectData.auth = Object.assign({}, defaultAuth, injectData.auth);
                }

                const res = await doWithTimeout({
                    action: () => server.inject(injectData),
                    timeout: routeTimeout,
                    errorMessage: `Route response exceeded timeout (${routeTimeout} milliseconds)`
                });

                expect(validJson(res.payload), 'INVALID JSON').to.be.true();

                const data = JSON.parse(res.payload);

                expect(validAPISchema(data), 'INVALID API SCHEMA').to.be.true();
                expect(data.statusCode, 'INVALID STATUS CODE').to.equal(expectedStatusCode);

                dataSchema = dataSchema || Joi.array().length(0);

                expect(dataSchema.validate(data.data).error, 'INVALID DATA SCHEMA').to.be.undefined();

                if (testCallback) {
                    await doWithTimeout({
                        action: () => testCallback({ data: data.data, errors: data.errors, server, context }),
                        timeout: testCallbackTimeout,
                        errorMessage: `Test callback exceeded timeout (${testCallbackTimeout} milliseconds)`
                    });
                }
            });
        };
    }
});

/**
 * Test if a string is a valid json
 * @param string jsonString
 * @return bool
 */
const validJson = (jsonString) => {

    try {
        JSON.parse(jsonString);
    }
    catch (e) {
        return false;
    }

    return true;
};

/**
 * Tests if the schema in the array is valid
 * @param array data
 * @return bool
 */
const validAPISchema = (data) => {

    /**
     * Make sense to store this schema in a configuration file somewhere
     * and pass to validAPISchema as second parameter
     */
    const schema = Joi.object({
        statusCode: Joi.number().integer().required(),
        data: Joi.required(),
        errors: Joi.array().required()
    });

    const { error } = schema.validate(data);

    if (typeof error === 'undefined') {
        return true;
    }

    return false;
};

/**
 * Transform data and files into into a multipart/form-data payload.
 *
 * @param {object} data
 *  The data to insert in the payload, in key-value format. Optional.
 * @param {object} files
 *  The files to be inserted in the payload, as an object where the key
 *  is the name of the set of files and the value an array containing
 *  objects of the format { filename, contentType }.
 */
const multipartPayloadBuilder = ({ data = {}, files = {} }) => {

    const payloadBuffers = [];

    for (const key in data) {
        payloadBuffers.push(Buffer.from(`--AaB03x\r\ncontent-disposition: form-data; name="${key}"\r\n\r\n${data[key]}\r\n`));
    }

    for (const key in files) {
        for (const { filename, contentType = 'text/plain' } of files[key]) {
            // eslint-disable-next-line max-len
            payloadBuffers.push(Buffer.from(`--AaB03x\r\ncontent-disposition: form-data; name="${key}"; filename="${filename}"\r\ncontent-type: ${contentType}\r\n\r\n`));
            payloadBuffers.push(Fs.readFileSync(filename));
            payloadBuffers.push(Buffer.from('\r\n'));
        }
    }

    payloadBuffers.push(Buffer.from('--AaB03x--\r\n'));

    const payload = Buffer.concat(payloadBuffers);

    return {
        headers: { 'content-type': 'multipart/form-data; boundary=AaB03x', 'content-length': Buffer.byteLength(payload) },
        payload
    };
};

module.exports = {
    validJson,
    validAPISchema,
    experiment,
    experimentRoute,
    multipartPayloadBuilder
};
