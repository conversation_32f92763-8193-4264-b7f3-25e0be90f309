'use strict';

const Path = require('path');
const Fs = require('fs');

const allServices = Fs.readdirSync('lib/services');
const isService = (filename, path) => allServices.includes(path);
const isMockService = (filename, path) => isService(filename, path) && filename.endsWith('Mock');
const hasMockService = (filename, path) => isService(filename, path) && !isMockService(filename, path) && allServices.includes(`${filename}Mock.js`);

module.exports = {
    /* 
     * says to hautecouture to be recursive on lib/routes folder
     * https://github.com/hapipal/haute-couture/blob/master/API.md#specifying-amendments-with-hcjs
     */
     recursive: true,
     exclude: (filename, path) => {

        if (path.split(Path.sep).includes('helpers')) {
            // Default from base haute-couture plugin
            return true;
        }
        
        if (process.env.MOCK_BACKEND && JSON.parse(process.env.MOCK_BACKEND) === true) {
            // If we are mocking the backend, then exclude services with have a mock
            return hasMockService(filename, path);
        }
        else {
            // Otherwise, exclude the mocks themselves
            return isMockService(filename, path);
        }
     }
};
