'use strict';

const Joi = require('joi');
const { createRoute } = require('../helpers/route_factory');

module.exports = createRoute({
    documentation: {
        description: 'Health check endpoint',
        response: Joi.object({
            msg: Joi.string().required()
        })
    },
    method: 'GET',
    path: '/health',
    auth: false,
    handler: (request, h) => {

        const { apiService } = request.services();
        return apiService.formatResponse(h, {
            msg: 'Server is healthy'
        });
    }
});
