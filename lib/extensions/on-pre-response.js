'use strict';

const { CustomError } = require('../helpers/errors');
const { isDev } = require('../helpers/utils');

module.exports = (server) => ({
    method: (request, h) => {

        // NOTE: this is to fix the case in which `request.services()` is empty (e.g., if auth fails)
        const { apiService } = { ...server.services(), ...request.services() };

        if (request.response.isBoom) {

            const err = request.response;

            // The normalization is to manage both Hapi internal errors and custom errors from the errors helper
            const normalizedError = (err.output && err.output.payload) ? err.output.payload : err;
            const statusCode = err.statusCode || normalizedError.statusCode || 500;
            const errorMessage = err.message || normalizedError.message || 'Internal server error';
            const isCustomError = err instanceof CustomError;

            request.logger[isCustomError ? 'warn' : 'error']({
                stack: err.stack,
                err: normalizedError,
                context: 'on-pre-response',
                request: { method: request.method, path: request.path }
            }, errorMessage);
            return apiService.error(h, statusCode, isCustomError || isDev() ? [errorMessage] : []);
        }

        alertResponseTime(request);

        return h.continue;
    }
});

const alertResponseTime = (request) => {

    const responseTime = Date.now() - request.info.received;

    const logFunction = (level) => {

        request.logger[level]({ context: 'on-pre-response' }, `Response time is ${responseTime}`);
    };

    if (responseTime > 10000) {
        logFunction('error');
    }
    else if (responseTime > 2000 ) {
        logFunction('warn');
    }
};
