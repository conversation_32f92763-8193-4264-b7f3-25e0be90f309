'use strict';

//USAGE: node AdminCreator.js {email} {password} {name}

const { ManagementClient } = require('auth0');

const Knexfile = require('../knexfile.js');

const { passwordChecker } = require('../lib/helpers/utils');

const knex = require('knex')(Knexfile);

const createAdmin = async ({ email, password, name }) => {

    const auth0Client = new ManagementClient({
        domain: process.env.AUTH_PROVIDER_CONFIG_DOMAIN,
        clientId: process.env.AUTH_PROVIDER_CLIENT_ID,
        clientSecret: process.env.AUTH_PROVIDER_CLIENT_SECRET,
        scope: 'read:users update:users create:users delete:users'
    });

    const auth0User = await auth0Client.createUser({
        email,
        password,
        name,
        connection: 'Username-Password-Authentication',
        email_verified: true,
        user_metadata: {
            role: 'admin'
        }
    }).catch((err) => {

        console.error(err);
    });

    try {
        const admin = {
            name,
            email,
            phone: '**********',
            photographer_id: null,
            client_id: null,
            status: 'active'
        };

        await knex('users').insert(admin);

    }
    catch (error) {
        await auth0Client.deleteUser({ id: auth0User.user_id }).catch((err) => {

            console.error(err);
        });

        console.error(error);
    }

};

//Check if command contains 5 arguments where 2 arguments are "node" and "AdminCreator.js"
//and the other 3 are "email", "password" and "name"

if (process.argv.length !== 5) {
    console.error('Expecting at least 3 arguments');
    console.log('\nUSAGE: node AdminCreator.js {email} {password} {name}');
    process.exit(0);
}

const [email, password, name] = process.argv.slice(2);

if (!passwordChecker(password)) {
    throw new Error('Incorrect password format');
}

createAdmin({ email, password, name }).then(() => process.exit(0));
