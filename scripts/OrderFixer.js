'use strict';

//USAGE: node OrderFixer.js

const Knexfile = require('../knexfile.js');

const knex = require('knex')(Knexfile);

const orderFixer = async () => {

    console.info('\n[INFO]: running orders fixer\n');

    const orders = await knex('orders')
        .join('shoots', 'orders.id', '=', 'shoots.order_id')
        .where('shoots.redeemable_total', '-1' ).select('orders.id');

    for (const { id } of orders) {

        console.info(`\n[INFO]: fixing order with id:  ${id}\n`);

        const redeemable_shoot = await knex('shoots')
            .where('order_id', id)
            .where('redeemable_total', '-1')
            .first();

        console.info(`\n[INFO]: expecting : ${redeemable_shoot.redeemable_counter * redeemable_shoot.price }\n`);

        await knex('orders')
            .where('id', id)
            .update({ price: redeemable_shoot.redeemable_counter * redeemable_shoot.price });
    }
};

orderFixer().then(() => process.exit(0));
