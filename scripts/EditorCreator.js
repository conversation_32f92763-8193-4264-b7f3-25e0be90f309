'use strict';

//USAGE: node EditorCreator.js {email} {password} {name}

const { ManagementClient } = require('auth0');

const Knexfile = require('../knexfile.js');

const { passwordChecker } = require('../lib/helpers/utils');

const knex = require('knex')(Knexfile);

const createEditor = async ({ email, password, name }) => {

    const auth0Client = new ManagementClient({
        domain: process.env.AUTH_PROVIDER_CONFIG_DOMAIN,
        clientId: process.env.AUTH_PROVIDER_CLIENT_ID,
        clientSecret: process.env.AUTH_PROVIDER_CLIENT_SECRET,
        scope: 'read:users update:users create:users delete:users'
    });

    const auth0User = await auth0Client.createUser({
        email,
        password,
        name,
        connection: 'Username-Password-Authentication',
        email_verified: true
    }).catch((err) => {

        console.error(err);
    });

    try {

        const { id } = (await knex('editors').insert({}, ['id']))[0];

        const editor_user = {
            name,
            email,
            phone: '**********',
            photographer_id: null,
            client_id: null,
            editor_id: id,
            status: 'active'
        };

        await knex('users').insert(editor_user);

        console.log('Editor succesfully created');
    }
    catch (error) {
        await auth0Client.deleteUser({ id: auth0User.user_id }).catch((err) => {

            console.error(err);
        });

        console.error(error);
    }

};

//Check if command contains 5 arguments where 2 arguments are "node" and "EditorCreator.js"
//and the other 3 are "email", "password" and "name"

if (process.argv.length !== 5) {
    console.error('Expecting at least 3 arguments');
    console.log('\nUSAGE: node EditorCreator.js {email} {password} {name}');
    process.exit(0);
}

const [email, password, name] = process.argv.slice(2);

if (!passwordChecker(password)) {
    throw new Error('Incorrect password format');
}

createEditor({ email, password, name }).then(() => process.exit(0));
