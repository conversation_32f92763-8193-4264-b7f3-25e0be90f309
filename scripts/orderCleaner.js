'use strict';

//USAGE: node OrderCleaner.js

const Knexfile = require('../knexfile.js');

const knex = require('knex')(Knexfile);

const orderCleaner = async () => {

    console.info('\n[INFO]: running orders cleaner\n');

    await knex.raw(`TRUNCATE shoots, orders, reports_shoots, reports_users, notification_queue, shoots_services, reports CASCADE;`);
};

orderCleaner().then(() => process.exit(0));
