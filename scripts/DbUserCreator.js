'use strict';

//USAGE: node DbUserCreator.js {username} {password}

const Knexfile = require('../knexfile.js');

const knex = require('knex')(Knexfile);

const dbUserCreator = async ({ username, password }) => {

    console.info('\n[INFO]: running database user creator\n');

    await knex.raw(`create role ${username} login password '${password}';`);

    await knex.raw(`GRANT SELECT ON ALL TABLES IN SCHEMA public TO ${username};`);

    console.info('\n[INFO]: database user created\n');

};

//Check if command contains 4 arguments where 2 arguments are "node" and "DbUserCreator.js"
//and the other 2 are "username" and "password"

if (process.argv.length !== 4) {
    console.error('Expecting at least 2 arguments');
    console.log('\nUSAGE: node DbUserCreator.js {username} {password}');
    process.exit(0);
}

const [username, password] = process.argv.slice(2);

dbUserCreator({ username, password }).then(() => process.exit(0));
