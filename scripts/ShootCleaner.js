'use strict';

//USAGE: node ShootCleaner.js {id1} {id2} {id3} ...

const Knexfile = require('../knexfile.js');

const knex = require('knex')(Knexfile);

const deleteShoot = async ({ id }) => {

    try {
        const shoot = await knex('shoots').where('id', id).first();
        if (!shoot) {
            console.error(`Shoot with ID: ${id} has not be found`);
            return;
        }

        await knex('shoots').where('id', shoot.id).delete();

        await knex('orders').where('id', shoot.order_id).delete();

        console.error(`Shoot with ID: ${id} has been deleted`);

    }
    catch (error) {
        console.error(error);
    }

};

//Check if command contains at least 3 arguments where 2 arguments are "node" and "ShootCleaner.js"
//and the other one is "id1"

if (process.argv.length < 3) {
    console.error('Expecting at least 1 shoot id');
    console.log('\nUSAGE: node ShootCleaner.js {id1} {id2} {id3} ...');
    process.exit(0);
}

(async () => {

    const shootsId = process.argv.slice(2);
    for (const id of shootsId) {
        await deleteShoot({ id }).then(() => {});
    }

    process.exit(0);
})();
