'use strict';

//USAGE: node UserCleaner.js {id1} {id2} {id3} ...

const { ManagementClient } = require('auth0');

const Knexfile = require('../knexfile.js');

const knex = require('knex')(Knexfile);

const deleteUser = async ({ id }) => {

    const auth0Client = new ManagementClient({
        domain: process.env.AUTH_PROVIDER_CONFIG_DOMAIN,
        clientId: process.env.AUTH_PROVIDER_CLIENT_ID,
        clientSecret: process.env.AUTH_PROVIDER_CLIENT_SECRET,
        scope: 'read:users update:users create:users delete:users'
    });
    try {
        const user = await knex('users').where('id', id).first();
        if (!user) {
            console.error(`User with ID: ${id} has not be found`);
            return;
        }

        await knex('users').where('id', user.id).delete();

        const photographer = (user.photographer_id) && await knex('photographers').where('id', user.photographer_id).first();

        const client = (user.client_id) && await knex('clients').where('id', user.client_id).first();

        if (photographer || client) {

            await knex((photographer) ? 'photographers' : 'clients').where('id', user?.photographer_id || user?.client_id).delete();
            await knex('payment_details').where('id', photographer?.payment_details_id || client?.payment_details_id).delete();
        }

        const [auth0User] = await auth0Client.getUsersByEmail(user.email);

        await auth0Client.deleteUser({ id: auth0User.user_id });

        console.error(`User with ID: ${id} has been deleted`);

    }
    catch (error) {
        console.error(error);
    }

};

//Check if command contains at least 3 arguments where 2 arguments are "node" and "AdminCreator.js"
//and the other one is "id1"

if (process.argv.length < 3) {
    console.error('Expecting at least 1 user id');
    console.log('\nUSAGE: node UserCleaner.js {id1} {id2} {id3} ...');
    process.exit(0);
}

(async () => {

    const usersId = process.argv.slice(2);
    for (const id of usersId) {
        await deleteUser({ id }).then(() => {});
    }

    process.exit(0);
})();
