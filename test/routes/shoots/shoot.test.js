'use strict';

const Joi = require('joi');
const { experimentRoute } = require('../../../lib/helpers/tester');

const { shootDetail } = require('../../../lib/validation-schema/shoots/responses.js');

exports.lab = experimentRoute('GET "/shoot/{id}"', ({ test }) => {

    test({
        testName: '[success 200] admin retrieves shoot',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { Shoot, User } = server.models();

            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const shoot = await Shoot.query().first();

            return {
                method: 'GET',
                url: `/v1/shoot/${shoot.id}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                }
            };
        },
        dataSchema: Joi.object({
            shoot: shootDetail
        })
    });

    test({
        testName: '[success 200] photographer retrieves shoot',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { Shoot, User } = server.models();

            const user = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const shoot = await Shoot.query().where('photographer_id', user.photographer_id).first();

            return {
                method: 'GET',
                url: `/v1/shoot/${shoot.id}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'photographer' }
                    }
                }
            };
        },
        dataSchema: Joi.object({
            shoot: shootDetail
        })
    });

    test({
        testName: '[success 200] client retrieves shoot',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { Shoot, User } = server.models();

            const user = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const shoot = await Shoot.query()
                .leftJoinRelated('order').where('client_id', user.client_id ).first();

            return {
                method: 'GET',
                url: `/v1/shoot/${shoot.id}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'client' }
                    }
                }
            };
        },
        dataSchema: Joi.object({
            shoot: shootDetail
        })
    });

    test({
        testName: '[failure 404] admin searches for a shoot not in db',
        expectedStatusCode: 404,
        setupCallback: async ({ server }) => {

            const { Shoot, User } = server.models();

            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const shoot = await Shoot.query().max('id').first();
            return {
                method: 'GET',
                url: `/v1/shoot/${shoot.max + 1}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                }
            };
        }
    });

    test({
        testName: '[failure 404] client searches for a shoot not in db',
        expectedStatusCode: 404,
        setupCallback: async ({ server }) => {

            const { Shoot, User } = server.models();

            const user = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const shoot = await Shoot.query().max('id').first();
            return {
                method: 'GET',
                url: `/v1/shoot/${shoot.max + 1}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'client' }
                    }
                }
            };
        }
    });

    test({
        testName: '[failure 404] photographer searches for a shoot not in db',
        expectedStatusCode: 404,
        setupCallback: async ({ server }) => {

            const { Shoot, User } = server.models();

            const user = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const shoot = await Shoot.query().max('id').first();
            return {
                method: 'GET',
                url: `/v1/shoot/${shoot.max + 1}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'photographer' }
                    }
                }
            };
        }
    });

    test({
        testName: '[failure 404] photographer searches for not his shoot',
        expectedStatusCode: 404,
        setupCallback: async ({ server }) => {

            const { Shoot, User } = server.models();

            const user = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const shoot = await Shoot.query().whereNot('photographer_id', user.photographer_id).first();

            return {
                method: 'GET',
                url: `/v1/shoot/${shoot.id}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'photographer' }
                    }
                }
            };
        }
    });

    test({
        testName: '[failure 404] client searches for not his shoot',
        expectedStatusCode: 404,
        setupCallback: async ({ server }) => {

            const { Shoot, User } = server.models();

            const user = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const shoot = await Shoot.query()
                .leftJoinRelated('order').whereNot('client_id', user.client_id ).first();

            return {
                method: 'GET',
                url: `/v1/shoot/${shoot.id}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'client' }
                    }
                }
            };
        }
    });
});
