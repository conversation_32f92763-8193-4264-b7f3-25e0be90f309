'use strict';

const Joi = require('joi');
const { experimentRoute } = require('../../../lib/helpers/tester');
const Shoot = require('../../../lib/models/Shoot');

const { shootDetail } = require('../../../lib/validation-schema/shoots/responses.js');

const buildTestData = async ({ server, period, status, isAssigned = true, role }) => {

    const { Service, User } = server.models();

    const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
    const { shootService } = server.services();

    const photographer = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
    const client = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();

    const serviceTested = await Service.query().orderBy('id', 'DESC').first();
    const customPackage = [{ service_id: serviceTested.id,
        picture_number: 13,
        package: {
            service_id: serviceTested.id,
            picture_number: 13,
            name: 'custom' }
    }];
    let time;
    if (period === 'future') {
        time = {
            from: new Date(Date.now() + 48 * (60 * 60 * 1000)),
            to: new Date(Date.now() + 50 * (60 * 60 * 1000))
        };
    }
    else if (period === 'past') {
        time =  {
            from: new Date(Date.now() - 48 * (60 * 60 * 1000)),
            to: new Date(Date.now() - 46 * (60 * 60 * 1000))
        };
    }
    else {
        time = {};
    }

    //Shoot created with datetime set to the day after tomorrow
    const { savedShoot } = await shootService.createShoot({
        packages: customPackage,
        photographer_id: (isAssigned) ? photographer.photographer_id : null,
        client_id: client.client_id,
        location: {
            city: 'city',
            country: 'country',
            line1: 'line1',
            postal_code: '12345',
            state: 'state',
            formatted_address: 'formatted_address'
        },
        price: 1200,
        photographer_revenue: 100,
        time,
        duration: 20,
        notes: 'notes',
        contact_phone: 'contact_phone',
        contact_name: 'contact_name',
        is_payed: true,
        role: 'admin',
        user
    });

    let updatedShoot;
    if (status) {
        updatedShoot = await shootService.updateShoot({
            shoot: savedShoot,
            role: 'admin',
            user,
            newValues: {
                status
            }
        });
    }

    let loggedUser = user;
    if (role === 'photographer') {
        loggedUser = photographer;
    }
    else if (role === 'client') {
        loggedUser = client;
    }

    return { loggedUser, savedShoot: (updatedShoot?.id) ? updatedShoot : savedShoot };

};

exports.lab = experimentRoute('POST "/shoot/update/{id}"', ({ test }) => {

    test({
        testName: '[success 200] admin updates datetime for an assigned shoot in the future',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { loggedUser, savedShoot } = await buildTestData({ server, period: 'future' });

            return {
                method: 'POST',
                url: `/v1/shoot/schedule/${savedShoot.id}`,
                auth: {
                    credentials: {
                        user: loggedUser,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    datetime: new Date()
                }
            };
        },
        dataSchema: Joi.object({
            shootDetail
        })
    });

    // test({
    //     testName: '[failure 401] admin updates datetime for an assigned shoot in the past',
    //     expectedStatusCode: 401,
    //     setupCallback: async ({ server }) => {

    //         const { loggedUser, savedShoot } = await buildTestData({ server, period: 'past' });

    //         return {
    //             method: 'POST',
    //             url: `/v1/shoot/schedule/${savedShoot.id}`,
    //             auth: {
    //                 credentials: {
    //                     user: loggedUser,
    //                     tokenPayload: { role: 'admin' }
    //                 }
    //             },
    //             payload: {
    //                 datetime: new Date()
    //             }
    //         };
    //     }
    // });

    test({
        testName: '[success 200] admin updates datetime for a confirmed shoot in the future',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { loggedUser, savedShoot } = await buildTestData({ server, period: 'future', status: Shoot.statuses.confirmed });

            return {
                method: 'POST',
                url: `/v1/shoot/schedule/${savedShoot.id}`,
                auth: {
                    credentials: {
                        user: loggedUser,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    datetime: new Date()
                }
            };
        },
        dataSchema: Joi.object({
            shootDetail
        })
    });

    // test({
    //     testName: '[failure 401] admin updates datetime for a confirmed shoot in the past',
    //     expectedStatusCode: 401,
    //     setupCallback: async ({ server }) => {

    //         const { loggedUser, savedShoot } = await buildTestData({ server, period: 'past', status: Shoot.statuses.confirmed });

    //         return {
    //             method: 'POST',
    //             url: `/v1/shoot/schedule/${savedShoot.id}`,
    //             auth: {
    //                 credentials: {
    //                     user: loggedUser,
    //                     tokenPayload: { role: 'admin' }
    //                 }
    //             },
    //             payload: {
    //                 datetime: new Date()
    //             }
    //         };
    //     }
    // });

    test({
        testName: '[success 200] admin updates datetime for a shoot to be scheduled',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { loggedUser, savedShoot } = await buildTestData({ server, period: 'no', isAssigned: false });

            return {
                method: 'POST',
                url: `/v1/shoot/schedule/${savedShoot.id}`,
                auth: {
                    credentials: {
                        user: loggedUser,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    datetime: new Date()
                }
            };
        },
        dataSchema: Joi.object({
            shootDetail
        })
    });

    test({
        testName: '[success 200] admin updates datetime for a shoot scheduled in the future',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { loggedUser, savedShoot } = await buildTestData({ server, period: 'future', isAssigned: false });

            return {
                method: 'POST',
                url: `/v1/shoot/schedule/${savedShoot.id}`,
                auth: {
                    credentials: {
                        user: loggedUser,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    datetime: new Date()
                }
            };
        },
        dataSchema: Joi.object({
            shootDetail
        })
    });

    // test({
    //     testName: '[failure 401] admin updates datetime for a shoot scheduled in the past',
    //     expectedStatusCode: 401,
    //     setupCallback: async ({ server }) => {

    //         const { loggedUser, savedShoot } = await buildTestData({ server, period: 'past', isAssigned: false });

    //         return {
    //             method: 'POST',
    //             url: `/v1/shoot/schedule/${savedShoot.id}`,
    //             auth: {
    //                 credentials: {
    //                     user: loggedUser,
    //                     tokenPayload: { role: 'admin' }
    //                 }
    //             },
    //             payload: {
    //                 datetime: new Date()
    //             }
    //         };
    //     }
    // });

    test({
        testName: '[failure 401] photographer updates datetime for a shoot assigned to him',
        expectedStatusCode: 401,
        setupCallback: async ({ server }) => {

            const { loggedUser, savedShoot } = await buildTestData({ server, period: 'future', isAssigned: true, role: 'photographer' });

            return {
                method: 'POST',
                url: `/v1/shoot/schedule/${savedShoot.id}`,
                auth: {
                    credentials: {
                        user: loggedUser,
                        tokenPayload: { role: 'photographer' }
                    }
                },
                payload: {
                    datetime: new Date()
                }
            };
        }
    });

    test({
        testName: '[failure 404] photographer updates datetime for a shoot not assigned to him',
        expectedStatusCode: 404,
        setupCallback: async ({ server }) => {

            const { loggedUser, savedShoot } = await buildTestData({ server, period: 'future', isAssigned: false, role: 'photographer' });

            return {
                method: 'POST',
                url: `/v1/shoot/schedule/${savedShoot.id}`,
                auth: {
                    credentials: {
                        user: loggedUser,
                        tokenPayload: { role: 'photographer' }
                    }
                },
                payload: {
                    datetime: new Date()
                }
            };
        }
    });

    test({
        testName: '[success 200] client updates datetime for an assigned shoot in the future',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { loggedUser, savedShoot } = await buildTestData({ server, period: 'future', role: 'client' });

            return {
                method: 'POST',
                url: `/v1/shoot/schedule/${savedShoot.id}`,
                auth: {
                    credentials: {
                        user: loggedUser,
                        tokenPayload: { role: 'client' }
                    }
                },
                payload: {
                    datetime: new Date()
                }
            };
        },
        dataSchema: Joi.object({
            shootDetail
        })
    });

    test({
        testName: '[failure 401] client updates datetime for an assigned shoot in the past',
        expectedStatusCode: 401,
        setupCallback: async ({ server }) => {

            const { loggedUser, savedShoot } = await buildTestData({ server, period: 'past', role: 'client' });

            return {
                method: 'POST',
                url: `/v1/shoot/schedule/${savedShoot.id}`,
                auth: {
                    credentials: {
                        user: loggedUser,
                        tokenPayload: { role: 'client' }
                    }
                },
                payload: {
                    datetime: new Date()
                }
            };
        }
    });

    test({
        testName: '[success 200] client updates datetime for a confirmed shoot in the future',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { loggedUser, savedShoot } = await buildTestData({ server, period: 'future', status: Shoot.statuses.confirmed, role: 'client' });

            return {
                method: 'POST',
                url: `/v1/shoot/schedule/${savedShoot.id}`,
                auth: {
                    credentials: {
                        user: loggedUser,
                        tokenPayload: { role: 'client' }
                    }
                },
                payload: {
                    datetime: new Date()
                }
            };
        },
        dataSchema: Joi.object({
            shootDetail
        })
    });

    test({
        testName: '[failure 401] client updates datetime for a confirmed shoot in the past',
        expectedStatusCode: 401,
        setupCallback: async ({ server }) => {

            const { loggedUser, savedShoot } = await buildTestData({ server, period: 'past', status: Shoot.statuses.confirmed, role: 'client' });

            return {
                method: 'POST',
                url: `/v1/shoot/schedule/${savedShoot.id}`,
                auth: {
                    credentials: {
                        user: loggedUser,
                        tokenPayload: { role: 'client' }
                    }
                },
                payload: {
                    datetime: new Date()
                }
            };
        }
    });

    test({
        testName: '[success 200] client updates datetime for a shoot to be scheduled',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { loggedUser, savedShoot } = await buildTestData({ server, period: 'no', isAssigned: false, role: 'client' });

            return {
                method: 'POST',
                url: `/v1/shoot/schedule/${savedShoot.id}`,
                auth: {
                    credentials: {
                        user: loggedUser,
                        tokenPayload: { role: 'client' }
                    }
                },
                payload: {
                    datetime: new Date()
                }
            };
        },
        dataSchema: Joi.object({
            shootDetail
        })
    });

    test({
        testName: '[success 200] client updates datetime for a shoot scheduled in the future',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { loggedUser, savedShoot } = await buildTestData({ server, period: 'future', isAssigned: false, role: 'client' });

            return {
                method: 'POST',
                url: `/v1/shoot/schedule/${savedShoot.id}`,
                auth: {
                    credentials: {
                        user: loggedUser,
                        tokenPayload: { role: 'client' }
                    }
                },
                payload: {
                    datetime: new Date()
                }
            };
        },
        dataSchema: Joi.object({
            shootDetail
        })
    });

    test({
        testName: '[failure 401] client updates datetime for a shoot scheduled in the past',
        expectedStatusCode: 401,
        setupCallback: async ({ server }) => {

            const { loggedUser, savedShoot } = await buildTestData({ server, period: 'past', isAssigned: false, role: 'client' });

            return {
                method: 'POST',
                url: `/v1/shoot/schedule/${savedShoot.id}`,
                auth: {
                    credentials: {
                        user: loggedUser,
                        tokenPayload: { role: 'client' }
                    }
                },
                payload: {
                    datetime: new Date()
                }
            };
        }
    });
});
