'use strict';

const Joi = require('joi');
const { experimentRoute } = require('../../../lib/helpers/tester');

const { shoot } = require('../../../lib/validation-schema/shoots/responses.js');
const Shoot = require('../../../lib/models/Shoot');
const { expect } = require('@hapi/code');

exports.lab = experimentRoute('POST "/shoot"', ({ test }) => {

    test({
        testName: '[success 200] admin creates a custom payed shoot with photographer and not scheduled',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const photographer =  await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const client =  await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const firstService = await Service.query().orderBy('id', 'ASC').first();
            // const secondService = await Service.query().orderBy('id', 'DESC').first();

            return {
                method: 'POST',
                url: `/v1/shoot`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    packages: [
                        {
                            service_id: firstService.id,
                            picture_number: 13,
                            video_number: 0,
                            video_duration: 0
                        }
                    ],
                    photographer_id: photographer.photographer_id,
                    client_id: client.client_id,
                    location: {
                        city: 'city',
                        country: 'country',
                        line1: 'line1',
                        postal_code: '12345',
                        state: 'state',
                        formatted_address: 'formatted_address'
                    },
                    total_price: 1200,
                    total_revenue: 100,
                    time: {
                        duration: 0
                    },
                    type: 'custom',
                    notes: 'notes',
                    redeemable_total: null,
                    contact_phone: 'contact_phone',
                    contact_name: 'contact_name',
                    is_payed: true,
                    content: 'photography'
                }
            };
        },
        dataSchema: Joi.object({
            shoot
        }),
        testCallback: ({ data }) => {

            const savedShoot = data.shoot;
            expect(savedShoot.status).to.be.equal(Shoot.statuses.toSchedule);
            expect(savedShoot.scheduled).to.be.equal(false);
        }
    });

    test({
        testName: '[success 200] admin creates a custom payed shoot with photographer and scheduled',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const photographer =  await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const client =  await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const firstService = await Service.query().orderBy('id', 'ASC').first();
            // const secondService = await Service.query().orderBy('id', 'DESC').first();

            return {
                method: 'POST',
                url: `/v1/shoot`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    packages: [
                        {
                            service_id: firstService.id,
                            picture_number: 13,
                            video_number: 0,
                            video_duration: 0
                        }
                    ],
                    photographer_id: photographer.photographer_id,
                    client_id: client.client_id,
                    location: {
                        city: 'city',
                        country: 'country',
                        line1: 'line1',
                        postal_code: '12345',
                        state: 'state',
                        formatted_address: 'formatted_address'
                    },
                    total_price: 1200,
                    total_revenue: 100,
                    type: 'custom',
                    time: {
                        from: Date.now(),
                        duration: 20
                    },
                    notes: 'notes',
                    redeemable_total: null,
                    contact_phone: 'contact_phone',
                    contact_name: 'contact_name',
                    is_payed: true,
                    content: 'photography'
                }
            };
        },
        dataSchema: Joi.object({
            shoot
        }),
        testCallback: ({ data }) => {

            const savedShoot = data.shoot;
            expect(savedShoot.status).to.be.equal(Shoot.statuses.photographerAssigned);
            expect(savedShoot.scheduled).to.be.equal(true);
        }
    });

    test({
        testName: '[success 200] admin creates a custom payed shoot with no photographer and scheduled',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const client =  await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const firstService = await Service.query().orderBy('id', 'ASC').first();
            // const secondService = await Service.query().orderBy('id', 'DESC').first();

            return {
                method: 'POST',
                url: `/v1/shoot`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    packages: [
                        {
                            service_id: firstService.id,
                            picture_number: 13,
                            video_duration: 0,
                            video_number: 0
                        }
                    ],
                    photographer_id: null,
                    client_id: client.client_id,
                    type: 'custom',
                    location: {
                        city: 'city',
                        country: 'country',
                        line1: 'line1',
                        postal_code: '12345',
                        state: 'state',
                        formatted_address: 'formatted_address'
                    },
                    total_price: 1200,
                    total_revenue: 100,
                    time: {
                        from: Date.now(),
                        duration: 20
                    },
                    notes: 'notes',
                    redeemable_total: null,
                    contact_phone: 'contact_phone',
                    contact_name: 'contact_name',
                    is_payed: true,
                    content: 'photography'
                }
            };
        },
        dataSchema: Joi.object({
            shoot
        }),
        testCallback: ({ data }) => {

            const savedShoot = data.shoot;
            expect(savedShoot.status).to.be.equal(Shoot.statuses.scheduled);
            expect(savedShoot.scheduled).to.be.equal(true);
        }
    });

    test({
        testName: '[failure 400] admin creates a custom shoot with not existent service',
        expectedStatusCode: 400,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const client =  await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const photographer =  await User.query().whereNull('client_id').whereNotNull('photographer_id').first();

            const secondService = await Service.query().orderBy('id', 'DESC').first();

            return {
                method: 'POST',
                url: `/v1/shoot`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    packages: [
                        {
                            service_id: secondService.id + 1,
                            picture_number: 15
                        }
                    ],
                    photographer_id: photographer.photographer_id,
                    client_id: client.client_id,
                    location: {
                        city: 'city',
                        country: 'country',
                        line1: 'line1',
                        postal_code: '12345',
                        state: 'state',
                        formatted_address: 'formatted_address'
                    },
                    total_price: 1200,
                    total_revenue: 100,
                    type: 'custom',
                    time: {
                        from: Date.now(),
                        duration: 20
                    },
                    notes: 'notes',
                    redeemable_total: null,
                    contact_phone: 'contact_phone',
                    contact_name: 'contact_name',
                    is_payed: false
                }
            };
        }
    });

    test({
        testName: '[failure 404] admin creates a custom shoot with not existent photographer',
        expectedStatusCode: 404,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const client =  await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const photographer =  await User.query().whereNull('client_id')
                .whereNotNull('photographer_id').orderBy('photographer_id', 'DESC').first();

            const secondService = await Service.query().orderBy('id', 'DESC').first();

            return {
                method: 'POST',
                url: `/v1/shoot`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    packages: [
                        {
                            service_id: secondService.id,
                            picture_number: 15,
                            video_duration: 0,
                            video_number: 0
                        }
                    ],
                    photographer_id: photographer.photographer_id + 1,
                    client_id: client.client_id,
                    location: {
                        city: 'city',
                        country: 'country',
                        line1: 'line1',
                        postal_code: '12345',
                        state: 'state',
                        formatted_address: 'formatted_address'
                    },
                    total_price: 1200,
                    total_revenue: 100,
                    type: 'custom',
                    time: {
                        from: Date.now(),
                        duration: 20
                    },
                    notes: 'notes',
                    redeemable_total: null,
                    contact_phone: 'contact_phone',
                    contact_name: 'contact_name',
                    is_payed: false,
                    content: 'photography'
                }
            };
        }
    });

    test({
        testName: '[failure 400] admin creates a custom shoot with not existent client',
        expectedStatusCode: 400,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const client =  await User.query().whereNull('photographer_id')
                .whereNotNull('client_id').orderBy('client_id', 'DESC').first();
            const photographer =  await User.query().whereNull('client_id').whereNotNull('photographer_id').first();

            const secondService = await Service.query().orderBy('id', 'DESC').first();

            return {
                method: 'POST',
                url: `/v1/shoot`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    packages: [
                        {
                            service_id: secondService.id,
                            picture_number: 15
                        }
                    ],
                    photographer_id: photographer.photographer_id,
                    client_id: client.client_id + 1,
                    location: {
                        city: 'city',
                        country: 'country',
                        line1: 'line1',
                        postal_code: '12345',
                        state: 'state',
                        formatted_address: 'formatted_address'
                    },
                    total_price: 1200,
                    total_revenue: 100,
                    type: 'custom',
                    time: {
                        from: Date.now(),
                        duration: 20
                    },
                    duration: 20,
                    notes: 'notes',
                    redeemable_total: null,
                    contact_phone: 'contact_phone',
                    contact_name: 'contact_name',
                    is_payed: false
                }
            };
        }
    });

    test({
        testName: '[failure 400] admin creates a custom payed shoot with wrong payload',
        expectedStatusCode: 400,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const photographer =  await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const client =  await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const firstService = await Service.query().orderBy('id', 'ASC').first();
            const secondService = await Service.query().orderBy('id', 'DESC').first();

            return {
                method: 'POST',
                url: `/v1/shoot`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    packages: [
                        {
                            service_id: firstService.id,
                            picture_number: 13
                        },
                        {
                            service_id: secondService.id,
                            picture_number: 15
                        }
                    ],
                    photographer_id: photographer.photographer_id,
                    client_id: client.client_id,
                    location: {
                        city: 'city',
                        country: 'country',
                        line1: 'line1',
                        postal_code: '12345',
                        state: 'state',
                        formatted_address: 'formatted_address'
                    },
                    total_price: 1200,
                    total_revenue: 100,
                    type: 'custom',
                    time: {
                        duration: 0
                    },
                    wrong_field: 'string',
                    notes: 'notes',
                    redeemable_total: null,
                    contact_phone: 'contact_phone',
                    contact_name: 'contact_name',
                    is_payed: true
                }
            };
        }
    });

    test({
        testName: '[success 200] admin creates a fixed payed shoot with one package, photographer and not scheduled',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Package } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const photographer =  await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const client =  await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const packageTested = await Package.query().first();

            return {
                method: 'POST',
                url: `/v1/shoot`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    packages: [
                        {
                            package_id: packageTested.id
                        }
                    ],
                    photographer_id: photographer.photographer_id,
                    client_id: client.client_id,
                    location: {
                        city: 'city',
                        country: 'country',
                        line1: 'line1',
                        postal_code: '12345',
                        state: 'state',
                        formatted_address: 'formatted_address'
                    },
                    total_price: 1200,
                    total_revenue: 100,
                    type: 'express',
                    time: {
                        duration: 0
                    },
                    notes: 'notes',
                    redeemable_total: null,
                    contact_phone: 'contact_phone',
                    contact_name: 'contact_name',
                    is_payed: true
                }
            };
        },
        dataSchema: Joi.object({
            shoot
        }),
        testCallback: ({ data }) => {

            const savedShoot = data.shoot;
            expect(savedShoot.status).to.be.equal(Shoot.statuses.toSchedule);
            expect(savedShoot.scheduled).to.be.equal(false);
        }
    });

    test({
        testName: '[success 200] admin creates a fixed payed shoot with one package, photographer and scheduled',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Package } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const client =  await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const photographer =  await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const packageTested = await Package.query().first();

            return {
                method: 'POST',
                url: `/v1/shoot`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    packages: [
                        {
                            package_id: packageTested.id
                        }
                    ],
                    photographer_id: photographer.photographer_id,
                    client_id: client.client_id,
                    location: {
                        city: 'city',
                        country: 'country',
                        line1: 'line1',
                        postal_code: '12345',
                        state: 'state',
                        formatted_address: 'formatted_address'
                    },
                    total_price: 1200,
                    total_revenue: 100,
                    type: 'express',
                    time: {
                        from: Date.now(),
                        duration: 20
                    },
                    notes: 'notes',
                    redeemable_total: null,
                    contact_phone: 'contact_phone',
                    contact_name: 'contact_name',
                    is_payed: true
                }
            };
        },
        dataSchema: Joi.object({
            shoot
        }),
        testCallback: ({ data }) => {

            const savedShoot = data.shoot;
            expect(savedShoot.status).to.be.equal(Shoot.statuses.photographerAssigned);
            expect(savedShoot.scheduled).to.be.equal(true);
        }
    });

    test({
        testName: '[success 200] admin creates a fixed payed shoot with one package, no photographer and scheduled',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Package } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const client =  await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const packageTested = await Package.query().first();

            return {
                method: 'POST',
                url: `/v1/shoot`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    packages: [
                        {
                            package_id: packageTested.id
                        }
                    ],
                    photographer_id: null,
                    client_id: client.client_id,
                    location: {
                        city: 'city',
                        country: 'country',
                        line1: 'line1',
                        postal_code: '12345',
                        state: 'state',
                        formatted_address: 'formatted_address'
                    },
                    total_price: 1200,
                    total_revenue: 100,
                    type: 'express',
                    time: {
                        from: Date.now(),
                        duration: 20
                    },
                    notes: 'notes',
                    redeemable_total: null,
                    contact_phone: 'contact_phone',
                    contact_name: 'contact_name',
                    is_payed: true
                }
            };
        },
        dataSchema: Joi.object({
            shoot
        }),
        testCallback: ({ data }) => {

            const savedShoot = data.shoot;
            expect(savedShoot.status).to.be.equal(Shoot.statuses.scheduled);
            expect(savedShoot.scheduled).to.be.equal(true);
        }
    });

    test({
        testName: '[success 200] admin creates a fixed payed shoot with two packages, photographer and not scheduled',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Package } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const photographer =  await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const client =  await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const somePackage = await Package.query().first();
            const someOtherPackage = await Package.query().whereNot('service_id', somePackage.service_id).first();

            return {
                method: 'POST',
                url: `/v1/shoot`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    packages: [
                        {
                            package_id: somePackage.id
                        },
                        {
                            package_id: someOtherPackage.id
                        }
                    ],
                    photographer_id: photographer.photographer_id,
                    client_id: client.client_id,
                    location: {
                        city: 'city',
                        country: 'country',
                        line1: 'line1',
                        postal_code: '12345',
                        state: 'state',
                        formatted_address: 'formatted_address'
                    },
                    total_price: 1200,
                    total_revenue: 100,
                    type: 'express',
                    time: {
                        duration: 0
                    },
                    notes: 'notes',
                    redeemable_total: null,
                    contact_phone: 'contact_phone',
                    contact_name: 'contact_name',
                    is_payed: true
                }
            };
        },
        dataSchema: Joi.object({
            shoot
        }),
        testCallback: ({ data }) => {

            const savedShoot = data.shoot;
            expect(savedShoot.status).to.be.equal(Shoot.statuses.toSchedule);
            expect(savedShoot.scheduled).to.be.equal(false);
        }
    });

    test({
        testName: '[failure 400] admin creates a fixed payed shoot with one wrong package',
        expectedStatusCode: 400,
        setupCallback: async ({ server }) => {

            const { User, Package } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const photographer =  await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const client =  await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const firstPackageTested = await Package.query().orderBy('id', 'DESC').first();

            return {
                method: 'POST',
                url: `/v1/shoot`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    packages: [
                        {
                            package_id: firstPackageTested.id + 1
                        }
                    ],
                    photographer_id: photographer.photographer_id,
                    client_id: client.client_id,
                    location: {
                        city: 'city',
                        country: 'country',
                        line1: 'line1',
                        postal_code: '12345',
                        state: 'state',
                        formatted_address: 'formatted_address'
                    },
                    total_price: 1200,
                    total_revenue: 100,
                    type: 'express',
                    time: {
                        duration: 0
                    },
                    notes: 'notes',
                    redeemable_total: null,
                    contact_phone: 'contact_phone',
                    contact_name: 'contact_name',
                    is_payed: true
                }
            };
        }
    });

    test({
        testName: '[failure 404] admin creates a fixed shoot with not existent photographer',
        expectedStatusCode: 404,
        setupCallback: async ({ server }) => {

            const { User, Package } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const client =  await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const photographer =  await User.query().whereNull('client_id')
                .whereNotNull('photographer_id').orderBy('photographer_id', 'DESC').first();
            const packageTested = await Package.query().first();

            return {
                method: 'POST',
                url: `/v1/shoot`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    packages: [
                        {
                            package_id: packageTested.id
                        }
                    ],
                    photographer_id: photographer.photographer_id + 1,
                    client_id: client.client_id,
                    location: {
                        city: 'city',
                        country: 'country',
                        line1: 'line1',
                        postal_code: '12345',
                        state: 'state',
                        formatted_address: 'formatted_address'
                    },
                    total_price: 1200,
                    total_revenue: 100,
                    type: 'express',
                    time: {
                        from: Date.now(),
                        duration: 20
                    },
                    notes: 'notes',
                    redeemable_total: null,
                    contact_phone: 'contact_phone',
                    contact_name: 'contact_name',
                    is_payed: false
                }
            };
        }
    });

    test({
        testName: '[failure 404] admin creates a fixed shoot with not existent client',
        expectedStatusCode: 404,
        setupCallback: async ({ server }) => {

            const { User, Package } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const client =  await User.query().whereNull('photographer_id')
                .whereNotNull('client_id').orderBy('client_id', 'DESC').first();
            const photographer =  await User.query().whereNull('client_id').whereNotNull('photographer_id').first();

            const packageTested = await Package.query().first();

            return {
                method: 'POST',
                url: `/v1/shoot`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    packages: [
                        {
                            package_id: packageTested.id
                        }
                    ],
                    photographer_id: photographer.photographer_id,
                    client_id: client.client_id + 1,
                    location: {
                        city: 'city',
                        country: 'country',
                        line1: 'line1',
                        postal_code: '12345',
                        state: 'state',
                        formatted_address: 'formatted_address'
                    },
                    total_price: 1200,
                    total_revenue: 100,
                    type: 'express',
                    time: {
                        from: Date.now(),
                        duration: 20
                    },
                    notes: 'notes',
                    redeemable_total: null,
                    contact_phone: 'contact_phone',
                    contact_name: 'contact_name',
                    is_payed: false
                }
            };
        }
    });

    test({
        testName: '[failure 400] client creates a custom shoot',
        expectedStatusCode: 400,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const client =  await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const photographer =  await User.query().whereNull('client_id')
                .whereNotNull('photographer_id').orderBy('photographer_id', 'DESC').first();
            const firstService = await Service.query().orderBy('id', 'ASC').first();
            const secondService = await Service.query().orderBy('id', 'DESC').first();

            return {
                method: 'POST',
                url: `/v1/shoot`,
                auth: {
                    credentials: {
                        user: client,
                        tokenPayload: { role: 'client' }
                    }
                },
                payload: {
                    packages: [
                        {
                            service_id: firstService.id,
                            picture_number: 13
                        },
                        {
                            service_id: secondService.id,
                            picture_number: 15
                        }
                    ],
                    photographer_id: photographer.photographer_id,
                    client_id: client.client_id,
                    location: {
                        city: 'city',
                        country: 'country',
                        line1: 'line1',
                        postal_code: '12345',
                        state: 'state',
                        formatted_address: 'formatted_address'
                    },
                    total_price: 1200,
                    type: 'custom',
                    time: {
                        duration: 0
                    },
                    notes: 'notes',
                    redeemable_total: null,
                    contact_phone: 'contact_phone',
                    contact_name: 'contact_name',
                    is_payed: true
                }
            };
        }
    });

    test({
        testName: '[failure 400] client creates a fixed payed shoot',
        expectedStatusCode: 400,
        setupCallback: async ({ server }) => {

            const { User, Package } = server.models();
            const client =  await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const packageTested = await Package.query().first();

            return {
                method: 'POST',
                url: `/v1/shoot`,
                auth: {
                    credentials: {
                        user: client,
                        tokenPayload: { role: 'client' }
                    }
                },
                payload: {
                    packages: [
                        {
                            package_id: packageTested.id
                        }
                    ],
                    location: {
                        city: 'city',
                        country: 'country',
                        line1: 'line1',
                        postal_code: '12345',
                        state: 'state',
                        formatted_address: 'formatted_address'
                    },
                    time: {
                        duration: 0
                    },
                    type: 'express',
                    notes: 'notes',
                    redeemable_total: null,
                    contact_phone: 'contact_phone',
                    contact_name: 'contact_name',
                    is_payed: true
                }
            };
        }
    });

    test({
        testName: '[failure 400] client creates a fixed shoot with photographer assigned',
        expectedStatusCode: 400,
        setupCallback: async ({ server }) => {

            const { User, Package } = server.models();
            const client =  await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const photographer =  await User.query().whereNull('client_id');
            const packageTested = await Package.query().first();

            return {
                method: 'POST',
                url: `/v1/shoot`,
                auth: {
                    credentials: {
                        user: client,
                        tokenPayload: { role: 'client' }
                    }
                },
                payload: {
                    packages: [
                        {
                            package_id: packageTested.id
                        }
                    ],
                    location: {
                        city: 'city',
                        country: 'country',
                        line1: 'line1',
                        postal_code: '12345',
                        state: 'state',
                        formatted_address: 'formatted_address'
                    },
                    photographer: photographer.photographer_id,
                    time: {
                        duration: 0
                    },
                    type: 'express',
                    notes: 'notes',
                    redeemable_total: null,
                    contact_phone: 'contact_phone',
                    contact_name: 'contact_name',
                    is_payed: true
                }
            };
        }
    });

    test({
        testName: '[failure 400] photographer creates a custom shoot',
        expectedStatusCode: 400,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const client =  await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const photographer =  await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const firstService = await Service.query().orderBy('id', 'ASC').first();
            const secondService = await Service.query().orderBy('id', 'DESC').first();

            return {
                method: 'POST',
                url: `/v1/shoot`,
                auth: {
                    credentials: {
                        user: photographer,
                        tokenPayload: { role: 'photographer' }
                    }
                },
                payload: {
                    packages: [
                        {
                            service_id: firstService.id,
                            picture_number: 13
                        },
                        {
                            service_id: secondService.id,
                            picture_number: 15
                        }
                    ],
                    photographer_id: photographer.photographer_id,
                    client_id: client.client_id,
                    type: 'custom',
                    location: {
                        city: 'city',
                        country: 'country',
                        line1: 'line1',
                        postal_code: '12345',
                        state: 'state',
                        formatted_address: 'formatted_address'
                    },
                    total_price: 1200,
                    time: {
                        duration: 0
                    },
                    notes: 'notes',
                    redeemable_total: null,
                    contact_phone: 'contact_phone',
                    contact_name: 'contact_name',
                    is_payed: true
                }
            };
        }
    });

    test({
        testName: '[failure 400] photographer creates a fixed shoot',
        expectedStatusCode: 400,
        setupCallback: async ({ server }) => {

            const { User, Package } = server.models();
            const photographer =  await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const packageTested = await Package.query().first();

            return {
                method: 'POST',
                url: `/v1/shoot`,
                auth: {
                    credentials: {
                        user: photographer,
                        tokenPayload: { role: 'photographer' }
                    }
                },
                payload: {
                    packages: [
                        {
                            package_id: packageTested.id
                        }
                    ],
                    location: {
                        city: 'city',
                        country: 'country',
                        line1: 'line1',
                        postal_code: '12345',
                        state: 'state',
                        formatted_address: 'formatted_address'
                    },
                    time: {
                        duration: 0
                    },
                    type: 'express',
                    notes: 'notes',
                    redeemable_total: null,
                    contact_phone: 'contact_phone',
                    contact_name: 'contact_name'
                }
            };
        }
    });

    test({
        testName: '[success 200] admin creates a custom redeemable (10 times) shoot with no photographer',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const client =  await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const firstService = await Service.query().orderBy('id', 'ASC').first();
            // const secondService = await Service.query().orderBy('id', 'DESC').first();

            return {
                method: 'POST',
                url: `/v1/shoot`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    packages: [
                        {
                            service_id: firstService.id,
                            picture_number: 13,
                            video_number: 0,
                            video_duration: 0
                        }

                    ],
                    photographer_id: null,
                    client_id: client.client_id,
                    location: {
                        city: 'city',
                        country: 'country',
                        line1: 'line1',
                        postal_code: '12345',
                        state: 'state',
                        formatted_address: 'formatted_address'
                    },
                    type: 'custom',
                    total_price: 1200,
                    total_revenue: 100,
                    time: {
                        duration: 20
                    },
                    notes: 'notes',
                    redeemable_total: 10,
                    contact_phone: 'contact_phone',
                    contact_name: 'contact_name',
                    is_payed: true,
                    content: 'photography'
                }
            };
        },
        dataSchema: Joi.object({
            shoot
        }),
        testCallback: ({ data }) => {

            const savedShoot = data.shoot;
            expect(savedShoot.status).to.be.equal(Shoot.statuses.redeemable);
            expect(savedShoot.scheduled).to.be.equal(false);
        }
    });

    test({
        testName: '[success 200] admin creates a custom redeemable (infinite times) shoot with no photographer',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const client =  await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const firstService = await Service.query().orderBy('id', 'ASC').first();
            // const secondService = await Service.query().orderBy('id', 'DESC').first();

            return {
                method: 'POST',
                url: `/v1/shoot`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    packages: [
                        {
                            service_id: firstService.id,
                            picture_number: 13,
                            video_duration: 0,
                            video_number: 0
                        }
                    ],
                    photographer_id: null,
                    client_id: client.client_id,
                    location: {
                        city: 'city',
                        country: 'country',
                        line1: 'line1',
                        postal_code: '12345',
                        state: 'state',
                        formatted_address: 'formatted_address'
                    },
                    type: 'custom',
                    total_price: 1200,
                    total_revenue: 100,
                    time: {
                        duration: 20
                    },
                    notes: 'notes',
                    redeemable_total: -1,
                    contact_phone: 'contact_phone',
                    contact_name: 'contact_name',
                    is_payed: true,
                    content: 'photography'
                }
            };
        },
        dataSchema: Joi.object({
            shoot
        }),
        testCallback: ({ data }) => {

            const savedShoot = data.shoot;
            expect(savedShoot.status).to.be.equal(Shoot.statuses.redeemable);
            expect(savedShoot.scheduled).to.be.equal(false);
        }
    });

    test({
        testName: '[failure 400] client creates a fixed redeemable (infinite times) shoot no scheduled',
        expectedStatusCode: 400,
        setupCallback: async ({ server }) => {

            const { User, Package } = server.models();
            const client =  await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const packageTested = await Package.query().first();

            return {
                method: 'POST',
                url: `/v1/shoot`,
                auth: {
                    credentials: {
                        user: client,
                        tokenPayload: { role: 'client' }
                    }
                },
                payload: {
                    packages: [
                        {
                            package_id: packageTested.id
                        }
                    ],
                    location: {
                        city: 'city',
                        country: 'country',
                        line1: 'line1',
                        postal_code: '12345',
                        state: 'state',
                        formatted_address: 'formatted_address'
                    },
                    time: {
                        duration: 60
                    },
                    type: 'express',
                    notes: 'notes',
                    redeemable_total: -1,
                    contact_phone: 'contact_phone',
                    contact_name: 'contact_name'
                }
            };
        }
    });
});
