'use strict';

const Joi = require('joi');
const { experimentRoute } = require('../../../../lib/helpers/tester');

const { shootDetail } = require('../../../../lib/validation-schema/shoots/responses.js');
const Shoot = require('../../../../lib/models/Shoot');
const { expect } = require('@hapi/code');

exports.lab = experimentRoute('POST "/shoot/{id}/action/assign-photographer"', ({ test }) => {

    test({
        testName: '[success 200] admin assign photographer to scheduled shoot',
        expectedStatusCode: 200,
        options: {
            routeTimeout: 250
        },
        setupCallback: async ({ server, context }) => {

            const { Shoot, User } = server.models();

            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            context.photographer = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const shoot = await Shoot.query().where('status', Shoot.statuses.scheduled).first();

            return {
                method: 'POST',
                url: `/v1/shoot/${shoot.id}/action/assign-photographer`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    photographer_id: context.photographer.photographer_id
                }
            };
        },
        dataSchema: Joi.object({
            shootDetail
        }),
        testCallback: ({ data, context }) => {

            const { shootDetail } = data;
            expect(shootDetail.status).to.be.equal(Shoot.statuses.scheduled);
            expect(shootDetail.photographer_id).to.be.equal(context.photographer.photographer_id);
        }
    });

    test({
        testName: '[success 200] admin assign photographer to shoot with assigned photographer',
        expectedStatusCode: 200,
        setupCallback: async ({ server, context }) => {

            const { Shoot, User } = server.models();

            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            context.photographer = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const shoot = await Shoot.query().where('status', Shoot.statuses.photographerAssigned).first();

            return {
                method: 'POST',
                url: `/v1/shoot/${shoot.id}/action/assign-photographer`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    photographer_id: context.photographer.photographer_id
                }
            };
        },
        dataSchema: Joi.object({
            shootDetail
        }),
        testCallback: ({ data, context }) => {

            const { shootDetail } = data;
            expect(shootDetail.status).to.be.equal(Shoot.statuses.photographerAssigned);
            expect(shootDetail.photographer_id).to.be.equal(context.photographer.photographer_id);
        }
    });

    // test({
    //     testName: '[failure 400] admin assign non existant photographer to scheduled shoot',
    //     expectedStatusCode: 400,
    //     setupCallback: async ({ server, context }) => {

    //         const { Shoot, User } = server.models();

    //         const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
    //         context.photographer = await User.query().whereNull('client_id')
    //             .whereNotNull('photographer_id').orderBy('photographer_id', 'DESC').first();
    //         const shoot = await Shoot.query().where('status', Shoot.statuses.scheduled).first();

    //         return {
    //             method: 'POST',
    //             url: `/v1/shoot/${shoot.id}/action/assign-photographer`,
    //             auth: {
    //                 credentials: {
    //                     user,
    //                     tokenPayload: { role: 'admin' }
    //                 }
    //             },
    //             payload: {
    //                 photographer_id: context.photographer.photographer_id + 1
    //             }
    //         };
    //     }
    // });

    // test({
    //     testName: '[failure 400] admin assign non existant photographer to shoot with assigned photographer',
    //     expectedStatusCode: 400,
    //     setupCallback: async ({ server, context }) => {

    //         const { Shoot, User } = server.models();

    //         const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
    //         context.photographer = await User.query().whereNull('client_id')
    //             .whereNotNull('photographer_id').orderBy('photographer_id', 'DESC').first();
    //         const shoot = await Shoot.query().where('status', Shoot.statuses.photographerAssigned).first();

    //         return {
    //             method: 'POST',
    //             url: `/v1/shoot/${shoot.id}/action/assign-photographer`,
    //             auth: {
    //                 credentials: {
    //                     user,
    //                     tokenPayload: { role: 'admin' }
    //                 }
    //             },
    //             payload: {
    //                 photographer_id: context.photographer.photographer_id + 1
    //             }
    //         };
    //     }
    // });

    // test({
    //     testName: '[failure 401] client assign photographer',
    //     expectedStatusCode: 401,
    //     options: { skip: true }, // NOTE: we skip this as it's failing (if the client is not connected to the shoot, it gets a 404)
    //     setupCallback: async ({ server, context }) => {

    //         const { Shoot, User } = server.models();

    //         const user = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
    //         const photographer = await User.query().whereNull('client_id')
    //             .whereNotNull('photographer_id').orderBy('photographer_id', 'DESC').first();
    //         const shoot = await Shoot.query().where('status', Shoot.statuses.photographerAssigned).first();

    //         return {
    //             method: 'POST',
    //             url: `/v1/shoot/${shoot.id}/action/assign-photographer`,
    //             auth: {
    //                 credentials: {
    //                     user,
    //                     tokenPayload: { role: 'client' }
    //                 }
    //             },
    //             payload: {
    //                 photographer_id: photographer.photographer_id
    //             }
    //         };
    //     }
    // });

    // test({
    //     testName: '[failure 401] photographer assign photographer',
    //     expectedStatusCode: 401,
    //     options: { skip: true }, // NOTE: we skip this as it's failing (if the photographer is not already assigned to the shoot, it gets a 404)
    //     setupCallback: async ({ server, context }) => {

    //         const { Shoot, User } = server.models();

    //         const photographer = await User.query().whereNull('client_id')
    //             .whereNotNull('photographer_id').first();
    //         const shoot = await Shoot.query().where('status', Shoot.statuses.photographerAssigned).first();

    //         return {
    //             method: 'POST',
    //             url: `/v1/shoot/${shoot.id}/action/assign-photographer`,
    //             auth: {
    //                 credentials: {
    //                     user: photographer,
    //                     tokenPayload: { role: 'photographer' }
    //                 }
    //             },
    //             payload: {
    //                 photographer_id: photographer.photographer_id
    //             }
    //         };
    //     }
    // });
});
