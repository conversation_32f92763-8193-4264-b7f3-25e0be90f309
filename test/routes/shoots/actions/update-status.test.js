'use strict';

const Joi = require('joi');
const { experimentRoute } = require('../../../../lib/helpers/tester');

const { shootDetail } = require('../../../../lib/validation-schema/shoots/responses.js');
const Shoot = require('../../../../lib/models/Shoot');
const { expect } = require('@hapi/code');

exports.lab = experimentRoute('POST "/shoot/{id}/action/update-status"', ({ test }) => {

    test({
        testName: '[success 200] admin updates status from "toSchedule" to "scheduled"',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const { shootService } = server.services();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            //CREATE SHOOT USING SERVICES
            const photographer = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const client = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const serviceTested = await Service.query().orderBy('id', 'DESC').first();
            const customPackage = [{ service_id: serviceTested.id,
                picture_number: 13,
                package: {
                    service_id: serviceTested.id,
                    picture_number: 13,
                    name: 'custom' }
            }];

            const { savedShoot } = await shootService.createShoot({
                packages: customPackage,
                photographer_id: photographer.photographer_id,
                client_id: client.client_id,
                location: {
                    city: 'city',
                    country: 'country',
                    line1: 'line1',
                    postal_code: '12345',
                    state: 'state'
                },
                price: 1200,
                photographer_revenue: 100,
                time: {},
                duration: 20,
                notes: 'notes',
                contact_phone: 'contact_phone',
                contact_name: 'contact_name',
                is_payed: true,
                role: 'admin',
                user
            });

            //PERFORM UPDATE THAT TRIGGERS STATUS CHANGEMENT - TIME ASSIGNMENT
            await shootService.updateShoot({
                shoot: savedShoot,
                user,
                role: 'admin',
                newValues: {
                    datetime: Date.now(),

                    scheduled: true
                }
            });

            const { shoot } = await shootService.getShoot({ id: savedShoot.id, user, role: 'admin' });

            return {
                method: 'POST',
                url: `/v1/shoot/${shoot.id}/action/update-status`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    targetStatus: Shoot.statuses.scheduled
                }
            };
        },
        dataSchema: Joi.object({
            shootDetail
        }),
        testCallback: ({ data }) => {

            const { shootDetail } = data;
            expect(shootDetail.status).to.be.equal(Shoot.statuses.scheduled);
            expect(shootDetail.scheduled).to.be.equal(true);
        }
    });

    test({
        testName: '[success 200] admin updates status from "scheduled" to "assigned"',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const { shootService } = server.services();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            //CREATE SHOOT USING SERVICES
            const photographer = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const client = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const serviceTested = await Service.query().orderBy('id', 'DESC').first();
            const customPackage = [{ service_id: serviceTested.id,
                picture_number: 13,
                package: {
                    service_id: serviceTested.id,
                    picture_number: 13,
                    name: 'custom' }
            }];

            const { savedShoot } = await shootService.createShoot({
                packages: customPackage,
                photographer_id: null,
                client_id: client.client_id,
                location: {
                    city: 'city',
                    country: 'country',
                    line1: 'line1',
                    postal_code: '12345',
                    state: 'state'
                },
                price: 1200,
                photographer_revenue: 100,
                time: {
                    from: Date.now(),
                    to: new Date(Date.now() + 2 * (60 * 60 * 1000))
                },
                duration: 20,
                notes: 'notes',
                contact_phone: 'contact_phone',
                contact_name: 'contact_name',
                is_payed: true,
                role: 'admin',
                user
            });

            //PERFORM UPDATE THAT TRIGGERS STATUS CHANGEMENT - PHOTOGRAPHER ASSIGNMENT
            await shootService.updateShoot({
                shoot: savedShoot,
                user,
                role: 'admin',
                newValues: {
                    photographer_id: photographer.photographer_id
                }
            });
            const { shoot } = await shootService.getShoot({ id: savedShoot.id, user, role: 'admin' });

            return {
                method: 'POST',
                url: `/v1/shoot/${shoot.id}/action/update-status`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    targetStatus: Shoot.statuses.photographerAssigned
                }
            };
        },
        dataSchema: Joi.object({
            shootDetail
        }),
        testCallback: ({ data }) => {

            const { shootDetail } = data;
            expect(shootDetail.status).to.be.equal(Shoot.statuses.photographerAssigned);
        }
    });

    test({
        testName: '[success 200] admin updates status from "assigned" to "confirmed"',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const { shootService } = server.services();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            //CREATE SHOOT USING SERVICES
            const photographer = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const client = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const serviceTested = await Service.query().orderBy('id', 'DESC').first();
            const customPackage = [{ service_id: serviceTested.id,
                picture_number: 13,
                package: {
                    service_id: serviceTested.id,
                    picture_number: 13,
                    name: 'custom' }
            }];

            const { savedShoot } = await shootService.createShoot({
                packages: customPackage,
                photographer_id: photographer.photographer_id,
                client_id: client.client_id,
                location: {
                    city: 'city',
                    country: 'country',
                    line1: 'line1',
                    postal_code: '12345',
                    state: 'state'
                },
                price: 1200,
                photographer_revenue: 100,
                time: {
                    from: Date.now(),
                    to: new Date(Date.now() + 2 * (60 * 60 * 1000))
                },
                duration: 20,
                notes: 'notes',
                contact_phone: 'contact_phone',
                contact_name: 'contact_name',
                is_payed: true,
                role: 'admin',
                user
            });

            //NO UPDATE NEEDED

            const { shoot } = await shootService.getShoot({ id: savedShoot.id, user, role: 'admin' });

            return {
                method: 'POST',
                url: `/v1/shoot/${shoot.id}/action/update-status`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    targetStatus: Shoot.statuses.confirmed
                }
            };
        },
        dataSchema: Joi.object({
            shootDetail
        }),
        testCallback: ({ data }) => {

            const { shootDetail } = data;
            expect(shootDetail.status).to.be.equal(Shoot.statuses.confirmed);
        }
    });

    test({
        testName: '[success 200] admin updates status from "scheduled" to "canceled"',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const { shootService } = server.services();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            //CREATE SHOOT USING SERVICES
            const client = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const serviceTested = await Service.query().orderBy('id', 'DESC').first();
            const customPackage = [{ service_id: serviceTested.id,
                picture_number: 13,
                package: {
                    service_id: serviceTested.id,
                    picture_number: 13,
                    name: 'custom' }
            }];

            const { savedShoot } = await shootService.createShoot({
                packages: customPackage,
                photographer_id: null,
                client_id: client.client_id,
                location: {
                    city: 'city',
                    country: 'country',
                    line1: 'line1',
                    postal_code: '12345',
                    state: 'state'
                },
                price: 1200,
                photographer_revenue: 100,
                time: {
                    from: Date.now(),
                    to: new Date(Date.now() + 2 * (60 * 60 * 1000))
                },
                duration: 20,
                notes: 'notes',
                contact_phone: 'contact_phone',
                contact_name: 'contact_name',
                is_payed: true,
                role: 'admin',
                user
            });

            //NO UPDATE NEEDED

            const { shoot } = await shootService.getShoot({ id: savedShoot.id, user, role: 'admin' });

            return {
                method: 'POST',
                url: `/v1/shoot/${shoot.id}/action/update-status`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    targetStatus: Shoot.statuses.canceled
                }
            };
        },
        dataSchema: Joi.object({
            shootDetail
        }),
        testCallback: ({ data }) => {

            const { shootDetail } = data;
            expect(shootDetail.status).to.be.equal(Shoot.statuses.canceled);
        }
    });

    test({
        testName: '[success 200] photographer updates status from "assigned" to "confirmed"',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Service, Shoot } = server.models();
            const { shootService } = server.services();

            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            //CREATE SHOOT USING SERVICES
            const photographer = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const client = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const serviceTested = await Service.query().orderBy('id', 'DESC').first();
            const customPackage = [{ service_id: serviceTested.id,
                picture_number: 13,
                package: {
                    service_id: serviceTested.id,
                    picture_number: 13,
                    name: 'custom' }
            }];

            const { max: maxStartTime } = await Shoot.query().max('datetime').first();
            const startTime = new Date(maxStartTime).getTime() + 24 * (60 * 60 * 1000); // One day after, to ensure no overlap

            const { savedShoot } = await shootService.createShoot({
                packages: customPackage,
                photographer_id: photographer.photographer_id,
                client_id: client.client_id,
                location: {
                    city: 'city',
                    country: 'country',
                    line1: 'line1',
                    postal_code: '12345',
                    state: 'state'
                },
                price: 1200,
                photographer_revenue: 100,
                time: {
                    from: new Date(startTime),
                    to: new Date(startTime + 60 * 60 * 1000) // One hour duration
                },
                duration: 20,
                notes: 'notes',
                contact_phone: 'contact_phone',
                contact_name: 'contact_name',
                is_payed: true,
                role: 'admin',
                user,
                content: 'photography',
                video_number: 0,
                video_duration: 0
            });
            //NO UPDATE NEEDED

            const { shoot } = await shootService.getShoot({ id: savedShoot.id, user: photographer, role: 'photographer' });

            // This is to make sure that if we fail to request a shoot that can be correctly confirmed by the photographer,
            // then we do not fail the test but rather fail the setup (since the problem is in the test and not in the application code)
            const { count: overlappingShootsAmount } = await server.services().shootService.getOverlappingShoots({ shoot }).count().first();
            expect(parseInt(overlappingShootsAmount)).to.be.equal(0);

            return {
                method: 'POST',
                url: `/v1/shoot/${shoot.id}/action/update-status`,
                auth: {
                    credentials: {
                        user: photographer,
                        tokenPayload: { role: 'photographer' }
                    }
                },
                payload: {
                    targetStatus: Shoot.statuses.confirmed
                }
            };
        },
        dataSchema: Joi.object({
            shootDetail
        }),
        testCallback: ({ data }) => {

            const { shootDetail } = data;
            expect(shootDetail.status).to.be.equal(Shoot.statuses.confirmed);
        }
    });

    test({
        testName: '[success 200] admin updates status from "toSchedule" to "canceled"',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const { shootService } = server.services();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            //CREATE SHOOT USING SERVICES
            const photographer = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const client = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const serviceTested = await Service.query().orderBy('id', 'DESC').first();
            const customPackage = [{ service_id: serviceTested.id,
                picture_number: 13,
                package: {
                    service_id: serviceTested.id,
                    picture_number: 13,
                    name: 'custom' }
            }];

            const { savedShoot } = await shootService.createShoot({
                packages: customPackage,
                photographer_id: photographer.photographer_id,
                client_id: client.client_id,
                location: {
                    city: 'city',
                    country: 'country',
                    line1: 'line1',
                    postal_code: '12345',
                    state: 'state'
                },
                price: 1200,
                photographer_revenue: 100,
                time: {},
                duration: 20,
                notes: 'notes',
                contact_phone: 'contact_phone',
                contact_name: 'contact_name',
                is_payed: true,
                role: 'admin',
                user
            });

            //NO UPDATE NEEDED

            const { shoot } = await shootService.getShoot({ id: savedShoot.id, user, role: 'admin' });

            return {
                method: 'POST',
                url: `/v1/shoot/${shoot.id}/action/update-status`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    targetStatus: Shoot.statuses.canceled
                }
            };
        },
        dataSchema: Joi.object({
            shootDetail
        }),
        testCallback: ({ data }) => {

            const { shootDetail } = data;
            expect(shootDetail.status).to.be.equal(Shoot.statuses.canceled);
        }
    });

    test({
        testName: '[success 200] client updates status from "ready" to "completed"',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const { shootService } = server.services();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            //CREATE SHOOT USING SERVICES
            const photographer = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const client = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const serviceTested = await Service.query().orderBy('id', 'DESC').first();
            const customPackage = [{ service_id: serviceTested.id,
                picture_number: 13,
                package: {
                    service_id: serviceTested.id,
                    picture_number: 13,
                    name: 'custom' }
            }];

            const { savedShoot } = await shootService.createShoot({
                packages: customPackage,
                photographer_id: photographer.photographer_id,
                client_id: client.client_id,
                location: {
                    city: 'city',
                    country: 'country',
                    line1: 'line1',
                    postal_code: '12345',
                    state: 'state'
                },
                price: 1200,
                photographer_revenue: 100,
                time: {
                    from: Date.now(),
                    to: new Date(Date.now() + 2 * (60 * 60 * 1000))
                },
                duration: 20,
                notes: 'notes',
                contact_phone: 'contact_phone',
                contact_name: 'contact_name',
                is_payed: true,
                role: 'admin',
                user
            });

            //PERFORM UPDATE THAT CONFIRMS PHOTOGRAPHER
            await shootService.updateShoot({
                shoot: savedShoot,
                user,
                role: 'admin',
                newValues: {
                    status: Shoot.statuses.photosReady
                }
            });

            const { shoot } = await shootService.getShoot({ id: savedShoot.id, user: photographer, role: 'photographer' });

            return {
                method: 'POST',
                url: `/v1/shoot/${shoot.id}/action/update-status`,
                auth: {
                    credentials: {
                        user: client,
                        tokenPayload: { role: 'client' }
                    }
                },
                payload: {
                    targetStatus: Shoot.statuses.completed
                }
            };
        },
        dataSchema: Joi.object({
            shootDetail
        }),
        testCallback: ({ data }) => {

            const { shootDetail } = data;
            expect(shootDetail.status).to.be.equal(Shoot.statuses.completed);
        }
    });

    test({
        testName: '[success 200] admin updates status from "assigned" to "canceled"',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const { shootService } = server.services();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            //CREATE SHOOT USING SERVICES
            const photographer = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const client = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const serviceTested = await Service.query().orderBy('id', 'DESC').first();
            const customPackage = [{ service_id: serviceTested.id,
                picture_number: 13,
                package: {
                    service_id: serviceTested.id,
                    picture_number: 13,
                    name: 'custom' }
            }];

            const { savedShoot } = await shootService.createShoot({
                packages: customPackage,
                photographer_id: photographer.photographer_id,
                client_id: client.client_id,
                location: {
                    city: 'city',
                    country: 'country',
                    line1: 'line1',
                    postal_code: '12345',
                    state: 'state'
                },
                price: 1200,
                photographer_revenue: 100,
                time: {
                    from: Date.now(),
                    to: new Date(Date.now() + 2 * (60 * 60 * 1000))
                },
                duration: 20,
                notes: 'notes',
                contact_phone: 'contact_phone',
                contact_name: 'contact_name',
                is_payed: true,
                role: 'admin',
                user
            });

            //NO UPDATE NEEDED

            const { shoot } = await shootService.getShoot({ id: savedShoot.id, user, role: 'admin' });

            return {
                method: 'POST',
                url: `/v1/shoot/${shoot.id}/action/update-status`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    targetStatus: Shoot.statuses.canceled
                }
            };
        },
        dataSchema: Joi.object({
            shootDetail
        }),
        testCallback: ({ data }) => {

            const { shootDetail } = data;
            expect(shootDetail.status).to.be.equal(Shoot.statuses.canceled);
        }
    });

    test({
        testName: '[success 200] admin updates status from "confirmed" to "canceled"',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const { shootService } = server.services();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            //CREATE SHOOT USING SERVICES
            const photographer = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const client = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const serviceTested = await Service.query().orderBy('id', 'DESC').first();
            const customPackage = [{ service_id: serviceTested.id,
                picture_number: 13,
                package: {
                    service_id: serviceTested.id,
                    picture_number: 13,
                    name: 'custom' }
            }];

            const { savedShoot } = await shootService.createShoot({
                packages: customPackage,
                photographer_id: photographer.photographer_id,
                client_id: client.client_id,
                location: {
                    city: 'city',
                    country: 'country',
                    line1: 'line1',
                    postal_code: '12345',
                    state: 'state'
                },
                price: 1200,
                photographer_revenue: 100,
                time: {
                    from: Date.now(),
                    to: new Date(Date.now() + 2 * (60 * 60 * 1000))
                },
                duration: 20,
                notes: 'notes',
                contact_phone: 'contact_phone',
                contact_name: 'contact_name',
                is_payed: true,
                role: 'admin',
                user
            });

            //PERFORM UPDATE THAT CONFIRMS PHOTOGRAPHER
            await shootService.updateShoot({
                shoot: savedShoot,
                user,
                role: 'admin',
                newValues: {
                    status: Shoot.statuses.confirmed
                }
            });

            const { shoot } = await shootService.getShoot({ id: savedShoot.id, user: photographer, role: 'photographer' });

            return {
                method: 'POST',
                url: `/v1/shoot/${shoot.id}/action/update-status`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    targetStatus: Shoot.statuses.canceled
                }
            };
        },
        dataSchema: Joi.object({
            shootDetail
        }),
        testCallback: ({ data }) => {

            const { shootDetail } = data;
            expect(shootDetail.status).to.be.equal(Shoot.statuses.canceled);
        }
    });

    test({
        testName: '[failure 401] client updates status from "confirmed" to "canceled"',
        expectedStatusCode: 401,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const { shootService } = server.services();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            //CREATE SHOOT USING SERVICES
            const photographer = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const client = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const serviceTested = await Service.query().orderBy('id', 'DESC').first();
            const customPackage = [{ service_id: serviceTested.id,
                picture_number: 13,
                package: {
                    service_id: serviceTested.id,
                    picture_number: 13,
                    name: 'custom' }
            }];

            const { savedShoot } = await shootService.createShoot({
                packages: customPackage,
                photographer_id: photographer.photographer_id,
                client_id: client.client_id,
                location: {
                    city: 'city',
                    country: 'country',
                    line1: 'line1',
                    postal_code: '12345',
                    state: 'state'
                },
                price: 1200,
                photographer_revenue: 100,
                time: {
                    from: Date.now(),
                    to: new Date(Date.now() + 2 * (60 * 60 * 1000))
                },
                duration: 20,
                notes: 'notes',
                contact_phone: 'contact_phone',
                contact_name: 'contact_name',
                is_payed: true,
                role: 'admin',
                user
            });

            //PERFORM UPDATE THAT CONFIRMS PHOTOGRAPHER
            await shootService.updateShoot({
                shoot: savedShoot,
                user,
                role: 'admin',
                newValues: {
                    status: Shoot.statuses.confirmed
                }
            });

            const { shoot } = await shootService.getShoot({ id: savedShoot.id, user: photographer, role: 'photographer' });

            return {
                method: 'POST',
                url: `/v1/shoot/${shoot.id}/action/update-status`,
                auth: {
                    credentials: {
                        user: client,
                        tokenPayload: { role: 'client' }
                    }
                },
                payload: {
                    targetStatus: Shoot.statuses.canceled
                }
            };
        }
    });

    test({
        testName: '[failure 401] client updates status from "assigned" to "canceled"',
        expectedStatusCode: 401,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const { shootService } = server.services();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            //CREATE SHOOT USING SERVICES
            const photographer = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const client = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const serviceTested = await Service.query().orderBy('id', 'DESC').first();
            const customPackage = [{ service_id: serviceTested.id,
                picture_number: 13,
                package: {
                    service_id: serviceTested.id,
                    picture_number: 13,
                    name: 'custom' }
            }];

            const { savedShoot } = await shootService.createShoot({
                packages: customPackage,
                photographer_id: photographer.photographer_id,
                client_id: client.client_id,
                location: {
                    city: 'city',
                    country: 'country',
                    line1: 'line1',
                    postal_code: '12345',
                    state: 'state'
                },
                price: 1200,
                photographer_revenue: 100,
                time: {
                    from: Date.now(),
                    to: new Date(Date.now() + 2 * (60 * 60 * 1000))
                },
                duration: 20,
                notes: 'notes',
                contact_phone: 'contact_phone',
                contact_name: 'contact_name',
                is_payed: true,
                role: 'admin',
                user
            });

            //NO UPDATE NEEDED

            const { shoot } = await shootService.getShoot({ id: savedShoot.id, user, role: 'admin' });

            return {
                method: 'POST',
                url: `/v1/shoot/${shoot.id}/action/update-status`,
                auth: {
                    credentials: {
                        user: client,
                        tokenPayload: { role: 'client' }
                    }
                },
                payload: {
                    targetStatus: Shoot.statuses.canceled
                }
            };
        }
    });

    test({
        testName: '[failure 401] photographer updates status from "ready" to "completed"',
        expectedStatusCode: 401,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const { shootService } = server.services();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            //CREATE SHOOT USING SERVICES
            const photographer = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const client = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const serviceTested = await Service.query().orderBy('id', 'DESC').first();
            const customPackage = [{ service_id: serviceTested.id,
                picture_number: 13,
                package: {
                    service_id: serviceTested.id,
                    picture_number: 13,
                    name: 'custom' }
            }];

            const { savedShoot } = await shootService.createShoot({
                packages: customPackage,
                photographer_id: photographer.photographer_id,
                client_id: client.client_id,
                location: {
                    city: 'city',
                    country: 'country',
                    line1: 'line1',
                    postal_code: '12345',
                    state: 'state'
                },
                price: 1200,
                photographer_revenue: 100,
                time: {
                    from: Date.now(),
                    to: new Date(Date.now() + 2 * (60 * 60 * 1000))
                },
                duration: 20,
                notes: 'notes',
                contact_phone: 'contact_phone',
                contact_name: 'contact_name',
                is_payed: true,
                role: 'admin',
                user
            });

            //PERFORM UPDATE THAT CONFIRMS PHOTOGRAPHER
            await shootService.updateShoot({
                shoot: savedShoot,
                user,
                role: 'admin',
                newValues: {
                    status: Shoot.statuses.photosReady
                }
            });

            const { shoot } = await shootService.getShoot({ id: savedShoot.id, user: photographer, role: 'photographer' });

            return {
                method: 'POST',
                url: `/v1/shoot/${shoot.id}/action/update-status`,
                auth: {
                    credentials: {
                        user: photographer,
                        tokenPayload: { role: 'photographer' }
                    }
                },
                payload: {
                    targetStatus: Shoot.statuses.completed
                }
            };
        }
    });

    test({
        testName: '[success 200] matched photographer updates status from "scheduled" to "confirmed"',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Service, PhotographerShoot } = server.models();
            const { shootService } = server.services();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            //CREATE SHOOT USING SERVICES
            const client = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const photographer = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();

            const serviceTested = await Service.query().orderBy('id', 'DESC').first();
            const customPackage = [{ service_id: serviceTested.id,
                picture_number: 13,
                package: {
                    service_id: serviceTested.id,
                    picture_number: 13,
                    name: 'custom' }
            }];

            const { savedShoot } = await shootService.createShoot({
                packages: customPackage,
                photographer_id: null,
                client_id: client.client_id,
                location: {
                    city: 'city',
                    country: 'country',
                    line1: 'line1',
                    postal_code: '12345',
                    state: 'state'
                },
                price: 1200,
                photographer_revenue: 100,
                time: {
                    from: Date.now(),
                    to: new Date(Date.now() + 2 * (60 * 60 * 1000))
                },
                duration: 20,
                notes: 'notes',
                contact_phone: 'contact_phone',
                contact_name: 'contact_name',
                is_payed: true,
                role: 'admin',
                user
            });
            await PhotographerShoot.query().insert({ shoot_id: savedShoot.id, photographer_id: photographer.photographer_id });

            const { shoot } = await shootService.getShoot({ id: savedShoot.id, user: photographer, role: 'photographer' });
            return {
                method: 'POST',
                url: `/v1/shoot/${shoot.id}/action/update-status`,
                auth: {
                    credentials: {
                        user: photographer,
                        tokenPayload: { role: 'photographer' }
                    }
                },
                payload: {
                    targetStatus: Shoot.statuses.confirmed
                }
            };
        },
        dataSchema: Joi.object({
            shootDetail
        }),
        testCallback: ({ data }) => {

            const { shootDetail } = data;
            expect(shootDetail.status).to.be.equal(Shoot.statuses.confirmed);
        }
    });

    test({
        testName: '[failure 401] matched photographer updates status from "assigned" to "confirmed"',
        expectedStatusCode: 401,
        setupCallback: async ({ server }) => {

            const { User, Service, PhotographerShoot } = server.models();
            const { shootService } = server.services();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            //CREATE SHOOT USING SERVICES
            const client = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const photographer = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();

            const serviceTested = await Service.query().orderBy('id', 'DESC').first();
            const customPackage = [{ service_id: serviceTested.id,
                picture_number: 13,
                package: {
                    service_id: serviceTested.id,
                    picture_number: 13,
                    name: 'custom' }
            }];

            const { savedShoot } = await shootService.createShoot({
                packages: customPackage,
                photographer_id: null,
                client_id: client.client_id,
                location: {
                    city: 'city',
                    country: 'country',
                    line1: 'line1',
                    postal_code: '12345',
                    state: 'state'
                },
                price: 1200,
                photographer_revenue: 100,
                time: {
                    from: Date.now(),
                    to: new Date(Date.now() + 2 * (60 * 60 * 1000))
                },
                duration: 20,
                notes: 'notes',
                contact_phone: 'contact_phone',
                contact_name: 'contact_name',
                is_payed: true,
                role: 'admin',
                user,
                content: 'photography',
                video_number: 0,
                video_duration: 0
            });
            await PhotographerShoot.query().insert({ shoot_id: savedShoot.id, photographer_id: photographer.photographer_id });
            const { shoot } = await shootService.getShoot({ id: savedShoot.id, user: photographer, role: 'photographer' });

            return {
                method: 'POST',
                url: `/v1/shoot/${shoot.id}/action/update-status`,
                auth: {
                    credentials: {
                        user: photographer,
                        tokenPayload: { role: 'photographer' }
                    }
                },
                payload: {
                    targetStatus: Shoot.statuses.completed
                }
            };
        }
    });

    // test({
    //     testName: '[failure 401] photographer updates status from "assigned" to "confirmed" (overlapping schedule)',
    //     expectedStatusCode: 401,
    //     options: { skip: true }, //TODO: temporarily disabled
    //     setupCallback: async ({ server }) => {

    //         const { User, Service, Shoot } = server.models();
    //         const { shootService } = server.services();

    //         const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

    //         //CREATE SHOOT USING SERVICES
    //         const photographer = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
    //         const client = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
    //         const serviceTested = await Service.query().orderBy('id', 'DESC').first();
    //         const customPackage = [{ service_id: serviceTested.id,
    //             picture_number: 13,
    //             package: {
    //                 service_id: serviceTested.id,
    //                 picture_number: 13,
    //                 name: 'custom' }
    //         }];

    //         const overlappedShoot = await Shoot.query().where('status', Shoot.statuses.confirmed)
    //             .where('photographer_id', photographer.photographer_id).first();
    //         const startTime = new Date(overlappedShoot.datetime).getTime() - 60 * 1000; // One minute before, to ensure overlap

    //         const { savedShoot } = await shootService.createShoot({
    //             packages: customPackage,
    //             photographer_id: photographer.photographer_id,
    //             client_id: client.client_id,
    //             location: {
    //                 city: 'city',
    //                 country: 'country',
    //                 line1: 'line1',
    //                 postal_code: '12345',
    //                 state: 'state'
    //             },
    //             price: 1200,
    //             photographer_revenue: 100,
    //             time: {
    //                 from: new Date(startTime),
    //                 to: new Date(startTime + 60 * 60 * 1000) // One hour duration
    //             },
    //             duration: 20,
    //             notes: 'notes',
    //             contact_phone: 'contact_phone',
    //             contact_name: 'contact_name',
    //             is_payed: true,
    //             role: 'admin',
    //             user,
    //             content: 'photography',
    //             video_number: 0,
    //             video_duration: 0
    //         });
    //         //NO UPDATE NEEDED

    //         const { shoot } = await shootService.getShoot({ id: savedShoot.id, user: photographer, role: 'photographer' });

    //         // This is to make sure that if we fail to request a shoot that overlaps some other ones (by the same photographer),
    //         // then we do not fail the test but rather fail the setup (since the problem is in the test and not in the application code)
    //         const { count: overlappingShootsAmount } = await server.services().shootService.getOverlappingShoots({ shoot }).count().first();
    //         expect(parseInt(overlappingShootsAmount)).to.be.greaterThan(0);

    //         return {
    //             method: 'POST',
    //             url: `/v1/shoot/${shoot.id}/action/update-status`,
    //             auth: {
    //                 credentials: {
    //                     user: photographer,
    //                     tokenPayload: { role: 'photographer' }
    //                 }
    //             },
    //             payload: {
    //                 targetStatus: Shoot.statuses.confirmed
    //             }
    //         };
    //     }
    // });
});
