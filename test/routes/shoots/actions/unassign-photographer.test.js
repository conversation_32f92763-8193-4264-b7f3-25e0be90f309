'use strict';

const Joi = require('joi');
const { experimentRoute } = require('../../../../lib/helpers/tester');

const { shootDetail } = require('../../../../lib/validation-schema/shoots/responses.js');
const Shoot = require('../../../../lib/models/Shoot');
const { expect } = require('@hapi/code');

const buildTestData = ({ user = null, shoot = null }) => {

    expect(user, 'User not found in db').to.not.be.null();
    expect(shoot, 'Shoot not found in db').to.not.be.null();

    let role = 'admin';

    if (user.photographer_id !== null) {
        role = 'photographer';
    }
    else if (user.client_id !== null) {
        role = 'client';
    }

    return {
        method: 'POST',
        url: `/v1/shoot/${shoot.id}/action/unassign-photographer`,
        auth: {
            credentials: {
                user,
                tokenPayload: { role }
            }
        }
    };
};

exports.lab = experimentRoute('POST "/shoot/{id}/action/unassign-photographer"', ({ test }) => {

    test({
        testName: '[success 200] admin unassign photographer - assigned',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { Shoot, User } = server.models();

            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const shoot = await Shoot.query().where('status', Shoot.statuses.photographerAssigned).first();

            return buildTestData({ user, shoot });
        },
        dataSchema: Joi.object({
            shootDetail
        }),
        testCallback: ({ data }) => {

            const { shootDetail } = data;
            expect(shootDetail.status).to.be.equal(Shoot.statuses.scheduled);
            expect(shootDetail.photographer_id).to.be.null();
        }
    });

    test({
        testName: '[success 200] photographer unassign itself - assigned',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { Shoot, User } = server.models();

            const shoot = await Shoot.query().where('status', Shoot.statuses.photographerAssigned).first();
            const user = await User.query().whereNull('client_id').whereNotNull('photographer_id')
                .where('photographer_id', shoot.photographer_id).first();

            return buildTestData({ user, shoot });
        },
        dataSchema: Joi.object({
            shootDetail
        }),
        testCallback: ({ data }) => {

            const { shootDetail } = data;
            expect(shootDetail.status).to.be.equal(Shoot.statuses.scheduled);
            expect(shootDetail.photographer_id).to.be.null();
        }
    });

    test({
        testName: '[failure 401] client unassign photographer  - assigned',
        expectedStatusCode: 401,
        //options: { skip: true }, // NOTE: we skip this as it's failing (if the client is not connected to the shoot, it gets a 404)
        setupCallback: async ({ server, context }) => {
            // Changed

            const { Shoot, User } = server.models();

            const shoot = await Shoot.query().where('status', Shoot.statuses.photographerAssigned).first();
            const order = await shoot.$relatedQuery('order');
            const user = await User.query().where('client_id', order.client_id).whereNull('photographer_id').first();

            return buildTestData({ user, shoot });
        }
    });

    test({
        testName: '[success 200] admin unassign photographer - confirmed',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { Shoot, User } = server.models();

            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const shoot = await Shoot.query().where('status', Shoot.statuses.confirmed).first();

            return buildTestData({ user, shoot });
        },
        dataSchema: Joi.object({
            shootDetail
        }),
        testCallback: ({ data }) => {

            const { shootDetail } = data;
            expect(shootDetail.status).to.be.equal(Shoot.statuses.scheduled);
            expect(shootDetail.photographer_id).to.be.null();
        }
    });

    test({
        testName: '[success 200] photographer unassign itself - confirmed',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { Shoot, User } = server.models();

            const shoot = await Shoot.query().where('status', Shoot.statuses.confirmed).first();
            const user = await User.query().whereNull('client_id').whereNotNull('photographer_id')
                .where('photographer_id', shoot.photographer_id).first();

            return buildTestData({ user, shoot });
        },
        dataSchema: Joi.object({
            shootDetail
        }),
        testCallback: ({ data }) => {

            const { shootDetail } = data;
            expect(shootDetail.status).to.be.equal(Shoot.statuses.scheduled);
            expect(shootDetail.photographer_id).to.be.null();
        }
    });

    test({
        testName: '[failure 401] client unassign photographer  - confirmed',
        expectedStatusCode: 401,
        //options: { skip: true }, // NOTE: we skip this as it's failing (if the client is not connected to the shoot, it gets a 404)
        setupCallback: async ({ server, context }) => {

            const { Shoot, User } = server.models();

            const shoot = await Shoot.query().where('status', Shoot.statuses.confirmed).first();
            const order = await shoot.$relatedQuery('order');
            const user = await User.query().where('client_id', order.client_id).whereNull('photographer_id').first();

            return buildTestData({ user, shoot });
        }
    });

    test({
        testName: '[failure 404] photographer unassign from a shoot with a different photographer assigned',
        expectedStatusCode: 404,
        setupCallback: async ({ server }) => {

            const { Shoot, User } = server.models();

            const shoot = await Shoot.query().where('status', Shoot.statuses.photographerAssigned).first();
            const user = await User.query().whereNull('client_id').whereNotNull('photographer_id')
                .whereNot('photographer_id', shoot.photographer_id).first();

            return buildTestData({ user, shoot });
        }
    });
});
