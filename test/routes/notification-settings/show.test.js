'use strict';

const { experimentRoute } = require('../../../lib/helpers/tester');

const { settings } = require('../../../lib/validation-schema/notification-settings/responses');

exports.lab = experimentRoute('GET "/notificationsettings"', ({ test }) => {

    test({
        testName: '[success 200] admin show notification settings',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            return {
                method: 'GET',
                url: `/v1/notificationsettings`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                }
            };
        },
        dataSchema: settings
    });

    test({
        testName: '[success 401] client show notification settings',
        expectedStatusCode: 401,
        setupCallback: async ({ server }) => {

            const { User } = server.models();
            const user = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();

            return {
                method: 'GET',
                url: `/v1/notificationsettings`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'client' }
                    }
                }
            };
        }
    });

    test({
        testName: '[success 401] photographer show notification settings',
        expectedStatusCode: 401,
        setupCallback: async ({ server }) => {

            const { User } = server.models();
            const user = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();

            return {
                method: 'GET',
                url: `/v1/notificationsettings`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'photographer' }
                    }
                }
            };
        }

    });
});
