'use strict';

const { experimentRoute } = require('../../../lib/helpers/tester');
const { expect } = require('@hapi/code');
const Joi = require('joi');

exports.lab = experimentRoute('POST "/notificationsettings/{id}/update"', ({ test }) => {

    test({
        testName: '[success 200] admin update parent notification settings to false',
        expectedStatusCode: 200,
        setupCallback: async ({ server, context }) => {

            const { User, NotificationSettings } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const setting = await NotificationSettings.query().whereNull('parent_id').first();

            context.obj = setting;
            const status = false;

            return {
                method: 'POST',
                url: `/v1/notificationsettings/${setting.id}/update`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    status
                }
            };
        },
        dataSchema: Joi.object(),
        //When parent is shut down it must shut down all children as well
        testCallback: async ({ context, server }) => {

            const { NotificationSettings } = server.models();
            const parent = await NotificationSettings.query().findById(context.obj.id);
            expect(parent.value).to.be.equal(false);
            const children = await NotificationSettings.query().where('parent_id', parent.id);
            for (const child of children) {
                expect(child.value).to.be.equal(false);
            }
        }
    });

    test({
        testName: '[success 200] admin update parent notification settings to true',
        expectedStatusCode: 200,
        setupCallback: async ({ server, context }) => {

            const { User, NotificationSettings } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const setting =  await NotificationSettings.query().whereNull('parent_id').first();

            context.obj = setting;
            const status = true;

            return {
                method: 'POST',
                url: `/v1/notificationsettings/${setting.id}/update`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    status
                }
            };
        },
        dataSchema: Joi.object(),
        //When parent is turned on it must turned on all children asa well
        testCallback: async ({ context, server  }) => {

            const { NotificationSettings } = server.models();

            const parent = await NotificationSettings.query().findById(context.obj.id);
            expect(parent.value).to.be.equal(true);

            const children = await NotificationSettings.query().where('parent_id', parent.id);
            for (const child of children) {
                expect(child.value).to.be.equal(true);
            }
        }
    });

    test({
        testName: '[success 200] admin update child notification settings to true',
        expectedStatusCode: 200,
        setupCallback: async ({ server, context }) => {

            const { User, NotificationSettings } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const setting = await NotificationSettings.query().whereNotNull('parent_id').first();

            context.obj = setting;
            const status = true;

            return {
                method: 'POST',
                url: `/v1/notificationsettings/${setting.id}/update`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    status
                }
            };
        },
        dataSchema: Joi.object(),
        //When 1 child is turned on it must turned on the parent as well
        testCallback: async ({ data, context, server }) => {

            const { NotificationSettings } = server.models();

            const child = await NotificationSettings.query().findById(context.obj.id);
            expect(child.value).to.be.equal(true);

            const parent = await NotificationSettings.query().findById(context.obj.parent_id);
            expect(parent.value).to.be.equal(true);

        }
    });

    test({
        testName: '[success 200] admin update child notification settings to false',
        expectedStatusCode: 200,
        setupCallback: async ({ server, context }) => {

            const { User, NotificationSettings } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const setting = await NotificationSettings.query().whereNotNull('parent_id').first();

            context.obj = setting;
            const status = false;

            return {
                method: 'POST',
                url: `/v1/notificationsettings/${setting.id}/update`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    status
                }
            };
        },
        dataSchema: Joi.object(),
        testCallback: async ({ data, context, server }) => {

            const { NotificationSettings } = server.models();

            const child = await NotificationSettings.query().findById(context.obj.id);
            expect(child.value).to.be.equal(false);

        }
    });

    test({
        testName: '[success 401] client update child notification settings',
        expectedStatusCode: 401,
        setupCallback: async ({ server, context }) => {

            const { User, NotificationSettings } = server.models();
            const user = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const setting = await NotificationSettings.query().whereNotNull('parent_id').first();

            context.setting = setting;
            const status = false;

            return {
                method: 'POST',
                url: `/v1/notificationsettings/${setting.id}/update`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'client' }
                    }
                },
                payload: {
                    status
                }
            };
        }

    });

    test({
        testName: '[success 401] client update parent notification settings',
        expectedStatusCode: 401,
        setupCallback: async ({ server, context }) => {

            const { User, NotificationSettings } = server.models();
            const user = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const setting = await NotificationSettings.query().whereNull('parent_id').first();

            context.setting = setting;
            const status = false;

            return {
                method: 'POST',
                url: `/v1/notificationsettings/${setting.id}/update`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'client' }
                    }
                },
                payload: {
                    status
                }
            };
        }

    });

    test({
        testName: '[success 401] photographer update child notification settings',
        expectedStatusCode: 401,
        setupCallback: async ({ server, context }) => {

            const { User, NotificationSettings } = server.models();
            const user = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const setting = NotificationSettings.query().whereNotNull('parent_id').first();

            context.setting = setting;
            const status = false;

            return {
                method: 'POST',
                url: `/v1/notificationsettings/${setting.id}/update`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'photographer' }
                    }
                },
                payload: {
                    status
                }
            };
        }

    });

    test({
        testName: '[success 401] photographer update parent notification settings',
        expectedStatusCode: 401,
        setupCallback: async ({ server, context }) => {

            const { User, NotificationSettings } = server.models();
            const user = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const setting = NotificationSettings.query().whereNull('parent_id').first();

            context.setting = setting;
            const status = false;

            return {
                method: 'POST',
                url: `/v1/notificationsettings/${setting.id}/update`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'photographer' }
                    }
                },
                payload: {
                    status
                }
            };
        }

    });
});
