'use strict';

const Joi = require('joi');
const { experimentRoute } = require('../../../lib/helpers/tester');

const { service } = require('../../../lib/validation-schema/services/responses.js');

exports.lab = experimentRoute('POST "/service"', ({ test }) => {

    test({
        testName: '[success 200] admin creates service',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Purpose } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const purpose = await Purpose.query().first();

            return {
                method: 'POST',
                url: `/v1/service`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    name: 'test',
                    purpose_id: purpose.id
                }
            };
        },
        dataSchema: Joi.object({
            service
        })
    });

    test({
        testName: '[failure 400] admin creates service with not existing purpose',
        expectedStatusCode: 400,
        setupCallback: async ({ server }) => {

            const { User, Purpose } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const purpose = await Purpose.query().orderBy('id', 'DESC').first();

            return {
                method: 'POST',
                url: `/v1/service`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    name: 'test',
                    purpose_id: purpose.id + 1
                }
            };
        }
    });

    test({
        testName: '[failure 400] admin creates service with wrong payload',
        expectedStatusCode: 400,
        setupCallback: async ({ server }) => {

            const { User, Purpose } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const purpose = await Purpose.query().first();

            return {
                method: 'POST',
                url: `/v1/service`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    name: 'test',
                    wrong_field: purpose.id
                }
            };
        }
    });

    test({
        testName: '[failure 401] client creates service',
        expectedStatusCode: 401,
        setupCallback: async ({ server }) => {

            const { User, Purpose } = server.models();
            const user = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const purpose = await Purpose.query().first();

            return {
                method: 'POST',
                url: `/v1/service`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'client' }
                    }
                },
                payload: {
                    name: 'test',
                    purpose_id: purpose.id
                }
            };
        }
    });

    test({
        testName: '[failure 401] photographer creates service',
        expectedStatusCode: 401,
        setupCallback: async ({ server }) => {

            const { User, Purpose } = server.models();
            const user = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const purpose = await Purpose.query().first();

            return {
                method: 'POST',
                url: `/v1/service`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'photographer' }
                    }
                },
                payload: {
                    name: 'test',
                    purpose_id: purpose.id
                }
            };
        }
    });
});
