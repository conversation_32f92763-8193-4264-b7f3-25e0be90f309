'use strict';

const Joi = require('joi');
const { experimentRoute } = require('../../../../lib/helpers/tester');

const { service } = require('../../../../lib/validation-schema/services/responses.js');

exports.lab = experimentRoute('POST "/service/actions/reassign"', ({ test }) => {

    test({
        testName: '[success 200] admin reassigns packages',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const fromServiceTested = await Service.query().orderBy('id', 'ASC').first();
            const toServiceTested = await Service.query().orderBy('id', 'DESC').first();

            return {
                method: 'POST',
                url: `/v1/service/actions/reassign`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    from_id: `${fromServiceTested.id }`,
                    to_id: `${toServiceTested.id }`
                }
            };
        },
        dataSchema: Joi.object({
            service
        })
    });

    test({
        testName: '[failure 400] admin reassigns packages to not existing service',
        expectedStatusCode: 400,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const fromServiceTested = await Service.query().orderBy('id', 'ASC').first();
            const toServiceTested = await Service.query().orderBy('id', 'DESC').first();

            return {
                method: 'POST',
                url: `/v1/service/actions/reassign`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    from_id: `${fromServiceTested.id}`,
                    to_id: `${toServiceTested.id + 1}`
                }
            };
        }
    });

    test({
        testName: '[failure 400] admin reassigns packages from not existing service',
        expectedStatusCode: 400,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const toServiceTested = await Service.query().orderBy('id', 'ASC').first();
            const fromServiceTested = await Service.query().orderBy('id', 'DESC').first();

            return {
                method: 'POST',
                url: `/v1/service/actions/reassign`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    from_id: `${fromServiceTested.id + 1}`,
                    to_id: `${toServiceTested.id }`
                }
            };
        }
    });

    test({
        testName: '[failure 401] client reassigns packages',
        expectedStatusCode: 401,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const user = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const fromServiceTested = await Service.query().orderBy('id', 'ASC').first();
            const toServiceTested = await Service.query().orderBy('id', 'DESC').first();

            return {
                method: 'POST',
                url: `/v1/service/actions/reassign`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'client' }
                    }
                },
                payload: {
                    from_id: `${fromServiceTested.id }`,
                    to_id: `${toServiceTested.id }`
                }
            };
        }
    });

    test({
        testName: '[failure 401] photographer reassigns packages',
        expectedStatusCode: 401,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const user = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const fromServiceTested = await Service.query().orderBy('id', 'ASC').first();
            const toServiceTested = await Service.query().orderBy('id', 'DESC').first();

            return {
                method: 'POST',
                url: `/v1/service/actions/reassign`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'photographer' }
                    }
                },
                payload: {
                    from_id: `${fromServiceTested.id }`,
                    to_id: `${toServiceTested.id }`
                }
            };
        }
    });
});
