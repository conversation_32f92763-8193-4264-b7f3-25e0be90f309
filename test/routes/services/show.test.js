'use strict';

const Joi = require('joi');
const { experimentRoute } = require('../../../lib/helpers/tester');

const { service } = require('../../../lib/validation-schema/services/responses.js');

exports.lab = experimentRoute('GET "/service/{id}"', ({ test }) => {

    test({
        testName: '[success 200] admin retrieves service',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const serviceTested = await Service.query().first();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            return {
                method: 'GET',
                url: `/v1/service/${serviceTested.id}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                }
            };
        },
        dataSchema: Joi.object({
            service
        })
    });

    test({
        testName: '[failure 404] admin retrieves not existing service',
        expectedStatusCode: 404,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const serviceTested = await Service.query().orderBy('id', 'DESC').first();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            return {
                method: 'GET',
                url: `/v1/service/${serviceTested.id + 1}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                }
            };
        }
    });

    test({
        testName: '[success 200] client retrieves service',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const serviceTested = await Service.query().first();
            const user = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();

            return {
                method: 'GET',
                url: `/v1/service/${serviceTested.id}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'client' }
                    }
                }
            };
        },
        dataSchema: Joi.object({
            service
        })
    });

    test({
        testName: '[failure 404] client retrieves not existing service',
        expectedStatusCode: 404,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const serviceTested = await Service.query().orderBy('id', 'DESC').first();
            const user = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();

            return {
                method: 'GET',
                url: `/v1/service/${serviceTested.id + 1}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'client' }
                    }
                }
            };
        }
    });

    test({
        testName: '[success 200] photographer retrieves service',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const serviceTested = await Service.query().first();
            const user = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();

            return {
                method: 'GET',
                url: `/v1/service/${serviceTested.id}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'photographer' }
                    }
                }
            };
        },
        dataSchema: Joi.object({
            service
        })
    });

    test({
        testName: '[failure 404] photographer retrieves not existing service',
        expectedStatusCode: 404,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const serviceTested = await Service.query().orderBy('id', 'DESC').first();
            const user = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();

            return {
                method: 'GET',
                url: `/v1/service/${serviceTested.id + 1}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'photographer' }
                    }
                }
            };
        }
    });
});
