'use strict';

const Joi = require('joi');
const { experimentRoute } = require('../../../lib/helpers/tester');

const { service } = require('../../../lib/validation-schema/services/responses.js');

exports.lab = experimentRoute('DELETE "/service/delete/{id}"', ({ test }) => {

    test({
        testName: '[success 200] admin deletes service',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Service, Purpose } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const purpose = await Purpose.query().first();

            const serviceWithoutPackagesTested = await Service.query().insertAndFetch({
                name: 'trial',
                purpose_id: purpose.id
            });

            return {
                method: 'DELETE',
                url: `/v1/service/delete/${serviceWithoutPackagesTested.id}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                }
            };
        },
        dataSchema: Joi.object({
            service
        })
    });

    test({
        testName: '[failure 404] admin deletes not existing service',
        expectedStatusCode: 404,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            const serviceTested = await Service.query().orderBy('id', 'DESC').first();

            return {
                method: 'DELETE',
                url: `/v1/service/delete/${serviceTested.id + 1}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                }
            };
        }
    });

    test({
        testName: '[failure 400] admin deletes service with packages',
        expectedStatusCode: 400,
        setupCallback: async ({ server }) => {

            const { User, Service, Package } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const pack = await Package.query().first();
            const serviceTested = await Service.query().findById(pack.service_id);

            return {
                method: 'DELETE',
                url: `/v1/service/delete/${serviceTested.id}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                }
            };
        }
    });

    test({
        testName: '[failure 401] client deletes service',
        expectedStatusCode: 401,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const user = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const serviceTested = await Service.query().first();

            return {
                method: 'DELETE',
                url: `/v1/service/delete/${serviceTested.id}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'client' }
                    }
                }
            };
        }
    });

    test({
        testName: '[failure 401] photographer deletes service',
        expectedStatusCode: 401,
        setupCallback: async ({ server }) => {

            const { User, Service } = server.models();
            const user = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const serviceTested = await Service.query().first();

            return {
                method: 'DELETE',
                url: `/v1/service/delete/${serviceTested.id}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'photographer' }
                    }
                }
            };
        }
    });
});
