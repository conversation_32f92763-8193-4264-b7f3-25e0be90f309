'use strict';

const Joi = require('joi');
const { experimentRoute } = require('../../../lib/helpers/tester');

const { service } = require('../../../lib/validation-schema/services/responses.js');

exports.lab = experimentRoute('POST "/service/update/{id}"', ({ test }) => {

    test({
        testName: '[success 200] admin updates service',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Purpose, Service } = server.models();
            const serviceTested = await Service.query().first();

            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const purpose = await Purpose.query().first();

            return {
                method: 'POST',
                url: `/v1/service/update/${serviceTested.id}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    name: 'test',
                    purpose_id: purpose.id
                }
            };
        },
        dataSchema: Joi.object({
            service
        })
    });

    test({
        testName: '[failure 404] admin updates not existing service',
        expectedStatusCode: 404,
        setupCallback: async ({ server }) => {

            const { User, Purpose, Service } = server.models();
            const serviceTested = await Service.query().orderBy('id', 'DESC').first();

            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const purpose = await Purpose.query().first();

            return {
                method: 'POST',
                url: `/v1/service/update/${serviceTested.id + 1}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    name: 'test',
                    purpose_id: purpose.id
                }
            };
        }
    });

    test({
        testName: '[failure 400] admin updates service with not existing purpose',
        expectedStatusCode: 400,
        setupCallback: async ({ server }) => {

            const { User, Purpose, Service } = server.models();
            const serviceTested = await Service.query().first();

            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const purpose = await Purpose.query().orderBy('id', 'DESC').first();

            return {
                method: 'POST',
                url: `/v1/service/update/${serviceTested.id}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    name: 'test',
                    purpose_id: purpose.id + 1
                }
            };
        }
    });

    test({
        testName: '[failure 401] client updates service',
        expectedStatusCode: 401,
        setupCallback: async ({ server }) => {

            const { User, Purpose, Service } = server.models();
            const serviceTested = await Service.query().first();

            const user = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const purpose = await Purpose.query().first();

            return {
                method: 'POST',
                url: `/v1/service/update/${serviceTested.id}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'client' }
                    }
                },
                payload: {
                    name: 'test',
                    purpose_id: purpose.id
                }
            };
        }
    });

    test({
        testName: '[failure 401] photographer updates service',
        expectedStatusCode: 401,
        setupCallback: async ({ server }) => {

            const { User, Purpose, Service } = server.models();
            const serviceTested = await Service.query().first();

            const user = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const purpose = await Purpose.query().first();

            return {
                method: 'POST',
                url: `/v1/service/update/${serviceTested.id}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'photographer' }
                    }
                },
                payload: {
                    name: 'test',
                    purpose_id: purpose.id
                }
            };
        }
    });
});
