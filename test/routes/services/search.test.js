'use strict';

const Joi = require('joi');
const { experimentRoute } = require('../../../lib/helpers/tester');

const { service } = require('../../../lib/validation-schema/services/responses.js');

exports.lab = experimentRoute('POST "/services/search"', ({ test }) => {

    test({
        testName: '[success 200] admin retrieves services',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User } = server.models();

            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            return {
                method: 'POST',
                url: `/v1/services/search`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    filters: {}
                }
            };
        },
        dataSchema: Joi.object({
            services: Joi.array().items(service).required()
        })
    });

    test({
        testName: '[success 200] client retrieves services',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User } = server.models();

            const user = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();

            return {
                method: 'POST',
                url: `/v1/services/search`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'client' }
                    }
                },
                payload: {
                    filters: {}
                }
            };
        },
        dataSchema: Joi.object({
            services: Joi.array().items(service).required()
        })
    });

    test({
        testName: '[success 200] photographer retrieves services',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User } = server.models();

            const user = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();

            return {
                method: 'POST',
                url: `/v1/services/search`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'photographer' }
                    }
                },
                payload: {
                    filters: {}
                }
            };
        },
        dataSchema: Joi.object({
            services: Joi.array().items(service).required()
        })
    });

    test({
        testName: '[success 200] admin retrieves services with filters',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User } = server.models();

            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            return {
                method: 'POST',
                url: `/v1/services/search`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    filters: {
                        purposes: [1, 2]
                    }
                }
            };
        },
        dataSchema: Joi.object({
            services: Joi.array().items(service).required()
        })
    });

    test({
        testName: '[success 200] client retrieves services with filters',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User } = server.models();

            const user = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();

            return {
                method: 'POST',
                url: `/v1/services/search`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'clients' }
                    }
                },
                payload: {
                    filters: {
                        purposes: [1, 2]
                    }
                }
            };
        },
        dataSchema: Joi.object({
            services: Joi.array().items(service).required()
        })
    });

    test({
        testName: '[success 200] photographer retrieves services with filters',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User } = server.models();

            const user = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();

            return {
                method: 'POST',
                url: `/v1/services/search`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'photographer' }
                    }
                },
                payload: {
                    filters: {
                        purposes: [1, 2]
                    }
                }
            };
        },
        dataSchema: Joi.object({
            services: Joi.array().items(service).required()
        })
    });

    test({
        testName: '[failure 400] admin retrieves services with wrong filters',
        expectedStatusCode: 400,
        setupCallback: async ({ server }) => {

            const { User } = server.models();

            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            return {
                method: 'POST',
                url: `/v1/services/search`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    filters: {
                        packages: [1, 2]
                    }
                }
            };
        }
    });

    test({
        testName: '[failure 400] client retrieves services with wrong filters',
        expectedStatusCode: 400,
        setupCallback: async ({ server }) => {

            const { User } = server.models();

            const user = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();

            return {
                method: 'POST',
                url: `/v1/services/search`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'client' }
                    }
                },
                payload: {
                    filters: {
                        packages: [1, 2]
                    }
                }
            };
        }
    });

    test({
        testName: '[failure 400] client retrieves services with wrong filters',
        expectedStatusCode: 400,
        setupCallback: async ({ server }) => {

            const { User } = server.models();

            const user = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();

            return {
                method: 'POST',
                url: `/v1/services/search`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'photographer' }
                    }
                },
                payload: {
                    filters: {
                        packages: [1, 2]
                    }
                }
            };
        }
    });
});
