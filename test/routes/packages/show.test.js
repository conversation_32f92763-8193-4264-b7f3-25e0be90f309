'use strict';

const Joi = require('joi');
const { experimentRoute } = require('../../../lib/helpers/tester');

const { pack } = require('../../../lib/validation-schema/packages/responses.js');

exports.lab = experimentRoute('GET "/package/{id}"', ({ test }) => {

    test({
        testName: '[success 200] admin retrieves package',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Package } = server.models();
            const packageTested = await Package.query().whereNot('status', 'hidden').first();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            return {
                method: 'GET',
                url: `/v1/package/${packageTested.id}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                }
            };
        },
        dataSchema: Joi.object({
            pack
        })
    });

    test({
        testName: '[success 200] admin retrieves hidden package',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Package } = server.models();
            const packageTested = await Package.query().where('status', 'hidden').first();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            return {
                method: 'GET',
                url: `/v1/package/${packageTested.id}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                }
            };
        },
        dataSchema: Joi.object({
            pack
        })
    });

    test({
        testName: '[failure 404] admin retrieves not existing package',
        expectedStatusCode: 404,
        setupCallback: async ({ server }) => {

            const { User, Package } = server.models();
            const packageTested = await Package.query().orderBy('id', 'DESC').first();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            return {
                method: 'GET',
                url: `/v1/package/${packageTested.id + 1}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                }
            };
        }
    });

    test({
        testName: '[success 200] client retrieves package',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Package } = server.models();
            const packageTested = await Package.query().whereNot('status', 'hidden').first();
            const user = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();

            return {
                method: 'GET',
                url: `/v1/package/${packageTested.id}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'client' }
                    }
                }
            };
        },
        dataSchema: Joi.object({
            pack
        })
    });

    test({
        testName: '[failure 404] client retrieves hidden package',
        expectedStatusCode: 404,
        setupCallback: async ({ server }) => {

            const { User, Package } = server.models();
            const packageTested = await Package.query().where('status', 'hidden').first();
            const user = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();

            return {
                method: 'GET',
                url: `/v1/package/${packageTested.id}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'client' }
                    }
                }
            };
        }
    });

    test({
        testName: '[failure 404] client retrieves not existing package',
        expectedStatusCode: 404,
        setupCallback: async ({ server }) => {

            const { User, Package } = server.models();
            const packageTested = await Package.query().orderBy('id', 'DESC').first();
            const user = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();

            return {
                method: 'GET',
                url: `/v1/package/${packageTested.id + 1}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'client' }
                    }
                }
            };
        }
    });

    test({
        testName: '[success 200] photographer retrieves package',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User, Package } = server.models();
            const packageTested = await Package.query().whereNot('status', 'hidden').first();
            const user = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();

            return {
                method: 'GET',
                url: `/v1/package/${packageTested.id}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'photographer' }
                    }
                }
            };
        },
        dataSchema: Joi.object({
            pack
        })
    });

    test({
        testName: '[failure 404] photographer retrieves hidden package',
        expectedStatusCode: 404,
        setupCallback: async ({ server }) => {

            const { User, Package } = server.models();
            const packageTested = await Package.query().where('status', 'hidden').first();
            const user = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();

            return {
                method: 'GET',
                url: `/v1/package/${packageTested.id}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'photographer' }
                    }
                }
            };
        }
    });

    test({
        testName: '[failure 404] photographer retrieves not existing package',
        expectedStatusCode: 404,
        setupCallback: async ({ server }) => {

            const { User, Package } = server.models();
            const packageTested = await Package.query().orderBy('id', 'DESC').first();
            const user = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();

            return {
                method: 'GET',
                url: `/v1/package/${packageTested.id + 1}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'photographer' }
                    }
                }
            };
        }
    });
});
