'use strict';

const Joi = require('joi');
const { experimentRoute } = require('../../../lib/helpers/tester');

const { pack } = require('../../../lib/validation-schema/packages/responses.js');

exports.lab = experimentRoute('POST "/packages/search"', ({ test }) => {

    test({
        testName: '[success 200] admin retrieves packages',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User } = server.models();

            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            return {
                method: 'POST',
                url: `/v1/packages/search`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    filters: {}
                }
            };
        },
        dataSchema: Joi.object({
            packages: Joi.array().items(pack).required()
        })
    });

    test({
        testName: '[success 200] client retrieves packages',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User } = server.models();

            const user = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();

            return {
                method: 'POST',
                url: `/v1/packages/search`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'client' }
                    }
                },
                payload: {
                    filters: {}
                }
            };
        },
        dataSchema: Joi.object({
            packages: Joi.array().items(pack).required()
        })
    });

    test({
        testName: '[success 200] photographer retrieves packages',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User } = server.models();

            const user = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();

            return {
                method: 'POST',
                url: `/v1/packages/search`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'photographer' }
                    }
                },
                payload: {
                    filters: {}
                }
            };
        },
        dataSchema: Joi.object({
            packages: Joi.array().items(pack).required()
        })
    });

    test({
        testName: '[success 200] admin retrieves packages with filters',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User } = server.models();

            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            return {
                method: 'POST',
                url: `/v1/packages/search`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    filters: {
                        services: [1, 2]
                    }
                }
            };
        },
        dataSchema: Joi.object({
            packages: Joi.array().items(pack).required()
        })
    });

    test({
        testName: '[success 200] client retrieves packages with filters',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User } = server.models();

            const user = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();

            return {
                method: 'POST',
                url: `/v1/packages/search`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'client' }
                    }
                },
                payload: {
                    filters: {
                        services: [1, 2]
                    }
                }
            };
        },
        dataSchema: Joi.object({
            packages: Joi.array().items(pack).required()
        })
    });

    test({
        testName: '[success 200] photographer retrieves packages with filters',
        expectedStatusCode: 200,
        setupCallback: async ({ server }) => {

            const { User } = server.models();

            const user = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();

            return {
                method: 'POST',
                url: `/v1/packages/search`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'photographer' }
                    }
                },
                payload: {
                    filters: {
                        services: [1, 2]
                    }
                }
            };
        },
        dataSchema: Joi.object({
            packages: Joi.array().items(pack).required()
        })
    });

    test({
        testName: '[failure 400] admin retrieves packages with wrong filters',
        expectedStatusCode: 400,
        setupCallback: async ({ server }) => {

            const { User } = server.models();

            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();

            return {
                method: 'POST',
                url: `/v1/packages/search`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                },
                payload: {
                    filters: {
                        purposes: [1, 2]
                    }
                }
            };
        }
    });

    test({
        testName: '[failure 400] client retrieves packages with wrong filters',
        expectedStatusCode: 400,
        setupCallback: async ({ server }) => {

            const { User } = server.models();

            const user = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();

            return {
                method: 'POST',
                url: `/v1/packages/search`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'client' }
                    }
                },
                payload: {
                    filters: {
                        purposes: [1, 2]
                    }
                }
            };
        }
    });

    test({
        testName: '[failure 400] photographer retrieves packages with wrong filters',
        expectedStatusCode: 400,
        setupCallback: async ({ server }) => {

            const { User } = server.models();

            const user = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();

            return {
                method: 'POST',
                url: `/v1/packages/search`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'photographer' }
                    }
                },
                payload: {
                    filters: {
                        purposes: [1, 2]
                    }
                }
            };
        }
    });
});
