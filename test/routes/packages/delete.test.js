'use strict';

// const Joi = require('joi');
const { experimentRoute } = require('../../../lib/helpers/tester');

// const { pack } = require('../../../lib/validation-schema/packages/responses.js');

exports.lab = experimentRoute('DELETE "/package/delete/{id}"', ({ test }) => {

    // test({
    //     testName: '[success 200] admin deletes package',
    //     expectedStatusCode: 200,
    //     setupCallback: async ({ server }) => {

    //         const { User, Package } = server.models();
    //         const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
    //         const packageTested = await Package.query().first();

    //         return {
    //             method: 'DELETE',
    //             url: `/v1/package/delete/${packageTested.id}`,
    //             auth: {
    //                 credentials: {
    //                     user,
    //                     tokenPayload: { role: 'admin' }
    //                 }
    //             }
    //         };
    //     },
    //     dataSchema: Joi.object({
    //         pack
    //     })
    // });

    test({
        testName: '[failure 404] admin deletes not existing package',
        expectedStatusCode: 404,
        setupCallback: async ({ server }) => {

            const { User, Package } = server.models();
            const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
            const packageTested = await Package.query().orderBy('id', 'DESC').first();

            return {
                method: 'DELETE',
                url: `/v1/package/delete/${packageTested.id + 1}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'admin' }
                    }
                }
            };
        }
    });

    test({
        testName: '[failure 401] client deletes package',
        expectedStatusCode: 401,
        setupCallback: async ({ server }) => {

            const { User, Package } = server.models();
            const user = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
            const packageTested = await Package.query().first();

            return {
                method: 'DELETE',
                url: `/v1/package/delete/${packageTested.id}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'client' }
                    }
                }
            };
        }
    });

    test({
        testName: '[failure 401] photographer deletes package',
        expectedStatusCode: 401,
        setupCallback: async ({ server }) => {

            const { User, Package } = server.models();
            const user = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
            const packageTested = await Package.query().first();

            return {
                method: 'DELETE',
                url: `/v1/package/delete/${packageTested.id}`,
                auth: {
                    credentials: {
                        user,
                        tokenPayload: { role: 'photographer' }
                    }
                }
            };
        }
    });
});
