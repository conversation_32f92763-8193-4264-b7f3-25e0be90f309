// 'use strict';

// // const Joi = require('joi');
// const { experimentRoute } = require('../../../lib/helpers/tester');
// // const { expect } = require('@hapi/code');

// // const { pack } = require('../../../lib/validation-schema/packages/responses.js');

// exports.lab = experimentRoute('POST "/package"', ({ test }) => {

//     // test({
//     //     testName: '[success 200] admin creates visible package',
//     //     expectedStatusCode: 200,
//     //     setupCallback: async ({ server }) => {

//     //         const { User, Service } = server.models();
//     //         const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
//     //         const service = await Service.query().first();

//     //         return {
//     //             method: 'POST',
//     //             url: `/v1/package`,
//     //             auth: {
//     //                 credentials: {
//     //                     user,
//     //                     tokenPayload: { role: 'admin' }
//     //                 }
//     //             },
//     //             payload: {
//     //                 name: 'test',
//     //                 service_id: service.id,
//     //                 price: 100,
//     //                 photographer_revenue: 0,
//     //                 duration: 60,
//     //                 picture_number: 15,
//     //                 description: 'trial',
//     //                 is_plus: true,
//     //                 hide: false
//     //             }
//     //         };
//     //     },
//     //     dataSchema: Joi.object({
//     //         pack
//     //     }),
//     //     testCallback: ({ data }) => {

//     //         const savedPackage = data.pack;
//     //         expect(savedPackage.status).to.be.equal('none');
//     //     }
//     // });

//     test({
//         testName: '[failure 400] admin creates package with not existing service',
//         expectedStatusCode: 400,
//         setupCallback: async ({ server }) => {

//             const { User, Service } = server.models();
//             const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
//             const service = await Service.query().orderBy('id', 'DESC').first();

//             return {
//                 method: 'POST',
//                 url: `/v1/package`,
//                 auth: {
//                     credentials: {
//                         user,
//                         tokenPayload: { role: 'admin' }
//                     }
//                 },
//                 payload: {
//                     name: 'test',
//                     service_id: service.id + 1,
//                     price: 100,
//                     photographer_revenue: 0,
//                     duration: 60,
//                     picture_number: 15,
//                     description: 'trial',
//                     is_plus: true,
//                     hide: true
//                 }
//             };
//         }
//     });

//     // test({
//     //     testName: '[success 200] admin creates hidden package',
//     //     expectedStatusCode: 200,
//     //     setupCallback: async ({ server }) => {

//     //         const { User, Service } = server.models();
//     //         const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
//     //         const service = await Service.query().first();

//     //         return {
//     //             method: 'POST',
//     //             url: `/v1/package`,
//     //             auth: {
//     //                 credentials: {
//     //                     user,
//     //                     tokenPayload: { role: 'admin' }
//     //                 }
//     //             },
//     //             payload: {
//     //                 name: 'test',
//     //                 service_id: service.id,
//     //                 price: 100,
//     //                 photographer_revenue: 0,
//     //                 duration: 60,
//     //                 picture_number: 15,
//     //                 description: 'trial',
//     //                 is_plus: true,
//     //                 hide: true
//     //             }
//     //         };
//     //     },
//     //     dataSchema: Joi.object({
//     //         pack
//     //     }),
//     //     testCallback: ({ data }) => {

//     //         const savedPackage = data.pack;
//     //         expect(savedPackage.status).to.be.equal('hidden');
//     //     }
//     // });

//     test({
//         testName: '[failure 400] admin creates package with wrong payload',
//         expectedStatusCode: 400,
//         setupCallback: async ({ server }) => {

//             const { User, Service } = server.models();
//             const user = await User.query().whereNull('client_id').whereNull('photographer_id').first();
//             const service = await Service.query().first();

//             return {
//                 method: 'POST',
//                 url: `/v1/package`,
//                 auth: {
//                     credentials: {
//                         user,
//                         tokenPayload: { role: 'admin' }
//                     }
//                 },
//                 payload: {
//                     name: 'test',
//                     service_id: service.id,
//                     price: 100,
//                     photographer_revenue: 0,
//                     duration: 60,
//                     wrong_field: 2094,
//                     description: 'trial',
//                     is_plus: true,
//                     hide: true
//                 }
//             };
//         }
//     });

//     test({
//         testName: '[failure 401] client creates package',
//         expectedStatusCode: 401,
//         setupCallback: async ({ server }) => {

//             const { User, Service } = server.models();
//             const user = await User.query().whereNotNull('client_id').whereNull('photographer_id').first();
//             const service = await Service.query().first();

//             return {
//                 method: 'POST',
//                 url: `/v1/package`,
//                 auth: {
//                     credentials: {
//                         user,
//                         tokenPayload: { role: 'client' }
//                     }
//                 },
//                 payload: {
//                     name: 'test',
//                     service_id: service.id,
//                     price: 100,
//                     photographer_revenue: 0,
//                     duration: 60,
//                     picture_number: 15,
//                     description: 'trial',
//                     is_plus: true,
//                     hide: true
//                 }
//             };
//         }
//     });

//     test({
//         testName: '[failure 401] photographer creates package',
//         expectedStatusCode: 401,
//         setupCallback: async ({ server }) => {

//             const { User, Service } = server.models();
//             const user = await User.query().whereNull('client_id').whereNotNull('photographer_id').first();
//             const service = await Service.query().first();

//             return {
//                 method: 'POST',
//                 url: `/v1/package`,
//                 auth: {
//                     credentials: {
//                         user,
//                         tokenPayload: { role: 'photographer' }
//                     }
//                 },
//                 payload: {
//                     name: 'test',
//                     service_id: service.id,
//                     price: 100,
//                     photographer_revenue: 0,
//                     duration: 60,
//                     picture_number: 15,
//                     description: 'trial',
//                     is_plus: true,
//                     hide: true
//                 }
//             };
//         }
//     });
// });
