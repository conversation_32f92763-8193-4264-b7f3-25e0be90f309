'use strict';

const Lab = require('@hapi/lab');

const lab = (exports.lab = Lab.script());
const Server = require('../../server');
const { shuffle } = require('../../lib/helpers/utils');

const Shoot = require('../../lib/models/Shoot');

const buildShootTestData = async ({ server, period, status, isAssigned = true, role, city, photographer_id }) => {

    const { Package, User } = server.models();

    const user = await User.query().whereNull('client_id').whereNull('photographer_id').whereNull('editor_id').first();
    const { shootService, packageService } = server.services();

    const photographer = await User.query().whereNull('client_id').whereNotNull('photographer_id').whereNull('editor_id').first();
    const client = await User.query().whereNotNull('client_id').whereNull('photographer_id').whereNull('editor_id').first();

    let packages = await Package.query().distinctOn('service_id').select('id as package_id', 'service_id', 'is_plus');
    packages = shuffle(packages);
    const { formattedPackages, totalFixedPrice, totalFixedRevenue } = await packageService.getFormattedPackages({ packages: packages.slice(-2) });
    let time;
    const { when, hours } = period;
    if (when === 'future') {
        time = {
            from: new Date(Date.now() + hours * (60 * 60 * 1000))
        };
    }
    else if (when === 'past') {
        time =  {
            from: new Date(Date.now() - hours * (60 * 60 * 1000))
        };
    }
    else {
        time = {};
    }

    //Shoot created with datetime set to the day after tomorrow
    const { savedShoot } = await shootService.createShoot({
        packages: formattedPackages,
        photographer_id: (isAssigned) ? photographer_id : null,
        client_id: client.client_id,
        location: {
            city,
            country: 'country',
            line1: 'line1',
            postal_code: '12345',
            state: 'state',
            formatted_address: 'formatted_address'
        },
        price: totalFixedPrice,
        photographer_revenue: totalFixedRevenue,
        time,
        duration: 50,
        notes: 'notes',
        contact_phone: 'contact_phone',
        contact_name: 'contact_name',
        is_payed: true,
        role: 'admin',
        user
    });

    let updatedShoot;
    if (status) {
        updatedShoot = await shootService.updateShoot({
            shoot: savedShoot,
            role: 'admin',
            user,
            newValues: {
                status
            }
        });
    }

    let loggedUser = user;
    if (role === 'photographer') {
        loggedUser = photographer;
    }
    else if (role === 'client') {
        loggedUser = client;
    }

    return { loggedUser, savedShoot: (updatedShoot?.id) ? updatedShoot : savedShoot, selectedPackages: packages.slice(-2), allPackages: packages };

};

const buildPhotographerTestData = async ({ server, status, city, selectedPackages, is_pro }) => {

    const { PaymentDetail, User, Photographer } = server.models();
    const { userService } = server.services();

    const savedPaymentDetail = await PaymentDetail.query().insertAndFetch({
        type: 'test',
        details: {}
    });

    const savedPhotographer = await Photographer.query()
        .insertAndFetch({
            birthdate: new Date(),
            english_level: 'B2',
            arabic_level: 'A1',
            main_location: city,
            is_pro,
            details: {},
            status: 'none',
            payment_details_id: savedPaymentDetail.id
        });

    const savedUser = await User.query().insertAndFetch({
        name: `Test photographer ${savedPhotographer.id}_algo`,
        email: `test.photographer${savedPhotographer.id}<EMAIL>`,
        phone: '1234567890',
        photographer_id: savedPhotographer.id,
        client_id: null,
        editor_id: null,
        status
    });

    userService.updateUser({
        affectedUser: {
            id: savedUser.id,
            photographer_id: savedUser.photographer_id
        },
        userToBeUpdatedRole: 'photographer',
        additionalInformations: {
            birthdate: new Date(),
            english_level: 'B2',
            arabic_level: 'A1',
            main_location: city,
            is_pro,
            details: {}
        },
        user: {
            name: `Test photographer ${savedPhotographer.id}_algo`,
            phone: '1234567890'
        },
        status,
        services: selectedPackages.map(({ service_id }) => ({
            service_id,
            rate: Math.floor(Math.random() * 10) + 1
        }))
    });
    return savedPhotographer;
};

lab.experiment('Algorithm testing', ()  => {

    lab.before( async ({ context }) => {

        const server = await Server.deployment();

        const { Photographer, PhotographerShoot } = server.models();
        context.scheduledShoot = await buildShootTestData({
            server,
            period: {
                when: 'future',
                hours: 48
            },
            status: Shoot.statuses.scheduled,
            isAssigned: false,
            role: 'admin',
            city: 'dubai'
        });
        context.notScheduledShoot = await buildShootTestData({
            server,
            period: { },
            status: Shoot.statuses.toSchedule,
            isAssigned: false,
            role: 'admin',
            city: 'dubai'
        });

        context.firstPhotographer = await buildPhotographerTestData({
            server,
            status: 'active',
            city: 'dubai',
            selectedPackages: context.scheduledShoot.selectedPackages,
            is_pro: context.scheduledShoot.selectedPackages.map(({ is_plus }) => is_plus ).every((c) => c === true)
        });

        context.secondPhotographer = await buildPhotographerTestData({
            server,
            status: 'active',
            city: 'abu dhabi',
            selectedPackages: context.scheduledShoot.selectedPackages,
            is_pro: context.scheduledShoot.selectedPackages.map(({ is_plus }) => is_plus ).every((c) => c === true)
        });

        context.notSpecializedPhotographer = await buildPhotographerTestData({
            server,
            status: 'active',
            city: 'dubai',
            selectedPackages: context.scheduledShoot.allPackages.filter((x) => !context.scheduledShoot.selectedPackages.includes(x)),
            is_pro: context.scheduledShoot.selectedPackages.map(({ is_plus }) => is_plus ).every((c) => c === true)
        });

        context.notProPhotographer = await buildPhotographerTestData({
            server,
            status: 'active',
            city: 'dubai',
            selectedPackages: context.scheduledShoot.allPackages.filter((x) => context.scheduledShoot.selectedPackages.includes(x)),
            is_pro: !context.scheduledShoot.selectedPackages.map(({ is_plus }) => is_plus ).every((c) => c === true)
        });

        context.samplePhotographer = await Photographer.query()
            .whereNotIn('id', [context.firstPhotographer.id, context.secondPhotographer.id, context.notSpecializedPhotographer.id]).first();

        context.assignedShoot = await buildShootTestData({
            server,
            period: {
                when: 'future',
                hours: 48
            },
            status: Shoot.statuses.assigned,
            isAssigned: true,
            role: 'admin',
            city: 'dubai',
            photographer_id: context.samplePhotographer.id
        });

        context.notStaleShoot = await buildShootTestData({
            server,
            period: {
                when: 'future',
                hours: 48
            },
            status: Shoot.statuses.scheduled,
            isAssigned: false,
            role: 'admin',
            city: 'dubai'
        });

        await PhotographerShoot.query().insert({
            shoot_id: context.notStaleShoot.savedShoot.id,
            photographer_id: context.samplePhotographer.id,
            creation_date: Date.now()
        });

        context.busyPhotographer = await buildPhotographerTestData({
            server,
            status: 'active',
            city: 'dubai',
            selectedPackages: context.scheduledShoot.selectedPackages,
            is_pro: context.scheduledShoot.selectedPackages.map(({ is_plus }) => is_plus ).every((c) => c === true)
        });

        context.coincidentShoot = await buildShootTestData({
            server,
            period: {
                when: 'future',
                hours: 48
            },
            status: Shoot.statuses.assigned,
            isAssigned: true,
            role: 'admin',
            city: 'dubai',
            photographer_id: context.busyPhotographer.id
        });
    });

    // lab.test('Scenario 001 + Scenario 002 - Algorithm recognize matchable shoot (scheduled)', async ({ context }) => {

    //     const server = await Server.deployment();
    //     const { algorithmService } = server.services();
    //     console.time('match');
    //     const shoots = await algorithmService.getUnprocessedShoots();
    //     console.timeEnd('match');
    //     const shootIds = shoots.map(({ id }) => id);

    //     expect(shootIds).to.contain(context.scheduledShoot.savedShoot.id);
    // });

    // lab.test('Scenario 003 - Algorithm does not process shoots in other statuses', async ({ context }) => {

    //     const server = await Server.deployment();
    //     const { algorithmService } = server.services();

    //     const shoots = await algorithmService.getUnprocessedShoots();
    //     const shootIds = shoots.map(({ id }) => id);

    //     expect(shootIds).not.contain(context.notScheduledShoot.savedShoot.id);
    // });

    // lab.test('Scenario 004 - Algorithm does not process shoots with assigned photographer', async ({ context }) => {

    //     const server = await Server.deployment();
    //     const { algorithmService } = server.services();

    //     const shoots = await algorithmService.getUnprocessedShoots();
    //     const shootIds = shoots.map(({ id }) => id);

    //     expect(shootIds).not.contain(context.assignedShoot.savedShoot.id);
    // });

    // lab.test('Scenario 006 - Algorithm does not process shoots processed within 30 minutes', async ({ context }) => {

    //     const server = await Server.deployment();
    //     const { algorithmService } = server.services();

    //     const shoots = await algorithmService.getUnprocessedShoots();
    //     const shootIds = shoots.map(({ id }) => id);

    //     expect(shootIds).not.contain(context.notStaleShoot.savedShoot.id);
    // });

    // lab.test('Scenario 007 - Algorithm does not match shoot with photographers in different cities', async ({ context }) => {

    //     const server = await Server.deployment();
    //     const { algorithmService } = server.services();
    //     //scheduledShoot is in Dubai
    //     const matchedPhotographerIds = await algorithmService.getMatchedPhotographers({ shoot: context.scheduledShoot.savedShoot });
    //     //secondPhotographer is in Abu Dhabi
    //     expect(matchedPhotographerIds).not.contain(context.secondPhotographer.id);
    // });

    // lab.test('Scenario 008 - Algorithm does not match shoot with photographers with different specializations', async ({ context }) => {

    //     const server = await Server.deployment();
    //     const { algorithmService } = server.services();
    //     //scheduledShoot is in Dubai
    //     const matchedPhotographerIds = await algorithmService.getMatchedPhotographers({ shoot: context.scheduledShoot.savedShoot });
    //     //notSpecializedPhotographer has different specialization
    //     expect(matchedPhotographerIds).not.contain(context.notSpecializedPhotographer.id);
    // });

    // lab.test('Scenario 009 - Algorithm does not match pro shoot with not pro photographers ', async ({ context }) => {

    //     const server = await Server.deployment();
    //     const { algorithmService } = server.services();
    //     //scheduledShoot is in Dubai
    //     const matchedPhotographerIds = await algorithmService.getMatchedPhotographers({ shoot: context.scheduledShoot.savedShoot });
    //     //notProPhotographer is not pro
    //     expect(matchedPhotographerIds).not.contain(context.notProPhotographer.id);
    // });

    // lab.test('Scenario 010 - Algorithm does not match shoot with busy photographers', async ({ context }) => {

    //     const server = await Server.deployment();
    //     const { algorithmService } = server.services();
    //     //scheduledShoot is in Dubai
    //     const matchedPhotographerIds = await algorithmService.getMatchedPhotographers({ shoot: context.scheduledShoot.savedShoot });
    //     //busyPhotographer is involved in coincidentShoot that overlap scheduledShoot
    //     expect(matchedPhotographerIds).not.contain(context.busyPhotographer.id);
    // });
});
