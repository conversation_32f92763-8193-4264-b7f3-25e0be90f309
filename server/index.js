'use strict';

const Glue = require('@hapi/glue');
const Manifest = require('./manifest');
const { sleep } = require('../lib/helpers/utils');

exports.deployment = async (start) => {

    const manifest = Manifest.get('/');

    const server = await Glue.compose(manifest, { relativeTo: __dirname });

    server.logger.info({ startServer: start }, 'Initializating server');
    await server.initialize();

    server.logger.info({ startServer: start }, 'Server initialization complete');

    if (!start) {
        return server;
    }

    await server.start();

    return server;
};

if (require.main === module) { // If in the main module (i.e., we have been called directly from the node CLI)

    const serverPromise = exports.deployment(process.env.START_SERVER === 'true');

    (async () => {

        const server = await serverPromise;

        const runnerSystem = async () => {

            const { algorithmService } = server.services();

            const handlers = [
                {
                    handlerName: 'algorithm',
                    handlerDefinition: async ({ server }) => {

                        server.logger.info(
                            { context: 'photographer matching algorithm' },
                            'Starting retrieval of shoots to be matched.');
                        const shoots = await algorithmService.getUnprocessedShoots();

                        server.logger.info(
                            { context: 'photographer matching algorithm' },
                            `The photographer matching algorithm found ${shoots.length} shoots.`);

                        for (const shoot of shoots) {
                            try {
                                server.logger
                                    .info(
                                        { context: 'photographer matching algorithm' },
                                        `The photographer matching algorithm start to search photographers for shoot with id: ${shoot.id}.`);
                                const matchedPhotographerIds = await algorithmService.getMatchedPhotographers({ shoot });
                                server.logger.info(
                                    { context: 'photographer matching algorithm' },
                                    `The photographer matching algorithm found ${matchedPhotographerIds.length} photographers.`);

                                if (matchedPhotographerIds.length !== 0) {
                                    server.logger
                                        .info(
                                            { context: 'photographer matching algorithm' },
                                            `The photographer matching algorithm start to match photographers for shoot with id: ${shoot.id}.`);

                                    const matches = await algorithmService.addMatchedPhotographersToShoot({
                                        matchedPhotographerIds,
                                        shoot_id: shoot.id
                                    });

                                    server.logger
                                        .info({ context: 'photographer matching algorithm' },
                                            `The photographer matching algorithm matched ${matches.length} photographers.`);

                                    await algorithmService.notifyMatchedPhotographers({ matches });
                                }
                            }
                            catch (error) {

                                server.logger
                                    .error({
                                        error: error.message,
                                        context: 'photographer matching algorithm' },
                                    `The photographer matching algorithm fail on shoot with id: ${shoot.id}`);
                            }

                        }
                    }
                },
                {
                    handlerName: 'report_collector',
                    handlerDefinition: async ({ server }) => {

                        const { shootService, reportService, reportShootService } = server.services();
                        const { statuses } = require('../lib/models/Shoot');

                        const logContext = {
                            context: 'Handler.report_collector'
                        };

                        server.logger.info(
                            { context: 'report collector data' },
                            'Starting collect data.');

                        //crete report if dosn't exist. Else return the already existing
                        const currentDayReport = await reportService.getExistingOrCreateReport();

                        const toDay = new Date();
                        const todayReports = await reportShootService.getStatForTimeRange({ time: { from: toDay, to: toDay }, deltaCalculus: false });
                        //if is the first time for this day it will create stats for current day

                        if (Object.keys(todayReports).length === 0) {
                            const totStat = {};
                            const totPictures = {};

                            for ( const [key, value] of Object.entries(statuses)) {
                                const { count, picture_count } = await shootService.getCountShootsAndPictureCountForStatus({ status: value });
                                totStat[key] = count;
                                totPictures[key] = (picture_count || 0);
                            }

                            totStat.shoots = Object.values(totStat).reduce((a, b) => a + b);
                            totStat.pictures = Object.values(totPictures).reduce((a, b) => a + b);
                            reportShootService.createReportShoot( { id: currentDayReport.id, totStat } )
                                .catch((err) => server.logger.error({ ...logContext, err }, 'Unable to create daily stats.'));
                        }

                    }
                },
                {
                    handlerName: 'mailSender',
                    handlerDefinition: async ({ server }) => {

                        const { notificationQueueService, notificationService } = server.services();

                        server.logger.info(
                            { context: 'mail sender' },
                            'Start send enqueue email.');

                        const emails = await notificationQueueService.getAllNotificationsBeforeTime( { time: new Date() });

                        const emailsIdToDelete = [];

                        for ( const email of emails) {
                            const { id, type, target, metaData } = email;
                            const result = await notificationService.sendNotification({ type, target, metaData });

                            if ( result ) {
                                emailsIdToDelete.push(id);
                            }
                        }

                        await notificationQueueService.deleteQueuedNotificationsById( { ids: emailsIdToDelete });
                    }
                }
            ];
            const { runner_interval } = server.settings.app.runner.algorithm;

            // eslint-disable-next-line no-constant-condition
            while (true) {

                for (const { handlerName, handlerDefinition } of handlers) {
                    if (handlerName === 'algorithm') {
                        continue;
                    }

                    server.logger.info({ handlerName }, 'handler');

                    await handlerDefinition({ server })
                        .catch((err) => server.logger.error({ error: err.message }, `Handler ${handlerName} had errors`));

                }

                await sleep(runner_interval * 1000);
            }
        };

        if (server.settings.app.runner.start_runner_system) {
            runnerSystem();
        }

    })();

    process.on('unhandledRejection', (err) => {

        throw err;
    });
}
