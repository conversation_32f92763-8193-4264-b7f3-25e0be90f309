'use strict';

const Confidence = require('confidence');
const Toys = require('toys');

// Glue manifest as a confidence store
module.exports = new Confidence.Store({
    server: {
        routes: {
            cors: true
        },
        port: {
            $env: 'PORT',
            $coerce: 'number',
            $default: 8080
        },
        host: {
            $env: 'HOST',
            $default: '0.0.0.0'
        },
        debug: false,
        app: {
            migrateOnStart: {
                $env: 'MIGRATE_ON_START',
                $coerce: 'boolean'
            },
            publicHostname: {
                $env: 'PUBLIC_HOSTNAME',
                $default: 'localhost'
            },
            cookies: {
                options: {
                    encoding: 'iron',
                    path: '/',
                    password: {
                        $env: 'COOKIES_PASSWORD'
                    },
                    isSecure: {
                        $env: 'COOKIES_SECURE',
                        $coerce: 'boolean',
                        $default: true
                    },
                    isHttpOnly: {
                        $env: 'COOKIES_HTTP_ONLY',
                        $coerce: 'boolean',
                        $default: true
                    },
                    ttl: {
                        $env: 'COOKIES_TTL',
                        $coerce: 'number',
                        $default: 3600000 // 1h in ms
                    },
                    ignoreErrors: true,
                    clearInvalid: true
                },
                login: 'login',
                redirectUri: 'redirect-cookie'
            },
            auth: {
                jwt: {
                    secret: {
                        $env: 'AUTH_JWT_SECRET'
                    },
                    algorithm: {
                        $env: 'AUTH_JWT_ALGORITHM',
                        $default: 'hs256'
                    },
                    issuer: {
                        $env: 'AUTH_JWT_ISSUER',
                        $default: 'backend'
                    },
                    exptime: {
                        $env: 'AUTH_JWT_EXPTIME',
                        $coerce: 'number',
                        $default: 21600000 // 6h in ms
                    }
                },
                apiKeys: {
                    admin: {
                        $env: 'AUTH_ADMIN_API_KEYS',
                        $default: [],
                        $coerce: 'array'
                    }
                },
                auth0: {
                    no_verified_phones: {
                        $env: 'AUTH_NO_VERIFIED_PHONES',
                        $default: ['+84355527245'],
                        $coerce: 'array'
                    },
                    original_domain: {
                        $env: 'AUTH_PROVIDER_ORIGINAL_DOMAIN',
                        $default: 'backend.dev'
                    },
                    connection_id: {
                        $env: 'AUTH_PROVIDER_CONNECTION_ID',
                        $default: 'Username-Password-Authentication'
                    }
                },
                provider: {
                    name: {
                        $env: 'AUTH_PROVIDER_NAME',
                        $default: 'auth0'
                    },
                    client_id: {
                        $env: 'AUTH_PROVIDER_CLIENT_ID'
                    },
                    client_secret: {
                        $env: 'AUTH_PROVIDER_CLIENT_SECRET'
                    },
                    config: {
                        domain: {
                            $env: 'AUTH_PROVIDER_CONFIG_DOMAIN',
                            $default: 'backend.dev'
                        }
                    }
                },
                forceHttps: {
                    $env: 'AUTH_FORCE_HTTPS',
                    $coerce: 'boolean',
                    $default: true
                },
                allowedCallbackDomains: {
                    $env: 'AUTH_CALLBACK_DOMAINS',
                    $coerce: 'array'
                }
            },

            amazon: {
                admin_techs_email_receiver: {
                    $env: 'ADMIN_TECHS_EMAIL_RECEIVER'
                },
                admin_operation_email_receiver: {
                    $env: 'ADMIN_OPERATION_EMAIL_RECEIVER'
                },
                admin_sales_email_receiver: {
                    $env: 'ADMIN_SALES_EMAIL_RECEIVER'
                },
                default_email_sender: {
                    $env: 'DEFAULT_EMAIL_SENDER'
                },
                aws_id: {
                    $env: 'AWS_ACCESS_KEY_ID'
                },
                aws_secret: {
                    $env: 'AWS_SECRET_ACCESS_KEY'
                },
                sqs_download_queue_url: {
                    $env: 'SQS_DOWNLOAD_QUEUE_URL'
                },
                enrichment_sqs_queue_url: {
                    $env: 'SQS_ENRICHMENT_QUEUE_URL'
                },
                content_processing_queue_url: {
                    $env: 'CONTENT_PROCESSING_QUEUE_URL'
                },
                raw_photos_bucket_name: {
                    $env: 'RAW_PHOTOS_BUCKET_NAME'
                },
                watermarks_bucket_name: {
                    $env: 'WATERMARKS_BUCKET_NAME'
                },
                processed_photos_bucket_name: {
                    $env: 'PROCESSED_PHOTOS_BUCKET_NAME'
                },
                assets_bucket_name: {
                    $env: 'ASSETS_BUCKET_NAME'
                },
                briefs_bucket_name: {
                    $env: 'BRIEFS_BUCKET_NAME'
                },
                packages_pictures_bucket_name: {
                    $env: 'PACKAGES_PICTURES_BUCKET_NAME'
                },
                aws_region: {
                    $env: 'AWS_REGION'
                },
                max_upload_brief_size_mb: {
                    $env: 'MAX_UPLOAD_BRIEF_SIZE_MB',
                    $coerce: 'number',
                    $default: 70
                },
                max_upload_raw_size_mb: {
                    $env: 'MAX_UPLOAD_RAW_SIZE_MB',
                    $coerce: 'number',
                    $default: 5120
                },
                max_upload_processed_size_mb: {
                    $env: 'MAX_UPLOAD_PROCESSED_SIZE_MB',
                    $coerce: 'number',
                    $default: 5120
                },
                cloudfront_key_pair_id: {
                    $env: 'CLOUDFRONT_KEY_PAIR_ID'
                },
                cloudfront_private_key: {
                    $env: 'CLOUDFRONT_PRIVATE_KEY'
                },
                cloudfront_url: {
                    $env: 'CLOUDFRONT_URL'
                }
            },

            runner: {
                start_runner_system: {
                    $env: 'START_RUNNER_SYSTEM',
                    $coerce: 'boolean'
                },
                algorithm: {
                    runner_interval: {
                        $env: 'RUNNER_INTERVAL',
                        $coerce: 'number'
                    },
                    matchedPhotographersLimit: {
                        $env: 'MATCHED_PHOTOGRAPHERS_LIMIT',
                        $coerce: 'number'
                    },
                    stale_matched_photographer_interval_in_minutes: {
                        $env: 'STALE_MATCHED_PHOTOGRAPHER_INTERVAL_IN_MINUTES',
                        $coerce: 'number'
                    },
                    max_number_shoot_processed: {
                        $env: 'MAX_NUMBER_SHOOT_PROCESSED',
                        $coerce: 'number'
                    }
                }
            },
            stripe: {
                stripe_secret: {
                    $env: 'STRIPE_SECRET_ACCESS_KEY'
                },
                stripe_whsec: {
                    $env: 'STRIPE_WHSEC'
                },
                stripe_public: {
                    $env: 'STRIPE_PUBLIC_ACCESS_KEY'
                },
                stripe_tax_id: {
                    $env: 'STRIPE_TAX_ID',
                    $default: 'txr_1OPmlQE4MlwVJK0Q7t21H7WL'
                }
            },
            openai: {
                api_key: {
                    $env: 'OPENAI_API_KEY'
                }
            },
            replicate: {
                api_key: {
                    $env: 'REPLICATE_API_TOKEN'
                }
            },
            pinecone: {
                api_key: {
                    $env: 'PINECONE_API_KEY'
                }
            },
            facebook: {
                client_id: {
                    $env: 'FACEBOOK_CLIENT_ID'
                },
                client_secret: {
                    $env: 'FACEBOOK_CLIENT_SECRET'
                }
            },
            log: {
                level: {
                    $env: 'LOG_LEVEL',
                    $default: 'info'
                },
                prettyPrint: {
                    $env: 'LOG_PRETTY_PRINT',
                    $coerce: 'boolean',
                    $default: false
                },
                stream: {
                    $env: 'LOG_STREAM',
                    $default: process.env.NODE_ENV === 'test' ? `logs/test.log` : null // NOTE: null makes the logger default to stdout
                }
            },
            uploads: {
                pictures: {
                    max_per_resource: {
                        $env: 'MAX_PICTURES_PER_RESOURCE',
                        $default: 20,
                        $coerce: 'number'
                    },
                    allowed_extensions: {
                        $env: 'UPLOAD_PICTURES_ALLOWED_EXTENSIONS',
                        $default: ['.jpeg', '.jpg', '.png'],
                        $coerce: 'array'
                    }
                }
            },
            photographers: {
                photographer_rest_between_shoots_in_minutes: {
                    $env: 'PHOTOGRAPHER_REST_BETWEEN_SHOOTS_IN_MINUTES',
                    $coerce: 'number'
                }
            },
            notifications: {
                shoots: {
                    beforeShootRemindersHours: {
                        $env: 'BEFORE_SHOOT_REMINDERS_HOURS',
                        $default: [48, 24, 12, 6, 1],
                        $coerce: 'array'
                    },
                    photosUploadRemindersHours: {
                        $env: 'PHOTOS_UPLOAD_REMINDERS_HOURS',
                        $default: [1],
                        $coerce: 'array'
                    },
                    clientRateReminderHour: {
                        $env: 'CLIENT_RATE_REMINDER_HOUR',
                        $default: 72,
                        $coerce: 'array'
                    },
                    photographerRateReminderHour: {
                        $env: 'PHOTOGRAPHER_RATE_REMINDER_HOUR',
                        $default: 24,
                        $coerce: 'array'
                    }
                }
            },
            monday: {
                api_key: {
                    $env: 'MONDAY_API_KEY'
                },
                endpoint: {
                    $env: 'MONDAY_ENDPOINT'
                },
                bespoke_board_id: {
                    $env: 'MONDAY_BESPOKE_BOARD_ID'
                },
                flashyshoots_board_id: {
                    $env: 'MONDAY_FLASHYSHOOTS_BOARD_ID'
                },
                clients_board_id: {
                    $env: 'MONDAY_CLIENTS_BOARD_ID'
                },
                groups_id: {
                    $env: 'MONDAY_GROUPS_ID',
                    $coerce: 'object'
                },
                clients_groups_id: {
                    $env: 'MONDAY_CLIENTS_GROUPS_ID',
                    $coerce: 'object'
                },

                columns_id: {
                    $env: 'MONDAY_COLUMNS_ID',
                    $coerce: 'object'
                },
                clients_columns_id: {
                    $env: 'MONDAY_CLIENTS_COLUMNS_ID',
                    $coerce: 'object'
                }
            }
        }
    },
    register: {
        plugins: [
            {
                plugin: '../lib' // Main plugin
            },
            {
                plugin: {
                    $filter: { $env: 'NODE_ENV' },
                    $default: 'hpal-debug',
                    production: Toys.noop
                }
            }

        ]
    }
});
