#!/bin/bash

# Stop and remove existing container and volume
docker stop flashy_database || true
docker rm flashy_database || true
docker volume rm flashy-backend_database16-data || true

cd docker
docker-compose --env-file .env.local --env-file .env up -d database
cd ..

# Wait for database to be ready
echo "Waiting for database to be ready..."
sleep 10

# Copy the dump file into the container
docker cp ./docker/dbdump/ph16.dump flashy_database:/tmp/ph16.dump

# Restore the database
docker exec -t flashy_database pg_restore -U developerflashy -d flashy --clean --no-owner --no-privileges -v /tmp/ph16.dump

echo "Database restore completed"