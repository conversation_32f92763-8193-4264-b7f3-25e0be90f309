FROM node:18.19.0 AS base

LABEL maintainer="<PERSON><PERSON> <<EMAIL>>"

WORKDIR /home/<USER>/workdir
RUN npm install -g nodemon
RUN npm install -g npm@10.9.0
RUN chown node:node /home/<USER>/workdir

COPY docker/run.sh /usr/local/bin/run.sh
COPY docker/wait-for-it.sh /usr/local/bin/wait-for-it.sh

FROM base as local_dev

CMD ["run.sh", "development"]

FROM base as release
COPY --chown=node:node ./ /home/<USER>/workdir
RUN rm -rf /home/<USER>/workdir/docker

RUN npm ci --legacy-peer-deps
CMD ["run.sh", "production"]

FROM release as test

ARG DB_SERVICE
ARG DB_USER
ARG DB_PASSWORD
ARG DB_NAME
RUN npx knex migrate:latest && npx knex seed:run && npm run test
# Changed
