version: "3.7"

volumes:
  database-data:
    driver: local
    name: flashy-backend_database-data
  database16-data:
    driver: local
    name: flashy-backend_database16-data

services:
  ## Backend ##############################################
  nginx:
    image: nginx:1.15.3-alpine
    ports:
      - "80:80"
      - "443:443"
    container_name: flashy_nginx
    networks:
      - flashy-network
    volumes:
      - ./nginx-default.conf:/etc/nginx/conf.d/default.conf
      - ./ssl/self-signed.crt:/etc/nginx/ssl/self-signed.crt
      - ./ssl/self-signed.key:/etc/nginx/ssl/self-signed.key
    deploy:
      resources:
        limits:
          memory: 128M
  backend:
    image: backend
    build:
      context: ../
      dockerfile: docker/Dockerfile
      target: local_dev
    environment:
      NODE_ENV: $NODE_ENV
      LOG_LEVEL: $LOG_LEVEL
      LOG_PRETTY_PRINT: "true"
      AUTH_PROVIDER_NAME: ${AUTH_PROVIDER_NAME?err}
      AUTH_PROVIDER_CLIENT_ID: ${AUTH_PROVIDER_CLIENT_ID?err}
      AUTH_PROVIDER_CLIENT_SECRET: ${AUTH_PROVIDER_CLIENT_SECRET?err}
      AUTH_PROVIDER_CONFIG_DOMAIN: ${AUTH_PROVIDER_CONFIG_DOMAIN?err}
      AUTH_PROVIDER_ORIGINAL_DOMAIN: ${AUTH_PROVIDER_ORIGINAL_DOMAIN?err}
      AUTH_PROVIDER_CONNECTION_ID: ${AUTH_PROVIDER_CONNECTION_ID?err}
      AUTH_JWT_SECRET: ${AUTH_JWT_SECRET?err}
      AUTH_ADMIN_API_KEYS: ${AUTH_ADMIN_API_KEYS}
      DB_SERVICE: flashy_database:5432
      DB_USER: ${DB_USER?err}
      DB_PASSWORD: ${DB_PASSWORD?err}
      DB_NAME: ${DB_NAME?err}
      MIGRATE_ON_START: ${MIGRATE_ON_START?err}
      DEFAULT_EMAIL_SENDER: ${DEFAULT_EMAIL_SENDER?err}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID?err}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY?err}
      CLOUDFRONT_KEY_PAIR_ID: ${CLOUDFRONT_KEY_PAIR_ID?err}
      CLOUDFRONT_PRIVATE_KEY: ${CLOUDFRONT_PRIVATE_KEY?err}
      CLOUDFRONT_URL: ${CLOUDFRONT_URL?err}
      RAW_PHOTOS_BUCKET_NAME: ${RAW_PHOTOS_BUCKET_NAME?err}
      WATERMARKS_BUCKET_NAME: ${WATERMARKS_BUCKET_NAME?err}
      MASKS_BUCKET_NAME: ${MASKS_BUCKET_NAME?err}
      PROCESSED_PHOTOS_BUCKET_NAME: ${PROCESSED_PHOTOS_BUCKET_NAME?err}
      BRIEFS_BUCKET_NAME: ${BRIEFS_BUCKET_NAME?err}
      ASSETS_BUCKET_NAME: ${ASSETS_BUCKET_NAME?err}
      PACKAGES_PICTURES_BUCKET_NAME: ${PACKAGES_PICTURES_BUCKET_NAME?err}
      AWS_REGION: ${AWS_REGION?err}
      MAX_UPLOAD_BRIEF_SIZE_MB: ${MAX_UPLOAD_BRIEF_SIZE_MB?err}
      MAX_UPLOAD_RAW_SIZE_MB: ${MAX_UPLOAD_RAW_SIZE_MB?err}
      MAX_UPLOAD_PROCESSED_SIZE_MB: ${MAX_UPLOAD_PROCESSED_SIZE_MB?err}
      MAX_UPLOAD_PACKAGE_PICTURES_SIZE_MB: ${MAX_UPLOAD_PACKAGE_PICTURES_SIZE_MB?err}
      STRIPE_SECRET_ACCESS_KEY: ${STRIPE_SECRET_ACCESS_KEY?err}
      PINECONE_API_KEY: ${PINECONE_API_KEY?err}
      OPENAI_API_KEY: ${OPENAI_API_KEY?err}
      REPLICATE_API_TOKEN: ${REPLICATE_API_TOKEN?err}
      STRIPE_WHSEC: ${STRIPE_WHSEC?err}
      STRIPE_PUBLIC_ACCESS_KEY: ${STRIPE_PUBLIC_ACCESS_KEY?err}
      START_SERVER: ${START_SERVER?err}
      START_RUNNER_SYSTEM: ${START_RUNNER_SYSTEM?err}
      RUNNER_INTERVAL: ${RUNNER_INTERVAL?err}
      MATCHED_PHOTOGRAPHERS_LIMIT: ${MATCHED_PHOTOGRAPHERS_LIMIT?err}
      MAX_NUMBER_SHOOT_PROCESSED: ${MAX_NUMBER_SHOOT_PROCESSED?err}
      STALE_MATCHED_PHOTOGRAPHER_INTERVAL_IN_MINUTES: ${STALE_MATCHED_PHOTOGRAPHER_INTERVAL_IN_MINUTES?err}
      PHOTOGRAPHER_REST_BETWEEN_SHOOTS_IN_MINUTES: ${PHOTOGRAPHER_REST_BETWEEN_SHOOTS_IN_MINUTES?err}
      ADMIN_OPERATION_EMAIL_RECEIVER: ${ADMIN_OPERATION_EMAIL_RECEIVER?err}
      ADMIN_SALES_EMAIL_RECEIVER: ${ADMIN_SALES_EMAIL_RECEIVER?err}
      ADMIN_TECHS_EMAIL_RECEIVER: ${ADMIN_TECHS_EMAIL_RECEIVER?err}
      MONDAY_API_KEY: ${MONDAY_API_KEY?err}
      MONDAY_ENDPOINT: ${MONDAY_ENDPOINT?err}
      MONDAY_BESPOKE_BOARD_ID: ${MONDAY_BESPOKE_BOARD_ID?err}
      MONDAY_FLASHYSHOOTS_BOARD_ID: ${MONDAY_FLASHYSHOOTS_BOARD_ID?err}
      MONDAY_CLIENTS_BOARD_ID: ${MONDAY_CLIENTS_BOARD_ID?err}
      MONDAY_GROUPS_ID: ${MONDAY_GROUPS_ID?err}
      MONDAY_CLIENTS_GROUPS_ID: ${MONDAY_CLIENTS_GROUPS_ID?err}
      MONDAY_COLUMNS_ID: ${MONDAY_COLUMNS_ID?err}
      MONDAY_CLIENTS_COLUMNS_ID: ${MONDAY_CLIENTS_COLUMNS_ID?err}
      SQS_DOWNLOAD_QUEUE_URL: ${SQS_DOWNLOAD_QUEUE_URL?err}
      FACEBOOK_CLIENT_ID: ${FACEBOOK_CLIENT_ID?err}
      FACEBOOK_CLIENT_SECRET: ${FACEBOOK_CLIENT_SECRET?err}
      SQS_ENRICHMENT_QUEUE_URL: ${SQS_ENRICHMENT_QUEUE_URL?err}
      STRIPE_TAX_ID: ${STRIPE_TAX_ID?err}
      CURRENT_HOST: ${CURRENT_HOST?err}
      CONTENT_PROCESSING_QUEUE_URL: ${CONTENT_PROCESSING_QUEUE_URL?err}
    volumes:
      - ../:/home/<USER>/workdir
    ports:
      - "8080"
    container_name: flashy_backend
    deploy:
      resources:
        limits:
          memory: 1280M
    networks:
      - flashy-network

  database:
    image: pgvector/pgvector:pg16
    volumes:
      - database16-data:/var/lib/postgresql/data/
    environment:
      POSTGRES_USER: ${DB_USER?err}
      POSTGRES_PASSWORD: ${DB_PASSWORD?err}
      POSTGRES_DB: ${DB_NAME?err}
    container_name: flashy_database
    ports:
      - "5432:5432"
    deploy:
      resources:
        limits:
          memory: 256M
    networks:
      - flashy-network
networks:
  flashy-network:
    external: true
