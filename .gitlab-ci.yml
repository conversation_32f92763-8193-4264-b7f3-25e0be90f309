stages:
  - test
  - build

variables:
  POSTGRES_USER: db_user
  POSTGRES_PASSWORD: db_pw_w347f56h234
  POSTGRES_DB: default_db

unit-tests:
  tags:
    - aws-ec2
  stage: test

  image:
    name: "gcr.io/kaniko-project/executor:debug"
    entrypoint: [""]

  services:
    - name: pgvector/pgvector:pg16
      alias: postgres

  variables:
    DB_SERVICE: postgres:5432
    DB_USER: $POSTGRES_USER
    DB_PASSWORD: $POSTGRES_PASSWORD
    DB_NAME: $POSTGRES_DB

  script:
    - |
      /kaniko/executor --context $CI_PROJECT_DIR --dockerfile $CI_PROJECT_DIR/docker/Dockerfile --target test --no-push \
      --build-arg=DB_SERVICE=$DB_SERVICE --build-arg=DB_USER=$DB_USER --build-arg=DB_PASSWORD=$DB_PASSWORD --build-arg=DB_NAME=$DB_NAME

  artifacts:
    expire_in: 30 days
    paths:
      - public

docker-build:
  tags:
    - aws-ec2
  stage: build

  image:
    name: "gcr.io/kaniko-project/executor:debug"
    entrypoint: [""]

  script:
    - mkdir -p /kaniko/.docker
    - echo "{\"auths\":{\"$CI_REGISTRY\":{\"username\":\"$CI_DEPLOY_USER\",\"password\":\"$CI_DEPLOY_PASSWORD\"}}}" > /kaniko/.docker/config.json
    - if [ $CI_COMMIT_TAG ]; then image_tag="${CI_COMMIT_TAG//v/}"; else image_tag="$CI_COMMIT_SHORT_SHA"; fi
    - if [ $CI_COMMIT_BRANCH ]; then image_tag_branch=$CI_COMMIT_BRANCH"-latest"; else image_tag_branch=latest; fi
    - /kaniko/executor --context $CI_PROJECT_DIR --dockerfile $CI_PROJECT_DIR/docker/Dockerfile --skip-unused-stages=true --target="release" --destination $CI_REGISTRY_IMAGE:$image_tag --destination $CI_REGISTRY_IMAGE:$image_tag_branch

  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
      when: on_success
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+(\.\d+)?/'
      when: on_success
