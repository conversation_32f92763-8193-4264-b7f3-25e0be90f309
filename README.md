# Flashy Backend

## How to run

* Install dependencies with `npm install`
* <PERSON>reate a new tenant on auth0.com (create an account if you do not have one, it will guide you through the creation of the tenant), then:
  * Go to `Applications > Applications > Create application`, select `Regular Web App` and give it a name, then hit `Create`
  * In the newly created application, go to `Settings` and take note of the `client ID`, `client Secret` and `domain` (you will need them to fill the `.env.local` file)
  * Put `http://localhost/login` in the fields `Allowed Callback URLs`, `Allowed Logout URLs`, `Allowed Web Origins` and `Allowed Origina (CORS)`
  * Go to `Applications > APIs > Auth0 Management API`, and in the `Machine to Machine Application` authorize the application you just created to perform `read:users` and `update:users` (you need to expand the dropdown to select the permissions)
* Setup the `docker/.env.local` file by copying the sample one from `docker/.env.local.example` and filling in as needed (you will need the auth0 data you got in the previous point, you will need AWS credentials too. For the rest you can fill in with any random string)
* Run `docker/docker-compose up`
* The server should be available at  [localhost](http://localhost) (you can navigate to the swagger at [localhost/documentation/index](http://localhost/documentation/index)

## How to setup for local development (on VSCode)

* Install the VSCode ESLint extension, if you haven't already
* Copy the default IDE settings with `cp .vscode/settings.json.default .vscode/settings.json`
* Configure git hooks with `git config --local core.hooksPath .githooks/`

## How to setup for local development (v1)

* Install docker and docker-compose
* In frontend project, run command `sh run.sh` to start the frontend
* Add host 
  * 127.0.0.1   app.flashy-local.com
  * 127.0.0.1   flashy-local.com
* In auth0, add `https://flashy-local.com/login` to the allowed urls and allowed origins
* Run knex migration with `docker-compose exec backend npx knex migrate:latest`
* Run knex seed with `docker-compose exec backend npx knex seed:run`