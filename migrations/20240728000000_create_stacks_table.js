'use strict';

exports.up = async (knex) => {

    await knex.schema.createTable('stacks', (table) => {

        table.increments('id').primary();
        table.string('description').nullable();
        table.integer('user_id').unsigned().notNullable().references('id').inTable('users').onDelete('CASCADE');
        table.timestamps(true, true);
    });

    await knex.schema.table('images', (table) => {

        table.integer('stack_id').unsigned().nullable().references('id').inTable('stacks').onDelete('SET NULL');
    });
};

exports.down = async (knex) => {

    await knex.schema.table('images', (table) => {

        table.dropColumn('stack_id');
    });
    await knex.schema.dropTableIfExists('stacks');
};
