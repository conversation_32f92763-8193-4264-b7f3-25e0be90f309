#!/bin/bash

export NODE_ENV=test
export LOG_LEVEL=debug
export LOG_PRETTY_PRINT=false
export AUTH_PROVIDER_NAME=auth0
export AUTH_PROVIDER_CLIENT_ID=client_id
export AUTH_PROVIDER_CLIENT_SECRET=client_secret
export AUTH_PROVIDER_CONFIG_DOMAIN=domain
export AUTH_JWT_SECRET=secret
export AUTH_ADMIN_API_KEYS=api_key
export MIGRATE_ON_START=true
export DEFAULT_EMAIL_SENDER=<EMAIL>
export AWS_ACCESS_KEY_ID=aws_access_key
export AWS_SECRET_ACCESS_KEY=aws_secret_access_key
export AWS_REGION=aws_region
export RAW_PHOTOS_BUCKET_NAME=photos_bucket
export PROCESSED_PHOTOS_BUCKET_NAME=processed_photos_bucket
export BRIEFS_BUCKET_NAME=briefs_bucket
export ASSETS_BUCKET_NAME=assets_bucket
export MAX_UPLOAD_BRIEF_SIZE_MB=100
export MAX_UPLOAD_RAW_SIZE_MB=100
export MAX_UPLOAD_PROCESSED_SIZE_MB=100
export MAX_UPLOAD_PACKAGE_PICTURES_SIZE_MB=10
export STRIPE_SECRET_ACCESS_KEY=stripe_secret
export OPENAI_API_KEY=openai_api_key
export PINECONE_API_KEY=pinecone_api_key
export MOCK_BACKEND=true
export ADMIN_OPERATION_EMAIL_RECEIVER=someemail
export ADMIN_SALES_EMAIL_RECEIVER=someemail
export STALE_MATCHED_PHOTOGRAPHER_INTERVAL_IN_MINUTES=30
export MAX_NUMBER_SHOOT_PROCESSED=10

# Create a directory named 'logs' if it doesn't already exist
mkdir -p logs

# Get the current date and time, formatted as 'Year-Month-Day-Hour-Minute-Second'
date=$(date '+%Y-%m-%d-%H-%M-%S')

# Configure lab options for running tests
lab_options="-a @hapi/code -lv -r ./test_reporters/console"

# Exclude specific paths from coverage analysis
for path in test_reporters seeds lib/migrations scripts; do
    lab_options="$lab_options --coverage-exclude $path"
done

# If specific arguments are passed, modify the lab options
if [[ "$@" == *"-r"* ]]; then
    lab_options="$lab_options -o stdout"
fi

# Add any additional arguments passed to the script
lab_options="$lab_options $@"

# Inform the user that lab tests are about to run
echo "Running lab $lab_options"

# Run the lab tests and capture the exit status
lab $lab_options test
lab_exit_status=$?

# Check the exit status of the lab tests
if [ $lab_exit_status -ne 0 ]; then
    # If the exit status is not zero, an error occurred
    echo "Error: Tests failed with status $lab_exit_status"
    exit $lab_exit_status
else
    # If the exit status is zero, the tests were successful
    echo "Tests completed successfully"
fi

# Check if a log file named 'test.log' exists in the 'logs' directory
if [[ -f logs/test.log ]]; then
    # If it exists, rename it by appending the current date and time to its name
    mv logs/test.log logs/test_$date.log
fi
